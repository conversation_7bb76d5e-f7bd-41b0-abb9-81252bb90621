import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Image, ScrollView, Modal, Dimensions } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';

interface Plant {
  id: number;
  name: string;
  history?: {
    id: string;
    type: 'watering' | 'feeding' | 'photo' | 'stage' | 'log';
    date: string;
    description: string;
    photoUrl?: string;
    stage?: string;
  }[];
}

interface PhotoItem {
  id: string;
  plantId: number;
  plantName: string;
  photoUrl: string;
  date: string;
  description: string;
}

const STORAGE_KEY = '@plants_data';

export default function GalleryScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [photos, setPhotos] = useState<PhotoItem[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<PhotoItem | null>(null);
  const [showPhotoModal, setShowPhotoModal] = useState(false);

  useFocusEffect(
    useCallback(() => {
      loadPlants();
    }, [])
  );

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        const plantsData = JSON.parse(storedPlants);
        setPlants(plantsData);
        
        // Extract all photos from all plants
        const allPhotos: PhotoItem[] = [];
        plantsData.forEach((plant: Plant) => {
          if (plant.history) {
            plant.history.forEach(item => {
              if (item.type === 'photo' && item.photoUrl) {
                allPhotos.push({
                  id: item.id,
                  plantId: plant.id,
                  plantName: plant.name,
                  photoUrl: item.photoUrl,
                  date: item.date,
                  description: item.description
                });
              }
            });
          }
        });
        
        // Sort photos by date (newest first)
        allPhotos.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        setPhotos(allPhotos);
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Photo Gallery</Text>
            <Text style={styles.heroDescription}>Your plant growth journey</Text>
          </View>
        </View>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.statsCard}>
            <View style={styles.statItem}>
              <View style={styles.statIconContainer}>
                <Ionicons name="camera" size={24} color="#4CAF50" />
              </View>
              <View>
                <Text style={styles.statValue}>{photos.length}</Text>
                <Text style={styles.statLabel}>Total Photos</Text>
              </View>
            </View>
          </View>

          <View style={styles.photoGrid}>
            {photos.map((photo) => (
              <TouchableOpacity
                key={photo.id}
                style={styles.photoItem}
                onPress={() => {
                  setSelectedPhoto(photo);
                  setShowPhotoModal(true);
                }}
              >
                <Image
                  source={{ uri: photo.photoUrl }}
                  style={styles.photoThumbnail}
                  resizeMode="cover"
                />
                <View style={styles.photoInfo}>
                  <Text style={styles.plantName}>{photo.plantName}</Text>
                  <Text style={styles.photoDate}>{formatDate(photo.date)}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
        <Footer currentScreen="gallery" plantCount={plants.length} />
      </SafeAreaView>

      <Modal
        visible={showPhotoModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowPhotoModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={() => setShowPhotoModal(false)}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
            {selectedPhoto && (
              <>
                <Image 
                  source={{ uri: selectedPhoto.photoUrl }}
                  style={styles.modalImage}
                  resizeMode="contain"
                />
                <View style={styles.modalInfo}>
                  <Text style={styles.modalPlantName}>{selectedPhoto.plantName}</Text>
                  <Text style={styles.modalDate}>{formatDate(selectedPhoto.date)}</Text>
                  <Text style={styles.modalDescription}>{selectedPhoto.description}</Text>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 100,
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  statsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  photoItem: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  photoThumbnail: {
    width: '100%',
    height: 150,
  },
  photoInfo: {
    padding: 8,
  },
  plantName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  photoDate: {
    fontSize: 12,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 8,
  },
  modalImage: {
    width: '100%',
    height: 300,
  },
  modalInfo: {
    padding: 16,
  },
  modalPlantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  modalDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  modalDescription: {
    fontSize: 14,
    color: '#333',
  },
}); 