import React, { useState, useEffect } from 'react';
import { Modal, View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Platform, Keyboard } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { generateHistoryId } from '../utils/helpers';

const NOTIFICATIONS_STORAGE_KEY = '@notifications_data';
const ENVIRONMENTS_STORAGE_KEY = '@environments_simple';

interface PlantInputModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (plantData: { name: string; environment: string; stage: string }) => void;
  availableEnvironments?: string[];
}

const PLANT_STAGES = [
  'Seedling',
  'Vegetative',
  'Clone'
];

const DEFAULT_ENVIRONMENTS = ['Indoor Tent', 'Outdoor Garden'];

// Configure notification handler
if (Platform.OS !== 'web') {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });
}

export default function PlantInputModal({ visible, onClose, onSubmit, availableEnvironments }: PlantInputModalProps) {
  const [name, setName] = useState('');
  const [environment, setEnvironment] = useState('');
  const [stage, setStage] = useState(PLANT_STAGES[0]);
  const [environments, setEnvironments] = useState<string[]>([]);

  // Load environments from storage when component mounts or becomes visible
  useEffect(() => {
    if (visible) {
      loadEnvironments();
    }
  }, [visible]);

  // Update environment when environments change
  useEffect(() => {
    if (environments.length > 0) {
      setEnvironment(environments[0]);
    }
  }, [environments]);

  // Use availableEnvironments prop if provided
  useEffect(() => {
    if (availableEnvironments && availableEnvironments.length > 0) {
      setEnvironments(availableEnvironments);
      setEnvironment(availableEnvironments[0]);
    }
  }, [availableEnvironments]);

  const loadEnvironments = async () => {
    try {
      const storedEnvironments = await AsyncStorage.getItem(ENVIRONMENTS_STORAGE_KEY);
      if (storedEnvironments) {
        const parsedEnvironments = JSON.parse(storedEnvironments);
        if (Array.isArray(parsedEnvironments) && parsedEnvironments.length > 0) {
          // Extract environment names from the environment objects
          const environmentNames = parsedEnvironments.map((env: any) => env.name);
          setEnvironments(environmentNames);
        } else {
          setEnvironments(DEFAULT_ENVIRONMENTS);
        }
      } else {
        setEnvironments(DEFAULT_ENVIRONMENTS);
      }
    } catch (error) {
      console.error('Error loading environments:', error);
      setEnvironments(DEFAULT_ENVIRONMENTS);
    }
  };

  const handleSubmit = async () => {
    if (!name.trim() || !environment || !stage) return;

    console.log('Selected stage:', stage);

    // Removed automatic watering reminder creation

    onSubmit({ name, environment, stage });
    setName('');
    setEnvironment(environments.length > 0 ? environments[0] : DEFAULT_ENVIRONMENTS[0]);
    setStage(PLANT_STAGES[0]);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Add New Plant</Text>
            <TouchableOpacity onPress={onClose} style={styles.modalCloseButton}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Plant Name</Text>
              <TextInput
                style={styles.input}
                value={name}
                onChangeText={setName}
                placeholder="Enter plant name"
                placeholderTextColor="#999"
                returnKeyType="done"
                blurOnSubmit={true}
                onSubmitEditing={() => Keyboard.dismiss()}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Environment</Text>
              <View style={styles.optionsContainer}>
                {environments.map((env) => (
                  <TouchableOpacity
                    key={env}
                    style={[
                      styles.optionButton,
                      environment === env && styles.optionButtonActive
                    ]}
                    onPress={() => setEnvironment(env)}
                  >
                    <Text style={[
                      styles.optionText,
                      environment === env && styles.optionTextActive
                    ]}>
                      {env}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={[styles.inputGroup, { marginTop: 8 }]}>
              <Text style={styles.label}>Stage</Text>
              <View style={styles.optionsContainer}>
                {PLANT_STAGES.map((s) => (
                  <TouchableOpacity
                    key={s}
                    style={[
                      styles.optionButton,
                      stage === s && styles.optionButtonActive
                    ]}
                    onPress={() => setStage(s)}
                  >
                    <Text style={[
                      styles.optionText,
                      stage === s && styles.optionTextActive
                    ]}>
                      {s}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>

          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.saveButton]}
              onPress={handleSubmit}
              disabled={!name.trim()}
            >
              <Text style={styles.saveButtonText}>Add Plant</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalCloseButton: {
    padding: 4,
  },
  formContainer: {
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#F1F8E9',
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F1F8E9',
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  optionButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  optionText: {
    color: '#2E7D32',
    fontSize: 14,
  },
  optionTextActive: {
    color: '#fff',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
}); 