import { StyleSheet, Text, View, TouchableOpacity, Platform, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useState } from 'react';

interface HeaderProps {
  showMenu?: boolean;
  title?: string;
}

export default function Header({ showMenu = true, title = 'GrowIt Plant Journal' }: HeaderProps) {
  const router = useRouter();
  const [menuVisible, setMenuVisible] = useState(false);

  const handleHomePress = () => {
    router.push('/home');
  };

  const toggleMenu = () => {
    setMenuVisible(!menuVisible);
  };

  const handleMenuOptionPress = (route: string) => {
    setMenuVisible(false);
    router.push(route);
  };

  return (
    <View style={styles.header}>
      <View style={styles.headerContent}>
        <View style={styles.titleContainer}>
          <TouchableOpacity onPress={handleHomePress}>
            <Ionicons name="leaf-outline" size={24} color="#fff" style={styles.headerIcon} />
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
        </View>
        {showMenu && (
          <View style={styles.headerRight}>
            <TouchableOpacity onPress={toggleMenu}>
              <Ionicons name="menu" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Menu Modal */}
      <Modal
        visible={menuVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={toggleMenu}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1} 
          onPress={toggleMenu}
        >
          <View style={styles.menuContainer}>
            <TouchableOpacity 
              style={styles.menuItem}
              onPress={() => handleMenuOptionPress('/home')}
            >
              <Ionicons name="home" size={20} color="#2E7D32" />
              <Text style={styles.menuItemText}>Home</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.menuItem}
              onPress={() => handleMenuOptionPress('/plants')}
            >
              <Ionicons name="leaf" size={20} color="#2E7D32" />
              <Text style={styles.menuItemText}>My Plants</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.menuItem}
              onPress={() => handleMenuOptionPress('/profile')}
            >
              <Ionicons name="person" size={20} color="#2E7D32" />
              <Text style={styles.menuItemText}>My Profile</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.menuItem}
              onPress={() => handleMenuOptionPress('/diagnose')}
            >
              <Ionicons name="camera" size={20} color="#2E7D32" />
              <Text style={styles.menuItemText}>Plant Diagnosis</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.menuItem}
              onPress={() => handleMenuOptionPress('/settings')}
            >
              <Ionicons name="settings" size={20} color="#2E7D32" />
              <Text style={styles.menuItemText}>GrowIt Settings</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    backgroundColor: '#2E7D32', // Grass green
    paddingTop: Platform.OS === 'ios' ? 6 : 16,
    paddingBottom: 12,
    paddingHorizontal: 20,
    width: '100%',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    minHeight: 36,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 2,
  },
  emoji: {
    fontSize: 24,
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '400',
    color: '#fff',
    lineHeight: 22,
  },
  headerRight: {
    marginLeft: 'auto',
  },
  headerIcon: {
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 70,
    right: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 10,
    width: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  menuItemText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
  },
}); 