# GrowIt 🌱

GrowIt is your personal plant care companion, helping you track and manage your plants with ease. Built with Expo and React Native, this app runs on iOS, Android, and web platforms.

## Features

- **Plant Management**: Keep track of all your plants in one place
- **Care Reminders**: Get notifications for watering and feeding schedules
- **Environment Monitoring**: Track growing conditions including temperature, humidity, and light intensity
- **Plant Statistics**: View insights about your plant collection
- **Dark Mode Support**: Comfortable viewing in any lighting condition
- **Multi-platform**: Works on iOS, Android, and web browsers

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npx expo start
   ```

3. Open the app in your preferred environment:
   - iOS Simulator
   - Android Emulator
   - Expo Go on your physical device
   - Web browser

## Development Requirements

- Node.js
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

## Environment Setup

The app requires the following permissions:
- Location access
- Photo library access (for plant photos)
- Storage access (Android)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

