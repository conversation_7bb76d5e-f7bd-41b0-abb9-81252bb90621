import React, { useState, useEffect, useRef, useCallback } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, Modal, Alert, Pressable, Platform, ActivityIndicator, TextInput, KeyboardAvoidingView, Keyboard, Linking } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { GEMINI_API_KEY } from '@env';
import { NativeMethods, MeasureLayoutOnSuccessCallback } from 'react-native';
import { isToday } from 'date-fns';

interface Plant {
  id: string;
  name: string;
  lastWatered: string | null;
  lastFeeding: string | null;
  environment: string;
}

interface Notification {
  id: string;
  type: 'watering' | 'feeding' | 'schedule' | 'pruning' | 'check';
  title: string;
  time: string;
  description?: string;
  plantName: string;
  frequency?: string;
  isRecurring?: boolean;
}

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: 'disease' | 'pest' | 'nutrient' | 'care';
  icon: string;
  sourceUrl?: string;
}

const STORAGE_KEY = '@plants_data';
const NOTIFICATIONS_STORAGE_KEY = '@notifications_data';

export default function DiagnoseScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState({
    totalPlants: 0,
    healthyPlants: 0,
    needsAttention: 0,
  });
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [chatMessages, setChatMessages] = useState<{ role: 'user' | 'assistant', content: string }[]>([]);
  const [userInput, setUserInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [showHealthModal, setShowHealthModal] = useState(false);
  const [unhealthyPlants, setUnhealthyPlants] = useState<{
    name: string;
    wateringIssue: boolean;
    feedingIssue: boolean;
    lastWatered: string | null;
    lastFeeding: string | null;
  }[]>([]);
  const chatScrollViewRef = useRef<ScrollView>(null);
  const capturedImageRef = useRef<View>(null);
  const mainScrollViewRef = useRef<ScrollView>(null);
  const inputRef = useRef<TextInput>(null);
  const router = useRouter();
  
  // Get parameters from the route
  const params = useLocalSearchParams<{ 
    selectedIssue?: string;
    issueTitle?: string;
    issueDescription?: string;
  }>();

  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [geminiResults, setGeminiResults] = useState<string | null>(null);
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  // Load plants and notifications from storage when component mounts
  useEffect(() => {
    loadPlants();
    loadNotifications();
    requestCameraPermission();
  }, []);

  // Update stats whenever plants or notifications change
  useEffect(() => {
    updateStats();
  }, [plants, notifications]);

  // Update analysis when params change
  useEffect(() => {
    if (params?.selectedIssue && params?.issueTitle && params?.issueDescription) {
      setAnalysis(`## ${params.issueTitle}\n\n${params.issueDescription}`);
    }
  }, [params?.selectedIssue, params?.issueTitle, params?.issueDescription]);

  // Scroll to bottom of chat when it becomes visible
  useEffect(() => {
    if (showChat && chatScrollViewRef.current) {
      setTimeout(() => {
        chatScrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [showChat]);

  // Filter results when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      setGeminiResults(null);
      return;
    }

    setIsSearching(true);
    
    // Simulate search delay
    const timer = setTimeout(() => {
      performSearch();
      searchWithGemini();
      setIsSearching(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const loadNotifications = async () => {
    try {
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      if (storedData) {
        const data = JSON.parse(storedData);
        if (Array.isArray(data)) {
          setNotifications(data);
        }
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  const updateStats = () => {
    const now = new Date();
    
    // Calculate unhealthy plants for the modal
    const unhealthyPlantsList = plants.map(plant => {
      // Check watering schedule
      const isWateredRecently = plant.lastWatered ? 
        Math.floor((now.getTime() - new Date(plant.lastWatered).getTime()) / (1000 * 60 * 60 * 24)) < 7 : 
        false;
      
      // Check feeding schedule
      const isFedRecently = plant.lastFeeding ? 
        Math.floor((now.getTime() - new Date(plant.lastFeeding).getTime()) / (1000 * 60 * 60 * 24)) < 14 : 
        false;
      
      return {
        name: plant.name,
        wateringIssue: !isWateredRecently,
        feedingIssue: !isFedRecently,
        lastWatered: plant.lastWatered,
        lastFeeding: plant.lastFeeding
      };
    }).filter(plant => plant.wateringIssue || plant.feedingIssue);
    
    setUnhealthyPlants(unhealthyPlantsList);
    
    const stats = {
      totalPlants: plants.length,
      healthyPlants: plants.filter(plant => {
        // Check watering schedule
        const isWateredRecently = plant.lastWatered ? 
          Math.floor((now.getTime() - new Date(plant.lastWatered).getTime()) / (1000 * 60 * 60 * 24)) < 7 : 
          false;
        
        // Check feeding schedule
        const isFedRecently = plant.lastFeeding ? 
          Math.floor((now.getTime() - new Date(plant.lastFeeding).getTime()) / (1000 * 60 * 60 * 24)) < 14 : 
          false;
        
        // Plant is healthy only if both conditions are met
        return isWateredRecently && isFedRecently;
      }).length,
      needsAttention: notifications.filter(notification => 
        isToday(new Date(notification.time))
      ).length,
    };
    setStats(stats);
  };

  const requestCameraPermission = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to scan plants');
      }
    }
  };

  const analyzePlantImage = async (imageUri: string) => {
    try {
      setIsAnalyzing(true);
      
      // Initialize the Gemini API
      const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      
      // Convert image to base64
      const response = await fetch(imageUri);
      const blob = await response.blob();
      const reader = new FileReader();
      
      reader.onloadend = async () => {
        const base64data = reader.result?.toString().split(',')[1];
        
        if (!base64data) {
          throw new Error('Failed to convert image to base64');
        }
        
        // Prepare the image for Gemini
        const imageData = {
          inlineData: {
            data: base64data,
            mimeType: "image/jpeg"
          }
        };
        
        // Create the prompt for plant analysis
        const prompt = `Analyze this plant image and provide a detailed health assessment. 
        Focus on:
        1. Overall plant health
        2. Any visible issues (disease, pests, nutrient deficiencies)
        3. Specific recommendations for improvement
        4. Whether the plant appears to be thriving or struggling
        
        Format your response in a clear, concise manner that a plant owner can understand.`;
        
        // Generate content with Gemini
        const result = await model.generateContent([prompt, imageData]);
        const response = await result.response;
        const text = response.text();
        
        setAnalysis(text);
        setIsAnalyzing(false);
      };
      
      reader.readAsDataURL(blob);
    } catch (error) {
      console.error('Error analyzing plant image:', error);
      setAnalysis('Sorry, there was an error analyzing your plant image. Please try again.');
      setIsAnalyzing(false);
    }
  };

  const handleScanPlant = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        alert('Sorry, we need camera permissions to make this work!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled) {
        setCapturedImage(result.assets[0].uri);
        setShowChat(false);
        setAnalysis(null);
        setIsAnalyzing(true);
        
        // Scroll down by 600 pixels after capturing the image
        setTimeout(() => {
          if (mainScrollViewRef.current) {
            mainScrollViewRef.current.scrollTo({ y: 600, animated: true });
          }
        }, 100);

        // Add a small delay to ensure the image is rendered before scrolling
        setTimeout(() => {
          if (capturedImageRef.current && mainScrollViewRef.current) {
            capturedImageRef.current.measureLayout(
              mainScrollViewRef.current as unknown as number,
              ((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
                mainScrollViewRef.current?.scrollTo({ y: pageY, animated: true });
              }) as MeasureLayoutOnSuccessCallback,
              () => console.log('Failed to measure layout')
            );
          }
        }, 100);

        await analyzePlantImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('Failed to access camera. Please try again.');
    }
  };

  const handleGetMoreHelp = () => {
    // Initialize chat with a system message about the plant image
    setChatMessages([
      { 
        role: 'assistant', 
        content: 'Hello! I can help you with more specific questions about your plant. What would you like to know?' 
      }
    ]);
    setShowChat(true);
    
    // Add a small delay to ensure the chat container is rendered before scrolling
    setTimeout(() => {
      if (chatScrollViewRef.current) {
        chatScrollViewRef.current.scrollToEnd({ animated: true });
      }
    }, 100);
  };
  
  const sendMessage = async () => {
    if (!userInput.trim() || !capturedImage) return;
    
    // Dismiss keyboard on iOS
    if (Platform.OS === 'ios') {
      Keyboard.dismiss();
      inputRef.current?.blur();
    }
    
    // Add user message to chat
    const userMessage = { role: 'user' as const, content: userInput };
    setChatMessages(prev => [...prev, userMessage]);
    setUserInput('');
    setIsSending(true);
    
    try {
      // Initialize the Gemini API
      const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      
      // Convert image to base64
      const response = await fetch(capturedImage);
      const blob = await response.blob();
      const reader = new FileReader();
      
      reader.onloadend = async () => {
        const base64data = reader.result?.toString().split(',')[1];
        
        if (!base64data) {
          throw new Error('Failed to convert image to base64');
        }
        
        // Prepare the image for Gemini
        const imageData = {
          inlineData: {
            data: base64data,
            mimeType: "image/jpeg"
          }
        };
        
        // Create the prompt for the chat
        const prompt = `I'm chatting with you about this plant image. The user has asked: "${userInput}". 
        Please provide a helpful response based on what you can see in the image. 
        If you've already analyzed this image, refer to your previous analysis.`;
        
        // Generate content with Gemini
        const result = await model.generateContent([prompt, imageData]);
        const response = await result.response;
        const text = response.text();
        
        // Add assistant response to chat
        setChatMessages(prev => [...prev, { role: 'assistant', content: text }]);
        setIsSending(false);
        
        // Scroll to the bottom of the chat
        setTimeout(() => {
          chatScrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      };
      
      reader.readAsDataURL(blob);
    } catch (error) {
      console.error('Error sending message:', error);
      setChatMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'Sorry, there was an error processing your message. Please try again.' 
      }]);
      setIsSending(false);
    }
  };

  const formatDate = (dateStr: string | null): string => {
    if (!dateStr) return 'Never';
    const date = new Date(dateStr);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const getWateringMessage = (lastWatered: string | null): string => {
    if (!lastWatered) return 'Plant has never been watered';
    try {
      const date = new Date(lastWatered);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (isNaN(diffDays)) return 'Plant has never been watered';
      if (diffDays === 0) return 'Plant was watered today';
      if (diffDays === 1) return 'Plant was watered yesterday';
      return `Plant hasn't been watered in ${diffDays} days`;
    } catch (error) {
      return 'Plant has never been watered';
    }
  };

  const getFeedingMessage = (lastFeeding: string | null): string => {
    if (!lastFeeding) return 'Plant has never been fed';
    try {
      const date = new Date(lastFeeding);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (isNaN(diffDays)) return 'Plant has never been fed';
      if (diffDays === 0) return 'Plant was fed today';
      if (diffDays === 1) return 'Plant was fed yesterday';
      return `Plant hasn't been fed in ${diffDays} days`;
    } catch (error) {
      return 'Plant has never been fed';
    }
  };

  const formatAnalysisText = useCallback((text: string) => {
    // Clean up the text by removing extra asterisks and fixing formatting
    let cleanedText = text
      .replace(/\*\*/g, '') // Remove double asterisks
      .replace(/\n\*\s/g, '\n• ') // Replace asterisk bullets with bullet points
      .replace(/\n\n/g, '\n') // Remove extra line breaks
      .replace(/^\d+\.\s*/gm, '') // Remove numbered points at the beginning of lines
      .trim();
    
    // Add extra line breaks between bullet points
    cleanedText = cleanedText.replace(/(•[^\n]+)\n(?!\n)/g, '$1\n\n');
    
    // Remove the word "Specific" from the text
    cleanedText = cleanedText.replace(/\bSpecific\b/g, '');
    
    // Extract the main issues and recommendations
    let issues = '';
    let recommendations = '';
    
    // Try to find issues section
    const issuesMatch = cleanedText.match(/Issues[^:]*:(.*?)(?=Recommendations|Status|Thriving|$)/s);
    if (issuesMatch && issuesMatch[1]) {
      issues = issuesMatch[1].trim();
    }
    
    // Try to find recommendations section
    const recommendationsMatch = cleanedText.match(/Recommendations[^:]*:(.*?)(?=Status|Thriving|$)/s);
    if (recommendationsMatch && recommendationsMatch[1]) {
      recommendations = recommendationsMatch[1].trim();
    }
    
    // If we couldn't find structured sections, try to extract a summary
    if (!issues && !recommendations) {
      // Look for sentences that might indicate problems
      const problemSentences = cleanedText.match(/[^.!?]*(?:showing|displaying|exhibiting|suffering|experiencing|having|showing signs of)[^.!?]*[.!?]/gi);
      if (problemSentences && problemSentences.length > 0) {
        issues = problemSentences.join(' ');
      }
      
      // Look for sentences that might indicate solutions
      const solutionSentences = cleanedText.match(/[^.!?]*(?:recommend|suggest|advise|try|apply|use|increase|decrease|adjust|check|ensure|provide|give|water|fertilize|prune|repot|move|place)[^.!?]*[.!?]/gi);
      if (solutionSentences && solutionSentences.length > 0) {
        recommendations = solutionSentences.join(' ');
      }
    }
    
    // If we still don't have content, use the whole text
    if (!issues && !recommendations) {
      issues = cleanedText;
    }
    
    return (
      <View style={styles.summaryContainer}>
        {issues && (
          <View style={styles.summarySection}>
            <View style={styles.summaryHeader}>
              <Ionicons name="warning" size={24} color="#333" />
              <Text style={styles.summaryHeaderText}>What's Wrong</Text>
            </View>
            <Text style={styles.summaryContentText}>
              {issues}
            </Text>
          </View>
        )}
        
        {recommendations && (
          <View style={styles.summarySection}>
            <View style={styles.summaryHeader}>
              <Ionicons name="bulb" size={24} color="#333" />
              <Text style={styles.summaryHeaderText}>How to Fix It</Text>
            </View>
            <Text style={styles.summaryContentText}>
              {recommendations}
            </Text>
          </View>
        )}
        
        <TouchableOpacity style={styles.helpButton} onPress={handleGetMoreHelp}>
          <Ionicons name="help-circle-outline" size={20} color="#fff" />
          <Text style={styles.helpButtonText}>Get More Help</Text>
        </TouchableOpacity>
      </View>
    );
  }, [handleGetMoreHelp]);

  const handleGuidePress = () => {
    // Reset all state to reload the page
    setCapturedImage(null);
    setAnalysis(null);
    setShowChat(false);
    setChatMessages([]);
    setUserInput('');
    setIsAnalyzing(false);
    setIsSending(false);
  };

  const handleSearchPress = () => {
    router.push('/search');
  };

  const handleHealthCardPress = () => {
    setShowHealthModal(true);
  };

  const performSearch = () => {
    const query = searchQuery.toLowerCase().trim();
    
    // Search across all categories
    let results: SearchResult[] = [];
    results = results.concat(searchDiseases(query));
    results = results.concat(searchPests(query));
    results = results.concat(searchNutrients(query));
    results = results.concat(searchCare(query));
    
    setSearchResults(results);
  };

  const searchDiseases = (query: string): SearchResult[] => {
    const diseases = [
      { 
        id: 'd1', 
        title: 'Root Rot', 
        description: 'Caused by overwatering and poor drainage. Symptoms include yellowing leaves, wilting, and soft brown roots.', 
        category: 'disease' as const, 
        icon: 'water',
        sourceUrl: 'https://en.wikipedia.org/wiki/Root_rot'
      },
      { 
        id: 'd2', 
        title: 'Powdery Mildew', 
        description: 'White powdery spots on leaves. Caused by high humidity and poor air circulation.', 
        category: 'disease' as const, 
        icon: 'snow',
        sourceUrl: 'https://en.wikipedia.org/wiki/Powdery_mildew'
      },
      { 
        id: 'd3', 
        title: 'Leaf Spot Disease', 
        description: 'Brown or black spots with yellow halos on leaves. Caused by fungal or bacterial infections.', 
        category: 'disease' as const, 
        icon: 'alert-circle',
        sourceUrl: 'https://en.wikipedia.org/wiki/Leaf_spot'
      },
      { 
        id: 'd4', 
        title: 'Blight', 
        description: 'Rapid wilting and browning of leaves. Often caused by fungal infections in warm, humid conditions.', 
        category: 'disease' as const, 
        icon: 'flame',
        sourceUrl: 'https://en.wikipedia.org/wiki/Blight'
      },
    ];
    
    return diseases.filter(disease => 
      disease.title.toLowerCase().includes(query) || 
      disease.description.toLowerCase().includes(query)
    );
  };

  const searchPests = (query: string): SearchResult[] => {
    const pests = [
      { 
        id: 'p1', 
        title: 'Spider Mites', 
        description: 'Tiny red or brown mites that create fine webs. Cause yellow speckling on leaves.', 
        category: 'pest' as const, 
        icon: 'bug',
        sourceUrl: 'https://en.wikipedia.org/wiki/Spider_mite'
      },
      { 
        id: 'p2', 
        title: 'Aphids', 
        description: 'Small green, black, or brown insects that cluster on new growth. Cause distorted leaves and sticky residue.', 
        category: 'pest' as const, 
        icon: 'bug',
        sourceUrl: 'https://en.wikipedia.org/wiki/Aphid'
      },
      { 
        id: 'p3', 
        title: 'Mealybugs', 
        description: 'White, cottony insects that appear in leaf joints. Cause yellowing and stunted growth.', 
        category: 'pest' as const, 
        icon: 'bug',
        sourceUrl: 'https://en.wikipedia.org/wiki/Mealybug'
      },
      { 
        id: 'p4', 
        title: 'Scale Insects', 
        description: 'Brown or tan bumps on stems and leaves. Cause yellowing and leaf drop.', 
        category: 'pest' as const, 
        icon: 'bug',
        sourceUrl: 'https://en.wikipedia.org/wiki/Scale_insect'
      },
    ];
    
    return pests.filter(pest => 
      pest.title.toLowerCase().includes(query) || 
      pest.description.toLowerCase().includes(query)
    );
  };

  const searchNutrients = (query: string): SearchResult[] => {
    const nutrients = [
      { 
        id: 'n1', 
        title: 'Nitrogen Deficiency', 
        description: 'Yellowing of older leaves starting from the tips. Stunted growth and poor overall health.', 
        category: 'nutrient' as const, 
        icon: 'leaf',
        sourceUrl: 'https://en.wikipedia.org/wiki/Nitrogen_deficiency'
      },
      { 
        id: 'n2', 
        title: 'Phosphorus Deficiency', 
        description: 'Purple or red discoloration on leaves. Poor flowering and root development.', 
        category: 'nutrient' as const, 
        icon: 'flower',
        sourceUrl: 'https://en.wikipedia.org/wiki/Phosphorus_deficiency'
      },
      { 
        id: 'n3', 
        title: 'Potassium Deficiency', 
        description: 'Yellowing and browning of leaf edges. Weak stems and poor disease resistance.', 
        category: 'nutrient' as const, 
        icon: 'nutrition',
        sourceUrl: 'https://en.wikipedia.org/wiki/Potassium_deficiency'
      },
      { 
        id: 'n4', 
        title: 'Iron Deficiency', 
        description: 'Yellowing between leaf veins while veins remain green. Common in alkaline soils.', 
        category: 'nutrient' as const, 
        icon: 'leaf',
        sourceUrl: 'https://en.wikipedia.org/wiki/Iron_deficiency'
      },
    ];
    
    return nutrients.filter(nutrient => 
      nutrient.title.toLowerCase().includes(query) || 
      nutrient.description.toLowerCase().includes(query)
    );
  };

  const searchCare = (query: string): SearchResult[] => {
    const care = [
      { 
        id: 'c1', 
        title: 'Overwatering', 
        description: 'Too much water leads to root rot. Allow soil to dry between waterings and ensure proper drainage.', 
        category: 'care' as const, 
        icon: 'water',
        sourceUrl: 'https://en.wikipedia.org/wiki/Watering'
      },
      { 
        id: 'c2', 
        title: 'Underwatering', 
        description: 'Not enough water causes wilting and brown leaf edges. Water thoroughly when soil is dry to the touch.', 
        category: 'care' as const, 
        icon: 'water',
        sourceUrl: 'https://en.wikipedia.org/wiki/Watering'
      },
      { 
        id: 'c3', 
        title: 'Low Light', 
        description: 'Insufficient light causes leggy growth and pale leaves. Move plant to a brighter location or provide supplemental lighting.', 
        category: 'care' as const, 
        icon: 'sunny',
        sourceUrl: 'https://en.wikipedia.org/wiki/Plant_lighting'
      },
      { 
        id: 'c4', 
        title: 'Too Much Light', 
        description: 'Excessive light causes leaf scorching and brown spots. Move plant to a location with filtered or indirect light.', 
        category: 'care' as const, 
        icon: 'sunny',
        sourceUrl: 'https://en.wikipedia.org/wiki/Plant_lighting'
      },
      { 
        id: 'c5', 
        title: 'Low Humidity', 
        description: 'Dry air causes brown leaf tips and edges. Increase humidity with a humidifier or by grouping plants together.', 
        category: 'care' as const, 
        icon: 'water',
        sourceUrl: 'https://en.wikipedia.org/wiki/Humidity'
      },
      { 
        id: 'c6', 
        title: 'Temperature Stress', 
        description: 'Extreme temperatures cause leaf drop and stunted growth. Keep plants away from drafts and maintain consistent temperatures.', 
        category: 'care' as const, 
        icon: 'thermometer',
        sourceUrl: 'https://en.wikipedia.org/wiki/Plant_stress_measurement'
      },
    ];
    
    return care.filter(item => 
      item.title.toLowerCase().includes(query) || 
      item.description.toLowerCase().includes(query)
    );
  };

  const handleResultPress = (result: SearchResult) => {
    // Navigate to plant issue page with the selected result
    router.push({
      pathname: `/plant-issue/${result.id}`,
      params: { 
        selectedIssue: result.id,
        issueTitle: result.title,
        issueDescription: result.description,
        sourceUrl: result.sourceUrl || ''
      }
    });
  };

  const handleSourcePress = (url: string) => {
    // Open the URL in the device's browser
    Linking.openURL(url);
  };

  const searchWithGemini = async () => {
    try {
      // Initialize the Gemini API
      const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      
      // Create the prompt for plant search
      const prompt = `Provide a brief summary (2-3 sentences) about plant issues related to: "${searchQuery}". 
      Focus on the most important information that a plant owner needs to know.
      Keep your response concise and to the point.`;
      
      // Generate content with Gemini
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      setGeminiResults(text);
    } catch (error) {
      console.error('Error searching with Gemini:', error);
      
      // Provide a fallback response when the API is unavailable
      let fallbackText = '';
      
      // Check if the search query matches any of our predefined categories
      const query = searchQuery.toLowerCase();
      
      if (query.includes('spider') || query.includes('mite')) {
        fallbackText = 'Spider mites are tiny pests that can cause significant damage to plants. They typically appear as small red or brown dots on leaves and create fine webs. Regular misting and maintaining proper humidity can help prevent infestations.';
      } else if (query.includes('root') || query.includes('rot')) {
        fallbackText = 'Root rot is a common plant disease caused by overwatering and poor drainage. Symptoms include yellowing leaves, wilting, and soft brown roots. To prevent root rot, ensure proper drainage and allow soil to dry between waterings.';
      } else if (query.includes('powdery') || query.includes('mildew')) {
        fallbackText = 'Powdery mildew is a fungal disease that appears as white powdery spots on leaves. It thrives in high humidity and poor air circulation. Improving ventilation and reducing humidity can help prevent this disease.';
      } else if (query.includes('aphid')) {
        fallbackText = 'Aphids are small insects that cluster on new plant growth and can cause distorted leaves. They produce a sticky residue called honeydew. Regular inspection and treatment with insecticidal soap can help control aphid populations.';
      } else if (query.includes('nutrient') || query.includes('deficiency')) {
        fallbackText = 'Nutrient deficiencies in plants often manifest as discolored leaves or stunted growth. Common deficiencies include nitrogen (yellowing of older leaves), phosphorus (purple discoloration), and potassium (browning leaf edges). Proper fertilization can address these issues.';
      } else {
        fallbackText = `Based on your search for "${searchQuery}", here are some general tips for plant care: Ensure proper watering based on your plant's needs, provide appropriate light conditions, and maintain good air circulation. Regular inspection can help catch problems early.`;
      }
      
      setGeminiResults(fallbackText);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView 
          ref={mainScrollViewRef}
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.hero}>
            <Image 
              source={require('../assets/images/seedling.jpg')}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <View style={styles.heroContent}>
              <Text style={styles.heroTitle}>Plant Diagnosis</Text>
              <Text style={styles.heroDescription}>Identify and fix plant health issues</Text>
            </View>
          </View>
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={handleGuidePress}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="book-outline" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Guide</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={handleScanPlant}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="camera" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Scan Plant</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={handleSearchPress}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="search" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Ask Seymore</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={() => router.push('/strains')}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="leaf" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Strains</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.contentContainer}>
            {!capturedImage && (
              <>
                <Text style={styles.diagnosisTitle}>Common Plant Issues</Text>
                
                <View style={styles.searchContainer}>
                  <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={{ width: '100%' }}
                  >
                    <View style={[
                      styles.searchInputContainer,
                      isSearchFocused && { borderColor: '#4CAF50', borderWidth: 1 }
                    ]}>
                      <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
                      <TextInput
                        style={styles.searchInput}
                        placeholder="Search plant issues..."
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor="#666"
                        onFocus={() => {
                          setIsSearchFocused(true);
                          if (mainScrollViewRef.current) {
                            setTimeout(() => {
                              mainScrollViewRef.current?.scrollTo({ y: 0, animated: true });
                            }, 100);
                          }
                        }}
                        onBlur={() => setIsSearchFocused(false)}
                        returnKeyType="search"
                        onSubmitEditing={() => {
                          Keyboard.dismiss();
                          setIsSearchFocused(false);
                        }}
                      />
                      {searchQuery.length > 0 && (
                        <TouchableOpacity
                          onPress={() => setSearchQuery('')}
                          style={styles.clearButton}
                        >
                          <Ionicons name="close-circle" size={20} color="#666" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </KeyboardAvoidingView>
                  {isSearching ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="large" color="#4CAF50" />
                      <Text style={styles.loadingText}>Searching...</Text>
                    </View>
                  ) : searchQuery.length > 0 ? (
                    <View style={styles.resultsContainer}>
                      {geminiResults && (
                        <View style={styles.geminiResultCard}>
                          <View style={styles.geminiResultHeader}>
                            <Ionicons name="bulb" size={24} color="#4CAF50" />
                            <Text style={styles.geminiResultTitle}>AI Summary</Text>
                          </View>
                          <Text style={styles.geminiResultText}>{geminiResults}</Text>
                        </View>
                      )}
                      
                      <Text style={styles.sectionTitle}>
                        {searchResults.length > 0 
                          ? `Found ${searchResults.length} results` 
                          : 'No results found'}
                      </Text>
                      
                      {searchResults.map((result) => (
                        <View key={result.id}>
                          <TouchableOpacity 
                            style={styles.resultCard}
                            onPress={() => {
                              if (result.sourceUrl) {
                                handleSourcePress(result.sourceUrl);
                              } else {
                                handleResultPress(result);
                              }
                            }}
                          >
                            <View style={styles.resultIcon}>
                              <Ionicons name={result.icon as any} size={24} color="#4CAF50" />
                            </View>
                            <View style={styles.resultContent}>
                              <Text style={styles.resultTitle}>{result.title}</Text>
                              <Text style={styles.resultDescription}>{result.description}</Text>
                              {result.sourceUrl && (
                                <Text style={styles.wikiLink}>View on Wikipedia</Text>
                              )}
                            </View>
                            <Ionicons name="chevron-forward" size={20} color="#ccc" />
                          </TouchableOpacity>
                        </View>
                      ))}
                      
                      <TouchableOpacity 
                        style={styles.newSearchButton}
                        onPress={() => {
                          setSearchQuery('');
                          setSearchResults([]);
                          setGeminiResults(null);
                        }}
                      >
                        <Ionicons name="search" size={20} color="#fff" />
                        <Text style={styles.newSearchButtonText}>New Search</Text>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <View style={styles.emptyStateContainer}>
                      <Ionicons name="search" size={60} color="#ccc" />
                      <Text style={styles.emptyStateTitle}>Search for Plant Issues</Text>
                      <Text style={styles.emptyStateText}>
                        Enter a keyword to find solutions for common plant problems
                      </Text>
                    </View>
                  )}
                </View>
              </>
            )}
            
            <View style={styles.diagnosisContainer}>
              {capturedImage ? (
                <View style={styles.capturedImageContainer} ref={capturedImageRef}>
                  <Text style={styles.diagnosisTitle}>Captured Plant Image</Text>
                  <View style={styles.capturedImageWrapper}>
                    <Image 
                      source={{ uri: capturedImage }} 
                      style={styles.capturedImage}
                      resizeMode="cover"
                    />
                    <TouchableOpacity 
                      style={styles.retakeButton}
                      onPress={() => {
                        setCapturedImage(null);
                        setAnalysis(null);
                        setShowChat(false);
                      }}
                    >
                      <Ionicons name="refresh" size={20} color="#fff" />
                      <Text style={styles.retakeButtonText}>Retake</Text>
                    </TouchableOpacity>
                  </View>
                  
                  {!showChat ? (
                    <View style={styles.analysisContainer}>
                      <Text style={styles.analysisTitle}>AI Plant Analysis</Text>
                      {isAnalyzing ? (
                        <View style={styles.loadingContainer}>
                          <ActivityIndicator size="large" color="#4CAF50" />
                          <Text style={styles.loadingText}>Analyzing your plant...</Text>
                        </View>
                      ) : analysis ? (
                        <View style={styles.analysisContent}>
                          {formatAnalysisText(analysis)}
                        </View>
                      ) : (
                        <Text style={styles.analysisText}>
                          Your plant appears to be healthy. No significant issues detected.
                        </Text>
                      )}
                    </View>
                  ) : (
                    <View style={styles.chatContainer}>
                      <View style={styles.chatHeader}>
                        <Text style={styles.chatTitle}>Chat with Plant Expert</Text>
                      </View>
                      <TouchableOpacity 
                        style={styles.backButton}
                        onPress={() => setShowChat(false)}
                      >
                        <Ionicons name="arrow-back" size={20} color="#4CAF50" />
                        <Text style={styles.backButtonText}>Back to Analysis</Text>
                      </TouchableOpacity>
                      
                      <ScrollView 
                        ref={chatScrollViewRef}
                        style={styles.chatMessagesContainer}
                        contentContainerStyle={styles.chatMessagesContent}
                      >
                        {chatMessages.map((message, index) => (
                          <View 
                            key={index} 
                            style={[
                              styles.messageContainer,
                              message.role === 'user' ? styles.userMessage : styles.assistantMessage
                            ]}
                          >
                            <View style={styles.messageIcon}>
                              <Ionicons 
                                name={message.role === 'user' ? 'person' : 'leaf'} 
                                size={20} 
                                color="#4CAF50" 
                              />
                            </View>
                            <View style={styles.messageContent}>
                              <Text style={styles.messageText}>{message.content}</Text>
                            </View>
                          </View>
                        ))}
                        {isSending && (
                          <View style={styles.loadingMessage}>
                            <ActivityIndicator size="small" color="#4CAF50" />
                            <Text style={styles.loadingMessageText}>Thinking...</Text>
                          </View>
                        )}
                      </ScrollView>
                      
                      <KeyboardAvoidingView
                        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                        style={styles.inputContainer}
                      >
                        <TextInput
                          ref={inputRef}
                          style={styles.input}
                          placeholder="Ask a question about your plant..."
                          value={userInput}
                          onChangeText={setUserInput}
                          multiline
                          maxLength={500}
                          onSubmitEditing={() => {
                            if (Platform.OS === 'ios') {
                              Keyboard.dismiss();
                              inputRef.current?.blur();
                            }
                            sendMessage();
                          }}
                          returnKeyType="send"
                          blurOnSubmit={true}
                          onKeyPress={(e: any) => {
                            if (Platform.OS === 'web' && e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
                              e.preventDefault();
                              sendMessage();
                            }
                          }}
                        />
                        <TouchableOpacity 
                          style={styles.sendButton}
                          onPress={() => {
                            if (Platform.OS === 'ios') {
                              Keyboard.dismiss();
                              inputRef.current?.blur();
                            }
                            sendMessage();
                          }}
                          disabled={!userInput.trim() || isSending}
                        >
                          <Ionicons 
                            name="send" 
                            size={20} 
                            color={!userInput.trim() || isSending ? '#ccc' : '#fff'} 
                          />
                        </TouchableOpacity>
                      </KeyboardAvoidingView>
                    </View>
                  )}
                </View>
              ) : null}
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="diagnose" plantCount={plants.length} />
      </SafeAreaView>
      {showHealthModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowHealthModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.healthModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Plant Health Status</Text>
                <TouchableOpacity 
                  onPress={() => setShowHealthModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.healthModalBody}>
                {unhealthyPlants.length === 0 ? (
                  <View style={styles.emptyStateContainer}>
                    <View style={styles.emptyStateIcon}>
                      <Ionicons name="checkmark-circle-outline" size={48} color="#4CAF50" />
                    </View>
                    <Text style={styles.emptyStateTitle}>All Plants Healthy!</Text>
                    <Text style={styles.emptyStateText}>Your plants are being properly maintained</Text>
                  </View>
                ) : (
                  <>
                    <Text style={styles.healthModalSubtitle}>
                      {unhealthyPlants.length} plant{unhealthyPlants.length !== 1 ? 's' : ''} need{unhealthyPlants.length !== 1 ? '' : 's'} attention:
                    </Text>
                    <ScrollView style={styles.unhealthyPlantsList}>
                      {unhealthyPlants.map((plant, index) => (
                        <View key={index} style={styles.unhealthyPlantItem}>
                          <Text style={styles.unhealthyPlantName}>{plant.name}</Text>
                          <View style={styles.issueContainer}>
                            {plant.wateringIssue && (
                              <View style={styles.issueRow}>
                                <Ionicons name="water" size={20} color="#FF5252" />
                                <Text style={styles.issueText}>
                                  {getWateringMessage(plant.lastWatered)}
                                </Text>
                              </View>
                            )}
                            {plant.feedingIssue && (
                              <View style={styles.issueRow}>
                                <Ionicons name="leaf" size={20} color="#FF5252" />
                                <Text style={styles.issueText}>
                                  {getFeedingMessage(plant.lastFeeding)}
                                </Text>
                              </View>
                            )}
                          </View>
                        </View>
                      ))}
                    </ScrollView>
                  </>
                )}
              </View>
              
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.closeButton]}
                  onPress={() => setShowHealthModal(false)}
                >
                  <Text style={styles.closeButtonText}>Close</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32', // Grass green
  },
  safeArea: {
    backgroundColor: '#2E7D32', // Grass green
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
    gap: 4,
  },
  actionButtonIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  actionButtonText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  cardContainer: {
    padding: 16,
  },
  statsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 12,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    justifyContent: 'center',
  },
  statItem: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '30%',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 12,
    marginBottom: 12,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#E8F5E9',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: '#4CAF50',
    textAlign: 'center',
  },
  diagnosisContainer: {
    padding: 16,
    marginTop: -8,
  },
  diagnosisTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 12,
  },
  diagnosisCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 16,
    padding: 8,
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    overflow: 'hidden',
  },
  diagnosisItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E8F5E9',
    backgroundColor: '#FFFFFF',
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  diagnosisIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  diagnosisContent: {
    flex: 1,
  },
  diagnosisName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  diagnosisDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  capturedImageContainer: {
    padding: 12,
  },
  capturedImageWrapper: {
    position: 'relative',
    marginBottom: 12,
  },
  capturedImage: {
    width: '100%',
    height: 250,
    borderRadius: 10,
  },
  retakeButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 4,
  },
  retakeButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  analysisContainer: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 16,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  analysisTitle: {
    fontSize: 18,
    fontWeight: 'normal',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  analysisContent: {
    gap: 16,
  },
  analysisText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
    textAlign: 'center',
    fontWeight: '500',
  },
  summaryContainer: {
    gap: 16,
  },
  summarySection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  summaryContentText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#4CAF50',
    marginTop: 10,
    textAlign: 'center',
  },
  helpButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
  },
  helpButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  chatContainer: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 16,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    height: 600,
  },
  chatHeader: {
    marginBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#E8F5E9',
    paddingBottom: 4,
  },
  chatTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    textAlign: 'center',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  backButtonText: {
    color: '#4CAF50',
    fontSize: 14,
    marginLeft: 4,
  },
  chatMessagesContainer: {
    flex: 1,
    marginBottom: 16,
  },
  chatMessagesContent: {
    paddingBottom: 16,
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
    marginBottom: 12,
    flexDirection: 'row' as const,
    alignItems: 'flex-start',
    gap: 8,
  },
  messageIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E8F5E9',
  },
  messageContent: {
    flex: 1,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E8F5E9',
    borderBottomRightRadius: 4,
  },
  assistantMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E8F5E9',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  loadingMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    marginBottom: 12,
  },
  loadingMessageText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E8F5E9',
    paddingTop: 12,
  },
  input: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    ...(Platform.OS === 'web' ? { 
      borderWidth: 0, 
      WebkitAppearance: 'none',
      outline: 'none',
      boxShadow: 'none',
      border: 'none'
    } : {}),
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  healthModalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalCloseButton: {
    padding: 4,
  },
  healthModalBody: {
    marginBottom: 16,
  },
  healthModalSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  unhealthyPlantsList: {
    maxHeight: 300,
  },
  unhealthyPlantItem: {
    backgroundColor: '#F1F8E9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 4,
  },
  unhealthyPlantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 8,
  },
  issueContainer: {
    gap: 8,
  },
  issueRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  issueText: {
    fontSize: 16,
    color: '#333',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  closeButton: {
    backgroundColor: '#E0E0E0',
  },
  closeButtonText: {
    color: '#666',
    fontWeight: '600',
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyStateIcon: {
    backgroundColor: '#4CAF50',
    borderRadius: 24,
    padding: 8,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2E7D32',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  searchContainer: {
    marginTop: 8,
    marginBottom: 8,
    paddingHorizontal: 20,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    paddingHorizontal: 15,
    height: 50,
    marginBottom: 8,
    ...(Platform.OS === 'web' ? { 
      borderWidth: 0,
      outline: 'none',
      boxShadow: 'none',
      border: 'none'
    } : {}),
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    ...(Platform.OS === 'web' ? { 
      borderWidth: 0, 
      WebkitAppearance: 'none',
      outline: 'none',
      boxShadow: 'none',
      border: 'none'
    } : {}),
  },
  clearButton: {
    padding: 5,
  },
  searchHelperContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    marginTop: 10,
    marginBottom: 10,
  },
  searchIconText: {
    fontSize: 60,
    color: '#cccccc',
    fontFamily: 'ionicons',
    fontWeight: 'normal',
    fontStyle: 'normal',
    marginBottom: 10,
  },
  searchTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 5,
    marginTop: 5,
  },
  searchDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  resultsContainer: {
    padding: 16,
  },
  geminiResultCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  geminiResultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  geminiResultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginLeft: 8,
  },
  geminiResultText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  resultCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  resultIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  resultDescription: {
    fontSize: 14,
    color: '#666',
  },
  wikiLink: {
    fontSize: 12,
    color: '#4CAF50',
    marginTop: 4,
    textDecorationLine: 'underline',
  },
  contentContainer: {
    padding: 16,
  },
  newSearchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 10,
    padding: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  newSearchButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
}); 