import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Header from '../components/Header';

interface PlantIssue {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
}

const plantIssues: PlantIssue[] = [
  {
    id: 'bronze-brown-patches',
    title: 'Bronze or brown patches',
    description: 'Discolored patches appearing on plant surfaces',
    icon: 'color-palette',
    color: '#8D6E63'
  },
  {
    id: 'brown-slimy-roots',
    title: 'Brown or slimy roots',
    description: 'Root system showing signs of decay or disease',
    icon: 'water',
    color: '#795548'
  },
  {
    id: 'brown-yellow-tips',
    title: 'Brown or yellow leaf tips/edges',
    description: 'Leaf edges or tips showing discoloration',
    icon: 'leaf',
    color: '#FFA000'
  },
  {
    id: 'buds-dying',
    title: 'Buds dying',
    description: 'Flower buds failing to develop or dying off',
    icon: 'flower',
    color: '#D32F2F'
  },
  {
    id: 'buds-look-odd',
    title: 'Buds look odd',
    description: 'Abnormal appearance or development of buds',
    icon: 'alert-circle',
    color: '#7B1FA2'
  },
  {
    id: 'bugs-visible',
    title: 'Bugs are visible',
    description: 'Pests or insects visible on the plant',
    icon: 'bug',
    color: '#388E3C'
  },
  {
    id: 'curling-clawing-leaves',
    title: 'Curling or clawing leaves',
    description: 'Leaves showing abnormal curling or clawing',
    icon: 'git-branch',
    color: '#00796B'
  },
  {
    id: 'dark-leaves',
    title: 'Dark leaves',
    description: 'Leaves appearing darker than normal',
    icon: 'contrast',
    color: '#424242'
  },
  {
    id: 'drooping-plant',
    title: 'Drooping plant',
    description: 'Plant showing signs of wilting or drooping',
    icon: 'trending-down',
    color: '#5D4037'
  },
  {
    id: 'holes-in-leaves',
    title: 'Holes in leaves',
    description: 'Leaves with holes or damage',
    icon: 'remove-circle',
    color: '#689F38'
  },
  {
    id: 'mold-powder',
    title: 'Mold or powder',
    description: 'Powdery or moldy growth on plant surfaces',
    icon: 'snow',
    color: '#90A4AE'
  },
  {
    id: 'pink-purple-leaves',
    title: 'Pink or purple on leaves',
    description: 'Unusual pink or purple discoloration on leaves',
    icon: 'color-filter',
    color: '#E91E63'
  },
  {
    id: 'red-stems',
    title: 'Red stems',
    description: 'Stems showing unusual red coloring',
    icon: 'git-branch',
    color: '#F44336'
  },
  {
    id: 'shiny-smooth-leaves',
    title: 'Shiny or smooth leaves',
    description: 'Leaves with unusual shine or texture',
    icon: 'sunny',
    color: '#FFC107'
  },
  {
    id: 'spots-markings',
    title: 'Spots or markings',
    description: 'Unusual spots or markings on plant surfaces',
    icon: 'color-wand',
    color: '#9C27B0'
  },
  {
    id: 'twisted-growth',
    title: 'Twisted growth',
    description: 'Abnormal twisting or contortion of plant parts',
    icon: 'sync',
    color: '#009688'
  },
  {
    id: 'webbing',
    title: 'Webbing',
    description: 'Web-like structures on the plant',
    icon: 'globe',
    color: '#607D8B'
  },
  {
    id: 'wilting-leaves',
    title: 'Wilting leaves',
    description: 'Leaves showing signs of wilting or dehydration',
    icon: 'water',
    color: '#2196F3'
  },
  {
    id: 'yellow-between-veins',
    title: 'Yellow between leaf veins',
    description: 'Yellowing occurring between leaf veins',
    icon: 'leaf',
    color: '#FFEB3B'
  },
  {
    id: 'yellow-leaves',
    title: 'Yellow leaves',
    description: 'General yellowing of leaves',
    icon: 'warning',
    color: '#FF9800'
  }
];

export default function PlantIssuesScreen() {
  const router = useRouter();

  const handleIssuePress = (issueId: string) => {
    router.push(`/plant-issue/${issueId}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <Header title="Common Plant Issues" />
      <ScrollView style={styles.content}>
        <Text style={styles.introText}>
          Select a plant issue to learn more about its causes and solutions.
        </Text>
        <View style={styles.issuesContainer}>
          {plantIssues.map((issue) => (
            <TouchableOpacity
              key={issue.id}
              style={[styles.issueCard, { borderLeftColor: issue.color }]}
              onPress={() => handleIssuePress(issue.id)}
            >
              <View style={[styles.iconContainer, { backgroundColor: issue.color }]}>
                <Ionicons name={issue.icon as any} size={24} color="#fff" />
              </View>
              <View style={styles.issueContent}>
                <Text style={styles.issueTitle}>{issue.title}</Text>
                <Text style={styles.issueDescription}>{issue.description}</Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color="#666" />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  introText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    lineHeight: 24,
  },
  issuesContainer: {
    gap: 16,
  },
  issueCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  issueContent: {
    flex: 1,
  },
  issueTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  issueDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
}); 