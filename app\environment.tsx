import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Modal, Alert, TextInput, Image, Platform, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import Slider from '@react-native-community/slider';
import Header from '../components/Header';
import Footer from '../components/Footer';

interface Environment {
  id: string;
  name: string;
  type: 'indoor' | 'outdoor';
  location: string;
  temperature: {
    min: number;
    max: number;
    unit: 'C' | 'F';
  };
  humidity: {
    min: number;
    max: number;
    unit: '%';
  };
  lightIntensity: {
    percentage: number;
  };
  notes: string;
}

// Using a simpler storage key
const STORAGE_KEY = '@environments_simple';
const TEMP_UNIT_PREFERENCE_KEY = '@temperature_unit_preference';

const defaultEnvironments: Environment[] = [
  {
    id: '1',
    name: 'Indoor Tent',
    type: 'indoor',
    location: 'Basement',
    temperature: {
      min: 20,
      max: 28,
      unit: 'C'
    },
    humidity: {
      min: 40,
      max: 60,
      unit: '%'
    },
    lightIntensity: {
      percentage: 50
    },
    notes: 'Ventilation system with carbon filter'
  },
  {
    id: '2',
    name: 'Outdoor Garden',
    type: 'outdoor',
    location: 'Backyard',
    temperature: {
      min: 15,
      max: 35,
      unit: 'C'
    },
    humidity: {
      min: 30,
      max: 70,
      unit: '%'
    },
    lightIntensity: {
      percentage: 50
    },
    notes: 'Natural sunlight with wind protection'
  }
];

export default function EnvironmentSimpleScreen() {
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [selectedEnvironment, setSelectedEnvironment] = useState<Environment | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [temperatureUnit, setTemperatureUnit] = useState<'C' | 'F'>('F');
  const [notification, setNotification] = useState<{message: string, type: 'error' | 'success' | null}>({message: '', type: null});
  const scrollViewRef = useRef<ScrollView>(null);
  const [plantCounts, setPlantCounts] = useState<Record<string, number>>({});
  
  // Form state
  const [name, setName] = useState('');
  const [type, setType] = useState<'indoor' | 'outdoor'>('indoor');
  const [location, setLocation] = useState('');
  const [temperature, setTemperature] = useState({ min: 65, max: 80, unit: 'F' as 'C' | 'F' });
  const [humidity, setHumidity] = useState({ min: 50, max: 70, unit: '%' });
  const [lightIntensity, setLightIntensity] = useState({ percentage: 50 });
  const [notes, setNotes] = useState('');
  
  const [plants, setPlants] = useState<any[]>([]);
  
  const router = useRouter();

  useEffect(() => {
    loadEnvironments();
    loadTemperatureUnitPreference();
    loadPlants();
  }, []);

  const loadTemperatureUnitPreference = async () => {
    try {
      const savedUnit = await AsyncStorage.getItem(TEMP_UNIT_PREFERENCE_KEY);
      if (savedUnit === 'C' || savedUnit === 'F') {
        setTemperatureUnit(savedUnit);
      }
    } catch (error) {
      console.error('Error loading temperature unit preference:', error);
    }
  };

  const saveTemperatureUnitPreference = async (unit: 'C' | 'F') => {
    try {
      await AsyncStorage.setItem(TEMP_UNIT_PREFERENCE_KEY, unit);
      setTemperatureUnit(unit);
    } catch (error) {
      console.error('Error saving temperature unit preference:', error);
    }
  };

  const convertTemperature = (temp: number, fromUnit: 'C' | 'F', toUnit: 'C' | 'F'): number => {
    if (fromUnit === toUnit) return temp;
    if (fromUnit === 'C' && toUnit === 'F') return (temp * 9/5) + 32;
    if (fromUnit === 'F' && toUnit === 'C') return (temp - 32) * 5/9;
    return temp;
  };

  const formatTemperature = (temp: number, unit: 'C' | 'F'): string => {
    return `${Math.round(temp)}°${unit}`;
  };

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => prev + '\n' + info);
    console.log(info);
  };

  const loadEnvironments = async () => {
    try {
      addDebugInfo('Loading environments from AsyncStorage...');
      const storedEnvironments = await AsyncStorage.getItem(STORAGE_KEY);
      addDebugInfo(`Stored data: ${storedEnvironments || 'null'}`);
      
      if (storedEnvironments) {
        try {
          const parsedEnvironments = JSON.parse(storedEnvironments);
          addDebugInfo(`Parsed environments: ${JSON.stringify(parsedEnvironments)}`);
          
          // Verify that parsedEnvironments is an array
          if (Array.isArray(parsedEnvironments)) {
            // Check if the array is empty
            if (parsedEnvironments.length === 0) {
              addDebugInfo('No environments found in storage, loading defaults');
              setEnvironments(defaultEnvironments);
              await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultEnvironments));
            } else {
              setEnvironments(parsedEnvironments);
              addDebugInfo(`Successfully loaded ${parsedEnvironments.length} environments`);
            }
          } else {
            addDebugInfo('Parsed data is not an array, setting default environments');
            setEnvironments(defaultEnvironments);
            await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultEnvironments));
          }
        } catch (parseError) {
          addDebugInfo(`Error parsing stored data: ${parseError}`);
          setEnvironments(defaultEnvironments);
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultEnvironments));
        }
      } else {
        addDebugInfo('No stored environments found, setting default environments');
        setEnvironments(defaultEnvironments);
        // Save default environments
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultEnvironments));
      }
    } catch (error) {
      addDebugInfo(`Error loading: ${error}`);
      setEnvironments(defaultEnvironments);
    }
  };

  const loadPlants = async () => {
    try {
      const plantsData = await AsyncStorage.getItem('@plants_data');
      if (plantsData) {
        const plants = JSON.parse(plantsData);
        setPlants(plants);
        
        // Calculate plant counts for each environment
        const counts: Record<string, number> = {};
        
        // Initialize counts for all environments
        environments.forEach(env => {
          counts[env.name] = 0;
        });
        
        // Count plants in each environment
        plants.forEach((p: any) => {
          if (p.environment && counts[p.environment] !== undefined) {
            counts[p.environment]++;
          }
        });
        
        setPlantCounts(counts);
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  // Add a new useEffect to update plant counts when environments change
  useEffect(() => {
    if (plants.length > 0) {
      const counts: Record<string, number> = {};
      
      // Initialize counts for all environments
      environments.forEach(env => {
        counts[env.name] = 0;
      });
      
      // Count plants in each environment
      plants.forEach((p: any) => {
        if (p.environment && counts[p.environment] !== undefined) {
          counts[p.environment]++;
        }
      });
      
      setPlantCounts(counts);
    }
  }, [environments, plants]);

  const handleEnvironmentPress = (environment: Environment) => {
    setSelectedEnvironment(environment);
    setIsEditing(false);
    setIsModalVisible(true);
  };

  const handleAddEnvironment = () => {
    setSelectedEnvironment(null);
    setIsEditing(true);
    resetForm();
    setIsModalVisible(true);
  };

  const handleEditEnvironment = (environment: Environment) => {
    setIsEditing(true);
    setSelectedEnvironment(environment);
    setName(environment.name);
    setType(environment.type);
    setLocation(environment.location);
    setTemperature(environment.temperature);
    setHumidity(environment.humidity);
    setLightIntensity(environment.lightIntensity);
    setNotes(environment.notes);
  };

  const handleSaveEnvironment = async () => {
    console.log('Save button pressed');
    addDebugInfo('Save button pressed');
    
    if (!name) {
      console.log('Validation failed:', { name });
      addDebugInfo(`Validation failed - Name: ${name}`);
      Alert.alert('Error', 'Please enter a name for the environment');
      return;
    }

    try {
      addDebugInfo('Starting environment save process...');
      addDebugInfo(`Current form values - Name: ${name}, Type: ${type}, Location: ${location}`);
      console.log('Current form values:', { name, type, location });
      
      let updatedEnvironments: Environment[];
      
      if (selectedEnvironment) {
        // Update existing environment
        addDebugInfo(`Updating existing environment with ID: ${selectedEnvironment.id}`);
        console.log('Updating environment:', selectedEnvironment.id);
        updatedEnvironments = environments.map(env => 
          env.id === selectedEnvironment.id 
            ? {
                ...env,
                name,
                type,
                location,
                temperature,
                humidity: { ...humidity, unit: '%' as const },
                lightIntensity,
                notes
              }
            : env
        );
      } else {
        // Create new environment
        const newEnv: Environment = {
          id: Date.now().toString(),
          name,
          type: type || 'indoor',
          location,
          temperature,
          humidity: { ...humidity, unit: '%' as const },
          lightIntensity,
          notes
        };
        addDebugInfo(`Creating new environment with ID: ${newEnv.id}`);
        console.log('Creating new environment:', newEnv);
        updatedEnvironments = [...environments, newEnv];
      }
      
      addDebugInfo(`Total environments after update: ${updatedEnvironments.length}`);
      console.log('Updated environments:', updatedEnvironments);
      
      // First update state
      addDebugInfo('Updating React state...');
      setEnvironments(updatedEnvironments);
      
      // Then save to AsyncStorage
      addDebugInfo('Attempting to save to AsyncStorage...');
      const jsonValue = JSON.stringify(updatedEnvironments);
      addDebugInfo(`JSON string length: ${jsonValue.length}`);
      console.log('Saving to AsyncStorage:', jsonValue);
      
      await AsyncStorage.setItem(STORAGE_KEY, jsonValue);
      addDebugInfo('Successfully saved to AsyncStorage');
      
      // Verify the save
      const savedData = await AsyncStorage.getItem(STORAGE_KEY);
      addDebugInfo(`Verification - Read back from AsyncStorage: ${savedData ? 'Data exists' : 'No data found'}`);
      console.log('Verification - Read back data:', savedData);
      
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        addDebugInfo(`Verification - Parsed data: ${JSON.stringify(parsedData)}`);
        console.log('Verification - Parsed data:', parsedData);
      }
      
      // Update plant history for plants using this environment
      try {
        const plantsData = await AsyncStorage.getItem('@plants_data');
        if (plantsData) {
          const plants = JSON.parse(plantsData);
          const now = new Date().toISOString();
          
          // Find plants that use this environment
          const plantsToUpdate = plants.filter((plant: any) => 
            plant.environment === (selectedEnvironment ? selectedEnvironment.name : name)
          );
          
          if (plantsToUpdate.length > 0) {
            console.log(`Updating history for ${plantsToUpdate.length} plants using this environment`);
            
            // Update each plant's history
            const updatedPlants = plants.map((plant: any) => {
              if (plantsToUpdate.some((p: any) => p.id === plant.id)) {
                const historyItem = {
                  id: `env-${Date.now()}-${plant.id}`,
                  type: 'log',
                  date: now,
                  description: selectedEnvironment 
                    ? `Environment "${selectedEnvironment.name}" updated` 
                    : `Environment "${name}" added`
                };
                
                return {
                  ...plant,
                  history: [historyItem, ...(plant.history || [])]
                };
              }
              return plant;
            });
            
            // Save updated plants
            await AsyncStorage.setItem('@plants_data', JSON.stringify(updatedPlants));
            console.log('Updated plant history for environment change');
          }
        }
      } catch (error) {
        console.error('Error updating plant history:', error);
      }
      
      // Reset form and close modal
      resetForm();
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error in handleSaveEnvironment:', error);
      addDebugInfo(`Error in handleSaveEnvironment: ${error}`);
      Alert.alert('Error', 'Failed to save environment');
    }
  };

  const handleDeleteEnvironment = async (id: string) => {
    try {
      // Filter out the environment to delete
      const updatedEnvironments = environments.filter(env => env.id !== id);
      
      // Update state first
      setEnvironments(updatedEnvironments);
      
      // Then save to AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedEnvironments));
      
      // Close the modal
      setIsModalVisible(false);
      setSelectedEnvironment(null);
      setIsEditing(false);
      resetForm();
      
      // Show success message
      Alert.alert('Success', 'Environment deleted successfully');
    } catch (error) {
      console.error('Error deleting environment:', error);
      Alert.alert('Error', 'Failed to delete environment');
    }
  };

  // New direct delete function
  const deleteEnvironment = (id: string) => {
    // Filter out the environment to delete
    const updatedEnvironments = environments.filter(env => env.id !== id);
    
    // Update state first
    setEnvironments(updatedEnvironments);
    
    // Then save to AsyncStorage
    AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedEnvironments))
      .then(() => {
        // Close the modal
        setIsModalVisible(false);
        setSelectedEnvironment(null);
        setIsEditing(false);
        resetForm();
        
        // Show success message
        Alert.alert('Success', 'Environment deleted successfully');
      })
      .catch(error => {
        console.error('Error deleting environment:', error);
        Alert.alert('Error', 'Failed to delete environment');
      });
  };

  const resetForm = () => {
    setSelectedEnvironment(null);
    setName('');
    setType('indoor');
    setLocation('');
    setTemperature({ min: 65, max: 80, unit: 'F' });
    setHumidity({ min: 50, max: 70, unit: '%' });
    setLightIntensity({ percentage: 50 });
    setNotes('');
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSelectedEnvironment(null);
    setIsEditing(false);
    resetForm();
  };

  const showNotification = (message: string, type: 'error' | 'success') => {
    setNotification({message, type});
    // Auto-hide notification after 10 seconds (increased from 5 seconds)
    setTimeout(() => {
      setNotification({message: '', type: null});
    }, 10000);
    
    // Auto-scroll to the bottom when notification appears
    if (type === 'error' && scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  const handleTemperatureUnitChange = (newUnit: 'C' | 'F') => {
    if (newUnit === temperature.unit) return;
    
    // Convert the temperature values
    const convertTemp = (temp: number, fromUnit: 'C' | 'F', toUnit: 'C' | 'F'): number => {
      if (fromUnit === toUnit) return temp;
      if (fromUnit === 'C' && toUnit === 'F') return (temp * 9/5) + 32;
      if (fromUnit === 'F' && toUnit === 'C') return (temp - 32) * 5/9;
      return temp;
    };
    
    const newMin = Math.round(convertTemp(temperature.min, temperature.unit, newUnit));
    const newMax = Math.round(convertTemp(temperature.max, temperature.unit, newUnit));
    
    setTemperature({
      min: newMin,
      max: newMax,
      unit: newUnit
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Environments</Text>
            <Text style={styles.heroDescription}>Manage your growing spaces</Text>
          </View>
        </View>
        
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          bounces={true}
          scrollEventThrottle={16}
          decelerationRate="normal"
          style={styles.scrollView}
        >
          <View style={styles.cardContainer}>
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <View>
                  {/* Removed the title and description text */}
                </View>
                <TouchableOpacity 
                  style={styles.addButton}
                  onPress={handleAddEnvironment}
                >
                  <Ionicons name="add-circle-outline" size={24} color="#fff" />
                  <Text style={styles.addButtonText}>Add Environment</Text>
                </TouchableOpacity>
              </View>
              
              {environments.length === 0 ? (
                <View style={styles.emptyState}>
                  <Ionicons name="leaf-outline" size={48} color="#4CAF50" />
                  <Text style={styles.emptyStateText}>No environments yet</Text>
                  <Text style={styles.emptyStateDescription}>
                    Add your first growing environment to get started
                  </Text>
                  <TouchableOpacity 
                    style={styles.emptyStateButton}
                    onPress={handleAddEnvironment}
                  >
                    <Text style={styles.emptyStateButtonText}>Add Environment</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.environmentList}>
                  {environments.map(environment => (
                    <TouchableOpacity 
                      key={environment.id}
                      style={styles.environmentItem}
                      onPress={() => handleEnvironmentPress(environment)}
                    >
                      <View style={styles.environmentIcon}>
                        <Ionicons 
                          name={environment.type === 'indoor' ? 'home-outline' : 'sunny-outline'} 
                          size={24} 
                          color="#4CAF50" 
                        />
                      </View>
                      <View style={styles.environmentInfo}>
                        <View style={styles.environmentHeader}>
                          <Text style={styles.environmentName}>{environment.name}</Text>
                          <View style={styles.plantCountBadge}>
                            <Ionicons name="leaf-outline" size={14} color="#4CAF50" />
                            <Text style={styles.plantCountText}>{plantCounts[environment.name] || 0}</Text>
                          </View>
                        </View>
                        <Text style={styles.environmentDescription}>{environment.location}</Text>
                        <View style={styles.environmentDetails}>
                          <Text style={styles.environmentDetail}>
                            <Ionicons name="thermometer-outline" size={16} color="#666" /> {formatTemperature(convertTemperature(environment.temperature.min, environment.temperature.unit, temperatureUnit), temperatureUnit)} - {formatTemperature(convertTemperature(environment.temperature.max, environment.temperature.unit, temperatureUnit), temperatureUnit)}
                          </Text>
                          <Text style={styles.environmentDetail}>
                            <Ionicons name="water-outline" size={16} color="#666" /> {environment.humidity.min}% - {environment.humidity.max}%
                          </Text>
                        </View>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        </ScrollView>
        
        <Modal
          animationType="slide"
          transparent={true}
          visible={isModalVisible}
          onRequestClose={closeModal}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {isEditing ? (selectedEnvironment ? 'Edit Environment' : 'Add Environment') : 'Environment Details'}
                </Text>
                <TouchableOpacity onPress={closeModal}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              
              <ScrollView 
                style={styles.modalBody}
                ref={scrollViewRef}
              >
                {isEditing ? (
                  <>
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Name</Text>
                      <TextInput
                        style={styles.input}
                        value={name}
                        onChangeText={setName}
                        placeholder="Enter environment name"
                          returnKeyType="done"
                          blurOnSubmit={true}
                          onSubmitEditing={() => {
                            Keyboard.dismiss();
                          }}
                      />
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Type</Text>
                      <View style={styles.typeSelector}>
                        <TouchableOpacity 
                          style={[
                            styles.typeOption, 
                            type === 'indoor' && styles.typeOptionSelected
                          ]}
                          onPress={() => setType('indoor')}
                        >
                          <Ionicons 
                            name="home-outline" 
                            size={20} 
                            color={type === 'indoor' ? '#fff' : '#4CAF50'} 
                          />
                          <Text 
                            style={[
                              styles.typeOptionText,
                              type === 'indoor' && styles.typeOptionTextSelected
                            ]}
                          >
                            Indoor
                          </Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                          style={[
                            styles.typeOption, 
                            type === 'outdoor' && styles.typeOptionSelected
                          ]}
                          onPress={() => setType('outdoor')}
                        >
                          <Ionicons 
                            name="sunny-outline" 
                            size={20} 
                            color={type === 'outdoor' ? '#fff' : '#4CAF50'} 
                          />
                          <Text 
                            style={[
                              styles.typeOptionText,
                              type === 'outdoor' && styles.typeOptionTextSelected
                            ]}
                          >
                            Outdoor
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Location</Text>
                      <TextInput
                        style={[styles.input, styles.textArea]}
                        value={location}
                        onChangeText={setLocation}
                        placeholder="Enter environment location"
                        multiline
                        numberOfLines={2}
                          returnKeyType="done"
                          blurOnSubmit={true}
                          onSubmitEditing={() => Keyboard.dismiss()}
                      />
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Temperature Range</Text>
                      <View style={styles.rangeInputs}>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Min</Text>
                          <TextInput
                            style={styles.input}
                            value={temperature.min.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setTemperature({...temperature, min: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Max</Text>
                          <TextInput
                            style={styles.input}
                            value={temperature.max.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setTemperature({...temperature, max: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.unitSelector}>
                          <TouchableOpacity 
                            style={[
                              styles.unitOption, 
                              temperature.unit === 'C' && styles.unitOptionSelected
                            ]}
                              onPress={() => handleTemperatureUnitChange('C')}
                          >
                            <Text 
                              style={[
                                styles.unitOptionText,
                                temperature.unit === 'C' && styles.unitOptionTextSelected
                              ]}
                            >
                              °C
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity 
                            style={[
                              styles.unitOption, 
                              temperature.unit === 'F' && styles.unitOptionSelected
                            ]}
                              onPress={() => handleTemperatureUnitChange('F')}
                          >
                            <Text 
                              style={[
                                styles.unitOptionText,
                                temperature.unit === 'F' && styles.unitOptionTextSelected
                              ]}
                            >
                              °F
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Humidity Range</Text>
                      <View style={styles.rangeInputs}>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Min</Text>
                          <TextInput
                            style={styles.input}
                            value={humidity.min.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setHumidity({...humidity, min: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Max</Text>
                          <TextInput
                            style={styles.input}
                            value={humidity.max.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setHumidity({...humidity, max: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.unitSelector}>
                          <Text style={styles.unitText}>%</Text>
                        </View>
                      </View>
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Light Intensity</Text>
                      <View style={styles.sliderContainer}>
                        <Slider
                          style={styles.slider}
                          minimumValue={0}
                          maximumValue={100}
                          value={lightIntensity.percentage}
                          onValueChange={(value: number) => setLightIntensity({ percentage: value })}
                          minimumTrackTintColor="#10B981"
                          maximumTrackTintColor="#E5E7EB"
                          thumbTintColor="#10B981"
                        />
                        <Text style={styles.sliderValue}>{Math.round(lightIntensity.percentage)}%</Text>
                      </View>
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Notes</Text>
                      <TextInput
                        style={[styles.input, styles.textArea]}
                        value={notes}
                        onChangeText={setNotes}
                        placeholder="Enter additional notes"
                        multiline
                        numberOfLines={4}
                      />
                    </View>
                    
                    {/* Inline notification message above the buttons */}
                    {notification.message && (
                      <View style={[
                        styles.inlineNotification, 
                        notification.type === 'error' ? styles.errorNotification : styles.successNotification
                      ]}>
                        <Text style={styles.notificationText}>{notification.message}</Text>
                        <TouchableOpacity onPress={() => setNotification({message: '', type: null})}>
                          <Ionicons name="close" size={20} color="#333" />
                        </TouchableOpacity>
                      </View>
                    )}
                    
                    <View style={styles.modalFooter}>
                      {selectedEnvironment && (
                        <TouchableOpacity 
                          style={styles.deleteButton} 
                          onPress={() => {
                            // For web, we'll use a simpler approach without Alert
                            if (selectedEnvironment) {
                              console.log('Checking if environment can be deleted:', selectedEnvironment.id);
                              
                              // Check if any plants are assigned to this environment
                              AsyncStorage.getItem('@plants_data')
                                .then(plantsData => {
                                  if (plantsData) {
                                    const plants = JSON.parse(plantsData);
                                    const plantsInEnvironment = plants.filter(
                                      (plant: any) => plant.environment === selectedEnvironment.name
                                    );
                                    
                                    console.log(`Found ${plantsInEnvironment.length} plants in this environment`);
                                    
                                    if (plantsInEnvironment.length > 0) {
                                      // Cannot delete environment with plants
                                      console.log('Cannot delete environment with plants');
                                      
                                      // Show notification for web users
                                      showNotification(
                                        `Error: ${plantsInEnvironment.length} plant(s) are assigned to this environment. Please move all plants to a new environment.`,
                                        'error'
                                      );
                                      
                                      // Also show alert for mobile users
                                      if (Platform.OS !== 'web') {
                                        Alert.alert(
                                          'Cannot Delete Environment',
                                          `Error: ${plantsInEnvironment.length} plant(s) are assigned to this environment. Please move all plants to a new environment.`
                                        );
                                      }
                                      return;
                                    }
                                    
                                    // No plants assigned, proceed with deletion
                                    console.log('No plants assigned, proceeding with deletion');
                                    
                                    // Create a new array without the selected environment
                                    const newEnvironments = environments.filter(env => env.id !== selectedEnvironment.id);
                                    console.log('New environments array:', newEnvironments);
                                    
                                    // Update state immediately
                                    setEnvironments(newEnvironments);
                                    
                                    // Save to AsyncStorage
                                    AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newEnvironments))
                                      .then(() => {
                                        console.log('Successfully saved to AsyncStorage');
                                        
                                        // Close modal and reset form
                                        setIsModalVisible(false);
                                        setSelectedEnvironment(null);
                                        setIsEditing(false);
                                        resetForm();
                                      })
                                      .catch(error => {
                                        console.error('Error saving to AsyncStorage:', error);
                                      });
                                  } else {
                                    // No plants data, proceed with deletion
                                    console.log('No plants data found, proceeding with deletion');
                                    
                                    // Create a new array without the selected environment
                                    const newEnvironments = environments.filter(env => env.id !== selectedEnvironment.id);
                                    
                                    // Update state immediately
                                    setEnvironments(newEnvironments);
                                    
                                    // Save to AsyncStorage
                                    AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newEnvironments))
                                      .then(() => {
                                        console.log('Successfully saved to AsyncStorage');
                                        
                                        // Close modal and reset form
                                        setIsModalVisible(false);
                                        setSelectedEnvironment(null);
                                        setIsEditing(false);
                                        resetForm();
                                      })
                                      .catch(error => {
                                        console.error('Error saving to AsyncStorage:', error);
                                      });
                                  }
                                })
                                .catch(error => {
                                  console.error('Error checking plants:', error);
                                });
                            }
                          }}
                        >
                          <Text style={styles.deleteButtonText}>Delete</Text>
                        </TouchableOpacity>
                      )}
                      <TouchableOpacity 
                        style={styles.cancelButton} 
                        onPress={() => {
                          setSelectedEnvironment(null);
                          setIsEditing(false);
                          setIsModalVisible(false);
                          resetForm();
                        }}
                      >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                      </TouchableOpacity>
                      <TouchableOpacity 
                        style={styles.saveButton} 
                        onPress={handleSaveEnvironment}
                      >
                        <Text style={styles.saveButtonText}>Save</Text>
                      </TouchableOpacity>
                    </View>
                  </>
                ) : (
                  <>
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Type</Text>
                      <View style={styles.detailValue}>
                        <Ionicons 
                          name={selectedEnvironment?.type === 'indoor' ? 'home-outline' : 'sunny-outline'} 
                          size={20} 
                          color="#4CAF50" 
                        />
                        <Text style={styles.detailText}>
                          {selectedEnvironment?.type === 'indoor' ? 'Indoor' : 'Outdoor'}
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Location</Text>
                        <View style={styles.detailValue}>
                          <Ionicons name="location-outline" size={20} color="#4CAF50" />
                      <Text style={styles.detailText}>{selectedEnvironment?.location}</Text>
                        </View>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Plants</Text>
                        <View style={styles.detailValue}>
                          <Ionicons name="leaf-outline" size={20} color="#4CAF50" />
                      <Text style={styles.detailText}>
                        {selectedEnvironment ? 
                          `${plantCounts[selectedEnvironment.name] || 0} ${plantCounts[selectedEnvironment.name] === 1 ? 'plant' : 'plants'}` : 
                          '0 plants'}
                      </Text>
                        </View>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Temperature Range</Text>
                        <View style={styles.detailValue}>
                          <Ionicons name="thermometer-outline" size={20} color="#4CAF50" />
                      <Text style={styles.detailText}>
                        {selectedEnvironment?.temperature ? 
                          `${formatTemperature(convertTemperature(selectedEnvironment.temperature.min, selectedEnvironment.temperature.unit, temperatureUnit), temperatureUnit)} - ${formatTemperature(convertTemperature(selectedEnvironment.temperature.max, selectedEnvironment.temperature.unit, temperatureUnit), temperatureUnit)}` : 
                          'Not specified'}
                      </Text>
                        </View>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Humidity Range</Text>
                        <View style={styles.detailValue}>
                          <Ionicons name="water-outline" size={20} color="#4CAF50" />
                      <Text style={styles.detailText}>
                        {selectedEnvironment?.humidity.min}% - {selectedEnvironment?.humidity.max}%
                      </Text>
                        </View>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Light Intensity</Text>
                        <View style={styles.detailValue}>
                          <Ionicons name="sunny-outline" size={20} color="#4CAF50" />
                      <Text style={styles.detailText}>
                        {selectedEnvironment?.lightIntensity?.percentage ? 
                          `${Math.round(selectedEnvironment.lightIntensity.percentage)}%` : 
                          'Not specified'}
                      </Text>
                        </View>
                    </View>
                    
                    {selectedEnvironment?.notes && (
                      <View style={styles.detailSection}>
                        <Text style={styles.detailLabel}>Notes</Text>
                          <View style={styles.detailValue}>
                            <Ionicons name="document-text-outline" size={20} color="#4CAF50" />
                        <Text style={styles.detailText}>{selectedEnvironment.notes}</Text>
                          </View>
                      </View>
                    )}
                  </>
                )}
              </ScrollView>
              {!isEditing && (
                <View style={styles.modalFooter}>
                  <TouchableOpacity 
                    style={styles.editButton}
                    onPress={() => selectedEnvironment && handleEditEnvironment(selectedEnvironment)}
                  >
                    <Text style={styles.editButtonText}>Edit Environment</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
          </TouchableWithoutFeedback>
        </Modal>
        
        <Footer currentScreen="home" plantCount={plants.length} />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 120,
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  cardDescription: {
    fontSize: 14,
    color: '#4CAF50',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 6,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  emptyStateButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  environmentList: {
    gap: 12,
  },
  environmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  environmentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  environmentInfo: {
    flex: 1,
  },
  environmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  environmentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  environmentDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  environmentDetails: {
    flexDirection: 'row',
    gap: 12,
  },
  environmentDetail: {
    fontSize: 12,
    color: '#666',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalBody: {
    padding: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    padding: 16,
  },
  deleteButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#EF4444',
    marginRight: 8,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    marginLeft: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#10B981',
    marginLeft: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 60,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  typeOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  typeOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  typeOptionText: {
    fontSize: 16,
    color: '#4CAF50',
  },
  typeOptionTextSelected: {
    color: '#fff',
  },
  rangeInputs: {
    flexDirection: 'row',
    gap: 12,
  },
  rangeInput: {
    flex: 1,
  },
  rangeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  unitSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
  },
  unitOption: {
    padding: 8,
    borderRadius: 4,
  },
  unitOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  unitOptionText: {
    fontSize: 14,
    color: '#666',
  },
  unitOptionTextSelected: {
    color: '#fff',
  },
  unitText: {
    fontSize: 14,
    color: '#666',
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  slider: {
    flex: 1,
    height: 40,
  },
  sliderValue: {
    width: 50,
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    textAlign: 'right',
  },
  detailSection: {
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 16,
    color: '#333',
  },
  editButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#10B981',
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  notificationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-start',
    alignItems: 'center',
    zIndex: 9999,
    pointerEvents: 'box-none',
  },
  notification: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    width: '90%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  inlineNotification: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    marginTop: 12,
    marginBottom: 8,
    borderRadius: 6,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  errorNotification: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  successNotification: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    borderColor: 'rgba(16, 185, 129, 0.3)',
  },
  notificationText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    flex: 1,
  },
  plantCountBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    gap: 4,
  },
  plantCountText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
}); 