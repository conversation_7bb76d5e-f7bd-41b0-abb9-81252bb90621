/* Global styles for React Native Web */
input {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

input:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Target the specific class from the error message */
.css-textinput-11aywtz {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Target any React Native Web input */
[class*="css-textinput-"] {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
} 