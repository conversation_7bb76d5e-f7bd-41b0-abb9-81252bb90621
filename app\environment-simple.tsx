import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Modal, Alert, TextInput, Image } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import Header from '../components/Header';
import Footer from '../components/Footer';

interface Environment {
  id: string;
  name: string;
  type: 'indoor' | 'outdoor';
  description: string;
  temperature: {
    min: number;
    max: number;
    unit: 'C' | 'F';
  };
  humidity: {
    min: number;
    max: number;
    unit: '%';
  };
  lightIntensity: {
    level: 'low' | 'medium' | 'high' | 'very_high';
    description: string;
  };
  notes: string;
}

// Using a simpler storage key
const STORAGE_KEY = '@environments_simple';
const TEMP_UNIT_PREFERENCE_KEY = '@temperature_unit_preference';

const defaultEnvironments: Environment[] = [
  {
    id: '1',
    name: 'Indoor Tent',
    type: 'indoor',
    description: 'Standard 4x4 grow tent with LED lighting',
    temperature: {
      min: 20,
      max: 28,
      unit: 'C'
    },
    humidity: {
      min: 40,
      max: 60,
      unit: '%'
    },
    lightIntensity: {
      level: 'high',
      description: 'Strong LED lighting system'
    },
    notes: 'Ventilation system with carbon filter'
  },
  {
    id: '2',
    name: 'Outdoor Garden',
    type: 'outdoor',
    description: 'Sunny backyard garden with partial shade',
    temperature: {
      min: 15,
      max: 35,
      unit: 'C'
    },
    humidity: {
      min: 30,
      max: 70,
      unit: '%'
    },
    lightIntensity: {
      level: 'very_high',
      description: 'Full sun with partial shade'
    },
    notes: 'Natural sunlight with wind protection'
  }
];

export default function EnvironmentSimpleScreen() {
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [selectedEnvironment, setSelectedEnvironment] = useState<Environment | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [temperatureUnit, setTemperatureUnit] = useState<'C' | 'F'>('F');
  
  // Form state
  const [name, setName] = useState('');
  const [type, setType] = useState<'indoor' | 'outdoor'>('indoor');
  const [description, setDescription] = useState('');
  const [temperature, setTemperature] = useState({ min: 20, max: 28, unit: 'C' as 'C' | 'F' });
  const [humidity, setHumidity] = useState({ min: 40, max: 60, unit: '%' });
  const [lightIntensity, setLightIntensity] = useState({ level: 'medium' as 'low' | 'medium' | 'high' | 'very_high', description: 'Standard lighting' });
  const [notes, setNotes] = useState('');
  
  const router = useRouter();

  useEffect(() => {
    loadEnvironments();
    loadTemperatureUnitPreference();
  }, []);

  const loadTemperatureUnitPreference = async () => {
    try {
      const savedUnit = await AsyncStorage.getItem(TEMP_UNIT_PREFERENCE_KEY);
      if (savedUnit === 'C' || savedUnit === 'F') {
        setTemperatureUnit(savedUnit);
      }
    } catch (error) {
      console.error('Error loading temperature unit preference:', error);
    }
  };

  const saveTemperatureUnitPreference = async (unit: 'C' | 'F') => {
    try {
      await AsyncStorage.setItem(TEMP_UNIT_PREFERENCE_KEY, unit);
      setTemperatureUnit(unit);
    } catch (error) {
      console.error('Error saving temperature unit preference:', error);
    }
  };

  const convertTemperature = (temp: number, fromUnit: 'C' | 'F', toUnit: 'C' | 'F'): number => {
    if (fromUnit === toUnit) return temp;
    if (fromUnit === 'C' && toUnit === 'F') return (temp * 9/5) + 32;
    if (fromUnit === 'F' && toUnit === 'C') return (temp - 32) * 5/9;
    return temp;
  };

  const formatTemperature = (temp: number, unit: 'C' | 'F'): string => {
    return `${Math.round(temp)}°${unit}`;
  };

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => prev + '\n' + info);
    console.log(info);
  };

  const loadEnvironments = async () => {
    try {
      addDebugInfo('Loading environments from AsyncStorage...');
      const storedEnvironments = await AsyncStorage.getItem(STORAGE_KEY);
      addDebugInfo(`Stored data: ${storedEnvironments || 'null'}`);
      
      if (storedEnvironments) {
        try {
          const parsedEnvironments = JSON.parse(storedEnvironments);
          addDebugInfo(`Parsed environments: ${JSON.stringify(parsedEnvironments)}`);
          
          // Verify that parsedEnvironments is an array
          if (Array.isArray(parsedEnvironments)) {
            setEnvironments(parsedEnvironments);
            addDebugInfo(`Successfully loaded ${parsedEnvironments.length} environments`);
          } else {
            addDebugInfo('Parsed data is not an array, setting default environments');
            setEnvironments(defaultEnvironments);
            await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultEnvironments));
          }
        } catch (parseError) {
          addDebugInfo(`Error parsing stored data: ${parseError}`);
          setEnvironments(defaultEnvironments);
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultEnvironments));
        }
      } else {
        addDebugInfo('No stored environments found, setting default environments');
        setEnvironments(defaultEnvironments);
        // Save default environments
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultEnvironments));
      }
    } catch (error) {
      addDebugInfo(`Error loading: ${error}`);
      setEnvironments(defaultEnvironments);
    }
  };

  const handleEnvironmentPress = (environment: Environment) => {
    setSelectedEnvironment(environment);
    setIsEditing(false);
    setIsModalVisible(true);
  };

  const handleAddEnvironment = () => {
    setSelectedEnvironment(null);
    setIsEditing(true);
    resetForm();
    setIsModalVisible(true);
  };

  const handleEditEnvironment = (environment: Environment) => {
    setIsEditing(true);
    setSelectedEnvironment(environment);
    setName(environment.name);
    setType(environment.type);
    setDescription(environment.description);
    setTemperature(environment.temperature);
    setHumidity(environment.humidity);
    setLightIntensity(environment.lightIntensity);
    setNotes(environment.notes);
  };

  const handleSaveEnvironment = async () => {
    console.log('Save button pressed');
    addDebugInfo('Save button pressed');
    
    if (!name || !description) {
      console.log('Validation failed:', { name, description });
      addDebugInfo(`Validation failed - Name: ${name}, Description: ${description}`);
      Alert.alert('Error', 'Please enter both name and description');
      return;
    }

    try {
      addDebugInfo('Starting environment save process...');
      addDebugInfo(`Current form values - Name: ${name}, Type: ${type}, Description: ${description}`);
      console.log('Current form values:', { name, type, description });
      
      let updatedEnvironments: Environment[];
      
      if (selectedEnvironment) {
        // Update existing environment
        addDebugInfo(`Updating existing environment with ID: ${selectedEnvironment.id}`);
        console.log('Updating environment:', selectedEnvironment.id);
        updatedEnvironments = environments.map(env => 
          env.id === selectedEnvironment.id 
            ? {
                ...env,
                name,
                type,
                description,
                temperature,
                humidity: { ...humidity, unit: '%' as const },
                lightIntensity,
                notes
              }
            : env
        );
      } else {
        // Create new environment
        const newEnv: Environment = {
          id: Date.now().toString(),
          name,
          type: type || 'indoor',
          description,
          temperature,
          humidity: { ...humidity, unit: '%' as const },
          lightIntensity,
          notes
        };
        addDebugInfo(`Creating new environment with ID: ${newEnv.id}`);
        console.log('Creating new environment:', newEnv);
        updatedEnvironments = [...environments, newEnv];
      }
      
      addDebugInfo(`Total environments after update: ${updatedEnvironments.length}`);
      console.log('Updated environments:', updatedEnvironments);
      
      // First update state
      addDebugInfo('Updating React state...');
      setEnvironments(updatedEnvironments);
      
      // Then save to AsyncStorage
      addDebugInfo('Attempting to save to AsyncStorage...');
      const jsonValue = JSON.stringify(updatedEnvironments);
      addDebugInfo(`JSON string length: ${jsonValue.length}`);
      console.log('Saving to AsyncStorage:', jsonValue);
      
      await AsyncStorage.setItem(STORAGE_KEY, jsonValue);
      addDebugInfo('Successfully saved to AsyncStorage');
      
      // Verify the save
      const savedData = await AsyncStorage.getItem(STORAGE_KEY);
      addDebugInfo(`Verification - Read back from AsyncStorage: ${savedData ? 'Data exists' : 'No data found'}`);
      console.log('Verification - Read back data:', savedData);
      
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        addDebugInfo(`Verification - Parsed data: ${JSON.stringify(parsedData)}`);
        console.log('Verification - Parsed data:', parsedData);
      }
      
      // Reset form and close modal
      resetForm();
      setIsModalVisible(false);
      
      Alert.alert('Success', 'Environment saved successfully');
    } catch (error) {
      console.error('Error in handleSaveEnvironment:', error);
      addDebugInfo(`Error in handleSaveEnvironment: ${error}`);
      Alert.alert('Error', 'Failed to save environment');
    }
  };

  const handleDeleteEnvironment = (id: string) => {
    console.log('Delete button clicked for environment ID:', id);
    
    // Filter out the environment to delete
    const updatedEnvironments = environments.filter(env => env.id !== id);
    console.log('Updated environments count:', updatedEnvironments.length);
    
    // Update state first
    setEnvironments(updatedEnvironments);
    console.log('State updated with filtered environments');
    
    // Then save to AsyncStorage
    const jsonValue = JSON.stringify(updatedEnvironments);
    console.log('Saving to AsyncStorage, JSON length:', jsonValue.length);
    
    AsyncStorage.setItem(STORAGE_KEY, jsonValue)
      .then(() => {
        console.log('Successfully saved to AsyncStorage');
        // Close the modal
        setIsModalVisible(false);
        setSelectedEnvironment(null);
        setIsEditing(false);
        resetForm();
        console.log('Modal closed');
        
        // Show success message
        Alert.alert('Success', 'Environment deleted successfully');
      })
      .catch(error => {
        console.error('Error saving to AsyncStorage:', error);
        Alert.alert('Error', 'Failed to save changes');
      });
  };

  const resetForm = () => {
    setSelectedEnvironment(null);
    setName('');
    setType('indoor');
    setDescription('');
    setTemperature({ min: 20, max: 28, unit: 'C' });
    setHumidity({ min: 40, max: 60, unit: '%' });
    setLightIntensity({ level: 'medium', description: 'Standard lighting' });
    setNotes('');
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSelectedEnvironment(null);
    setIsEditing(false);
    resetForm();
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Environments</Text>
            <Text style={styles.heroDescription}>Manage your growing spaces</Text>
          </View>
        </View>
        
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <View>
                  <Text style={styles.cardTitle}>Your Environments</Text>
                  <Text style={styles.cardDescription}>Manage your growing spaces</Text>
                </View>
                <TouchableOpacity 
                  style={styles.addButton}
                  onPress={handleAddEnvironment}
                >
                  <Ionicons name="add" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              {environments.length === 0 ? (
                <View style={styles.emptyState}>
                  <Ionicons name="leaf-outline" size={48} color="#4CAF50" />
                  <Text style={styles.emptyStateText}>No environments yet</Text>
                  <Text style={styles.emptyStateDescription}>
                    Add your first growing environment to get started
                  </Text>
                  <TouchableOpacity 
                    style={styles.emptyStateButton}
                    onPress={handleAddEnvironment}
                  >
                    <Text style={styles.emptyStateButtonText}>Add Environment</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.environmentList}>
                  {environments.map(environment => (
                    <TouchableOpacity 
                      key={environment.id}
                      style={styles.environmentItem}
                      onPress={() => handleEnvironmentPress(environment)}
                    >
                      <View style={styles.environmentIcon}>
                        <Ionicons 
                          name={environment.type === 'indoor' ? 'home-outline' : 'sunny-outline'} 
                          size={24} 
                          color="#4CAF50" 
                        />
                      </View>
                      <View style={styles.environmentInfo}>
                        <Text style={styles.environmentName}>{environment.name}</Text>
                        <Text style={styles.environmentDescription}>{environment.description}</Text>
                        <View style={styles.environmentDetails}>
                          <Text style={styles.environmentDetail}>
                            <Ionicons name="thermometer-outline" size={16} color="#666" /> {formatTemperature(convertTemperature(environment.temperature.min, environment.temperature.unit, temperatureUnit), temperatureUnit)} - {formatTemperature(convertTemperature(environment.temperature.max, environment.temperature.unit, temperatureUnit), temperatureUnit)}
                          </Text>
                          <Text style={styles.environmentDetail}>
                            <Ionicons name="water-outline" size={16} color="#666" /> {environment.humidity.min}% - {environment.humidity.max}%
                          </Text>
                        </View>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        </ScrollView>
        
        <Modal
          animationType="slide"
          transparent={true}
          visible={isModalVisible}
          onRequestClose={closeModal}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {isEditing ? (selectedEnvironment ? 'Edit Environment' : 'Add Environment') : 'Environment Details'}
                </Text>
                <TouchableOpacity onPress={closeModal}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.modalBody}>
                {isEditing ? (
                  <>
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Name</Text>
                      <TextInput
                        style={styles.input}
                        value={name}
                        onChangeText={setName}
                        placeholder="Enter environment name"
                      />
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Type</Text>
                      <View style={styles.typeSelector}>
                        <TouchableOpacity 
                          style={[
                            styles.typeOption, 
                            type === 'indoor' && styles.typeOptionSelected
                          ]}
                          onPress={() => setType('indoor')}
                        >
                          <Ionicons 
                            name="home-outline" 
                            size={20} 
                            color={type === 'indoor' ? '#fff' : '#4CAF50'} 
                          />
                          <Text 
                            style={[
                              styles.typeOptionText,
                              type === 'indoor' && styles.typeOptionTextSelected
                            ]}
                          >
                            Indoor
                          </Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                          style={[
                            styles.typeOption, 
                            type === 'outdoor' && styles.typeOptionSelected
                          ]}
                          onPress={() => setType('outdoor')}
                        >
                          <Ionicons 
                            name="sunny-outline" 
                            size={20} 
                            color={type === 'outdoor' ? '#fff' : '#4CAF50'} 
                          />
                          <Text 
                            style={[
                              styles.typeOptionText,
                              type === 'outdoor' && styles.typeOptionTextSelected
                            ]}
                          >
                            Outdoor
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Description</Text>
                      <TextInput
                        style={[styles.input, styles.textArea]}
                        value={description}
                        onChangeText={setDescription}
                        placeholder="Enter environment description"
                        multiline
                        numberOfLines={3}
                      />
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Temperature Range</Text>
                      <View style={styles.rangeInputs}>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Min</Text>
                          <TextInput
                            style={styles.input}
                            value={temperature.min.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setTemperature({...temperature, min: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Max</Text>
                          <TextInput
                            style={styles.input}
                            value={temperature.max.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setTemperature({...temperature, max: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.unitSelector}>
                          <TouchableOpacity 
                            style={[
                              styles.unitOption, 
                              temperature.unit === 'C' && styles.unitOptionSelected
                            ]}
                            onPress={() => setTemperature({...temperature, unit: 'C'})}
                          >
                            <Text 
                              style={[
                                styles.unitOptionText,
                                temperature.unit === 'C' && styles.unitOptionTextSelected
                              ]}
                            >
                              °C
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity 
                            style={[
                              styles.unitOption, 
                              temperature.unit === 'F' && styles.unitOptionSelected
                            ]}
                            onPress={() => setTemperature({...temperature, unit: 'F'})}
                          >
                            <Text 
                              style={[
                                styles.unitOptionText,
                                temperature.unit === 'F' && styles.unitOptionTextSelected
                              ]}
                            >
                              °F
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Humidity Range</Text>
                      <View style={styles.rangeInputs}>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Min</Text>
                          <TextInput
                            style={styles.input}
                            value={humidity.min.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setHumidity({...humidity, min: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.rangeInput}>
                          <Text style={styles.rangeLabel}>Max</Text>
                          <TextInput
                            style={styles.input}
                            value={humidity.max.toString()}
                            onChangeText={(value) => {
                              const numValue = Number(value);
                              if (!isNaN(numValue)) {
                                setHumidity({...humidity, max: numValue});
                              }
                            }}
                            keyboardType="numeric"
                          />
                        </View>
                        <View style={styles.unitSelector}>
                          <Text style={styles.unitText}>%</Text>
                        </View>
                      </View>
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Light Intensity</Text>
                      <View style={styles.lightSelector}>
                        <TouchableOpacity 
                          style={[
                            styles.lightOption, 
                            lightIntensity.level === 'low' && styles.lightOptionSelected
                          ]}
                          onPress={() => setLightIntensity({...lightIntensity, level: 'low'})}
                        >
                          <Text 
                            style={[
                              styles.lightOptionText,
                              lightIntensity.level === 'low' && styles.lightOptionTextSelected
                            ]}
                          >
                            Low
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity 
                          style={[
                            styles.lightOption, 
                            lightIntensity.level === 'medium' && styles.lightOptionSelected
                          ]}
                          onPress={() => setLightIntensity({...lightIntensity, level: 'medium'})}
                        >
                          <Text 
                            style={[
                              styles.lightOptionText,
                              lightIntensity.level === 'medium' && styles.lightOptionTextSelected
                            ]}
                          >
                            Medium
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity 
                          style={[
                            styles.lightOption, 
                            lightIntensity.level === 'high' && styles.lightOptionSelected
                          ]}
                          onPress={() => setLightIntensity({...lightIntensity, level: 'high'})}
                        >
                          <Text 
                            style={[
                              styles.lightOptionText,
                              lightIntensity.level === 'high' && styles.lightOptionTextSelected
                            ]}
                          >
                            High
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity 
                          style={[
                            styles.lightOption, 
                            lightIntensity.level === 'very_high' && styles.lightOptionSelected
                          ]}
                          onPress={() => setLightIntensity({...lightIntensity, level: 'very_high'})}
                        >
                          <Text 
                            style={[
                              styles.lightOptionText,
                              lightIntensity.level === 'very_high' && styles.lightOptionTextSelected
                            ]}
                          >
                            Very High
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <TextInput
                        style={[styles.input, styles.textArea]}
                        value={lightIntensity.description}
                        onChangeText={(value) => setLightIntensity({...lightIntensity, description: value})}
                        placeholder="Enter light intensity description"
                        multiline
                        numberOfLines={2}
                      />
                    </View>
                    
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Notes</Text>
                      <TextInput
                        style={[styles.input, styles.textArea]}
                        value={notes}
                        onChangeText={setNotes}
                        placeholder="Enter additional notes"
                        multiline
                        numberOfLines={4}
                      />
                    </View>
                    
                    <View style={styles.modalFooter}>
                      {selectedEnvironment && (
                        <TouchableOpacity 
                          style={[styles.deleteButton, { flex: 1, marginLeft: 8 }]}
                          onPress={() => handleDeleteEnvironment(selectedEnvironment.id)}
                        >
                          <Text style={styles.deleteButtonText}>Delete Environment</Text>
                        </TouchableOpacity>
                      )}
                      <TouchableOpacity 
                        style={styles.saveButton}
                        onPress={handleSaveEnvironment}
                      >
                        <Text style={styles.saveButtonText}>Save Environment</Text>
                      </TouchableOpacity>
                    </View>
                  </>
                ) : (
                  <>
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Type</Text>
                      <View style={styles.detailValue}>
                        <Ionicons 
                          name={selectedEnvironment?.type === 'indoor' ? 'home-outline' : 'sunny-outline'} 
                          size={20} 
                          color="#4CAF50" 
                        />
                        <Text style={styles.detailText}>
                          {selectedEnvironment?.type === 'indoor' ? 'Indoor' : 'Outdoor'}
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Description</Text>
                      <Text style={styles.detailText}>{selectedEnvironment?.description}</Text>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Temperature Range</Text>
                      <Text style={styles.detailText}>
                        {selectedEnvironment?.temperature ? 
                          `${formatTemperature(convertTemperature(selectedEnvironment.temperature.min, selectedEnvironment.temperature.unit, temperatureUnit), temperatureUnit)} - ${formatTemperature(convertTemperature(selectedEnvironment.temperature.max, selectedEnvironment.temperature.unit, temperatureUnit), temperatureUnit)}` : 
                          'Not specified'}
                      </Text>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Humidity Range</Text>
                      <Text style={styles.detailText}>
                        {selectedEnvironment?.humidity.min}% - {selectedEnvironment?.humidity.max}%
                      </Text>
                    </View>
                    
                    <View style={styles.detailSection}>
                      <Text style={styles.detailLabel}>Light Intensity</Text>
                      <Text style={styles.detailText}>
                        {selectedEnvironment?.lightIntensity?.level ? 
                          selectedEnvironment.lightIntensity.level.charAt(0).toUpperCase() + 
                          selectedEnvironment.lightIntensity.level.slice(1).replace('_', ' ') : 
                          'Not specified'}
                      </Text>
                      <Text style={styles.detailDescription}>
                        {selectedEnvironment?.lightIntensity?.description || 'No description provided'}
                      </Text>
                    </View>
                    
                    {selectedEnvironment?.notes && (
                      <View style={styles.detailSection}>
                        <Text style={styles.detailLabel}>Notes</Text>
                        <Text style={styles.detailText}>{selectedEnvironment.notes}</Text>
                      </View>
                    )}
                  </>
                )}
              </ScrollView>
              {!isEditing && (
                <View style={styles.modalFooter}>
                  <View style={styles.modalFooterButtons}>
                    <TouchableOpacity 
                      style={[styles.editButton, { flex: 1, marginRight: 8 }]}
                      onPress={() => selectedEnvironment && handleEditEnvironment(selectedEnvironment)}
                    >
                      <Text style={styles.editButtonText}>Edit Environment</Text>
                    </TouchableOpacity>
                    {selectedEnvironment && (
                      <TouchableOpacity 
                        style={[styles.deleteButton, { flex: 1, marginLeft: 8 }]}
                        onPress={() => handleDeleteEnvironment(selectedEnvironment.id)}
                      >
                        <Text style={styles.deleteButtonText}>Delete Environment</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              )}
            </View>
          </View>
        </Modal>
        
        <Footer currentScreen="settings" />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 120,
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  cardDescription: {
    fontSize: 14,
    color: '#4CAF50',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  emptyStateButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  environmentList: {
    gap: 12,
  },
  environmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  environmentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  environmentInfo: {
    flex: 1,
  },
  environmentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  environmentDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  environmentDetails: {
    flexDirection: 'row',
    gap: 12,
  },
  environmentDetail: {
    fontSize: 12,
    color: '#666',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalBody: {
    padding: 16,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  modalFooterButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  typeOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  typeOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  typeOptionText: {
    fontSize: 16,
    color: '#4CAF50',
  },
  typeOptionTextSelected: {
    color: '#fff',
  },
  rangeInputs: {
    flexDirection: 'row',
    gap: 12,
  },
  rangeInput: {
    flex: 1,
  },
  rangeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  unitSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
  },
  unitOption: {
    padding: 8,
    borderRadius: 4,
  },
  unitOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  unitOptionText: {
    fontSize: 14,
    color: '#666',
  },
  unitOptionTextSelected: {
    color: '#fff',
  },
  unitText: {
    fontSize: 14,
    color: '#666',
  },
  lightSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
  },
  lightOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  lightOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  lightOptionText: {
    fontSize: 14,
    color: '#4CAF50',
  },
  lightOptionTextSelected: {
    color: '#fff',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  editButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  editButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  deleteButton: {
    backgroundColor: '#f44336',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  deleteButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  detailSection: {
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 16,
    color: '#333',
  },
  detailDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
}); 