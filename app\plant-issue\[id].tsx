import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity, Platform, ImageSourcePropType, ActivityIndicator, Modal, Pressable } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';

interface PlantIssue {
  id: string;
  title: string;
  description: string;
  symptoms: string[];
  solutions: string[];
  images: string[];
  source: string;
  icon: string;
  color: string;
}

const PLANT_ISSUES: { [key: string]: PlantIssue } = {
  'overwatering': {
    id: 'overwatering',
    title: 'Overwatering',
    description: 'Overwatering is one of the most common causes of plant problems. When plants receive too much water, their roots become deprived of oxygen, leading to root rot and other serious issues.',
    symptoms: [
      'Drooping / Curling is the first sign of overwatered plants',
      'Plants start drooping soon after watering',
      'Leaves are firm and curled down all the way from the stem to the leaf',
      'Yellowing leaves that feel soft and limp',
      'Brown, mushy roots with a foul odor',
      'Water-soaked spots and blisters (Oedema) on stems and leaves',
      'Leaf drop, especially of older leaves',
      'Stunted growth and wilting despite wet soil',
      'Mold or algae growth on soil surface',
      'Will eventually lead to leaf yellowing and other signs of nutrient problems if not corrected'
    ],
    solutions: [
      'Wait until the top of the growing medium is dry about an inch deep (up to your first knuckle)',
      'Add water until you see some at least 20% extra runoff water drain out the bottom of your pot',
      'If top of growing medium stays wet for a long time, you may need to give your plants less water at a time, or improve your drainage',
      'The goal is to be watering your plants every 2-3 days. If it needs longer to dry out, you should be giving less water at a time',
      'Ensure proper drainage by using well-draining soil and pots with drainage holes',
      'Remove any standing water from saucers or trays',
      'Consider repotting with fresh, well-draining soil if root rot is suspected',
      'Use a moisture meter to accurately gauge soil moisture levels',
      'Adjust watering schedule based on season, temperature, and plant growth stage',
      'If your plants are already overwatered, increase temperature and airflow to help water evaporate more quickly',
      'Use a pencil to gently poke some air holes into the growing medium to provide extra aeration and oxygen to the roots'
    ],
    images: ['overwatering-1.jpg', 'overwatering-2.jpg', 'overwatering-3.jpg'],
    source: 'Grow Weed Easy',
    icon: 'water',
    color: '#4CAF50'
  },
  'light': {
    id: 'light',
    title: 'Light Burn or Light Stress',
    description: 'Your cannabis plant can only withstand a certain amount of light. After a certain point, your cannabis will start turning yellow or otherwise exhibit signs of stress on the leaves near the sources of light and/or heat.',
    symptoms: [
      'Yellow leaves at the top of the plant directly under grow lights',
      'Leaves pointing up or "praying" (first sign of too much light)',
      'Yellowing with inside veins staying green',
      'Yellow leaves won\'t fall off or be plucked off easily',
      'Edges of leaves turning up and tips turning yellow',
      'Leaves becoming crispy and breaking off if bent',
      'Droopy colas (tallest buds) when light is too intense',
      'Leaves turning red or purple instead of yellow',
      'Brown spotting on leaves',
      'Light bleaching of buds (white or bleached appearance)',
      'Buds with brown or yellow sugar leaves'
    ],
    solutions: [
      'Move grow lights further away from the tops of plants',
      'Reduce power of grow lights if they can\'t be moved',
      'Bend plants over so tops are further from lights',
      'Consider cutting off the top of the plant if in vegetative stage',
      'Make environmental changes slowly to avoid shock',
      'Ensure proper temperature control alongside light management',
      'For outdoor plants, provide shade when moving from indoors to full sunlight',
      'Monitor for signs of heat stress which can look similar to light stress',
      'Keep plants healthy as nutrient deficiencies make light burn worse',
      'Adjust light distance based on plant growth stage (flowering plants are more sensitive)'
    ],
    images: ['light-1.jpg', 'light-2.jpg', 'light-3.jpg'],
    source: 'Grow Weed Easy',
    icon: 'sunny',
    color: '#FFC107'
  },
  'nutrients': {
    id: 'nutrients',
    title: 'Nutrient Deficiency',
    description: 'Plants require various nutrients for healthy growth. Deficiencies can occur when essential elements are lacking in the soil or when plants cannot absorb them properly.',
    symptoms: [
      'Yellowing of older leaves (nitrogen deficiency)',
      'Purple or red discoloration (phosphorus deficiency)',
      'Yellowing and browning of leaf edges (potassium deficiency)',
      'Yellowing between leaf veins (iron deficiency)',
      'Stunted growth and poor overall health'
    ],
    solutions: [
      'Use a balanced fertilizer appropriate for your plant type',
      'Test soil pH and adjust if necessary',
      'Add organic matter to improve soil quality',
      'Consider using slow-release fertilizers',
      'Follow proper fertilization schedules',
      'Address any soil drainage issues'
    ],
    images: ['nutrients-1.jpg', 'nutrients-2.jpg', 'nutrients-3.jpg'],
    source: 'Missouri Botanical Garden',
    icon: 'leaf',
    color: '#9C27B0'
  },
  'pests': {
    id: 'pests',
    title: 'Pests',
    description: 'Various insects and mites can damage plants by feeding on leaves, stems, and roots. Early detection and proper treatment are essential for controlling pest infestations.',
    symptoms: [
      'Visible insects on plants',
      'Holes or chewed areas in leaves',
      'Sticky residue (honeydew) on leaves',
      'Fine webs on plant surfaces',
      'Distorted or curled leaves',
      'Stunted growth'
    ],
    solutions: [
      'Regularly inspect plants for signs of pests',
      'Use insecticidal soap or neem oil for treatment',
      'Remove heavily infested leaves or stems',
      'Improve air circulation around plants',
      'Keep plants healthy to prevent pest attacks',
      'Consider using beneficial insects for biological control'
    ],
    images: ['pests-1.jpg', 'pests-2.jpg', 'pests-3.jpg'],
    source: 'Missouri Botanical Garden',
    icon: 'bug',
    color: '#F44336'
  },
  'bronze-brown-patches': {
    id: 'bronze-brown-patches',
    title: 'Bronze or brown patches',
    description: 'Discolored patches appearing on plant surfaces',
    symptoms: [
      'Bronze or brown discoloration on leaves or stems',
      'Patches may be irregular in shape',
      'Discoloration may spread over time',
      'Affected areas may feel dry or crispy'
    ],
    solutions: [
      'Check for nutrient deficiencies',
      'Monitor watering practices',
      'Ensure proper light exposure',
      'Inspect for pest damage',
      'Consider environmental stress factors'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'color-palette',
    color: '#8D6E63'
  },
  'brown-slimy-roots': {
    id: 'brown-slimy-roots',
    title: 'Brown or slimy roots',
    description: 'Root system showing signs of decay or disease',
    symptoms: [
      'Brown or black discoloration of roots',
      'Slimy or mushy texture when touched',
      'Foul odor emanating from the root zone',
      'Roots breaking off easily when handled',
      'Reduced root mass compared to healthy plants',
      'Wilting despite adequate watering'
    ],
    solutions: [
      'Improve drainage to prevent waterlogging',
      'Reduce watering frequency and amount',
      'Consider repotting with fresh, well-draining soil',
      'Trim away damaged roots before repotting',
      'Use a fungicide treatment if fungal infection is suspected',
      'Ensure proper aeration in the root zone',
      'Check for root-bound conditions and repot if necessary',
      'Monitor for signs of recovery after treatment'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'water',
    color: '#795548'
  },
  'brown-yellow-tips': {
    id: 'brown-yellow-tips',
    title: 'Brown or yellow leaf tips/edges',
    description: 'Leaf edges or tips showing discoloration',
    symptoms: [
      'Brown or yellow discoloration at leaf tips or edges',
      'Crispy or dry texture in affected areas',
      'Discoloration may spread inward over time',
      'Multiple leaves affected, often starting with older leaves',
      'May be accompanied by leaf curling'
    ],
    solutions: [
      'Check for over-fertilization and flush soil if necessary',
      'Ensure proper watering practices (not too little, not too much)',
      'Monitor humidity levels, especially for indoor plants',
      'Check for salt buildup in soil and flush if needed',
      'Ensure proper light exposure (not too intense)',
      'Consider environmental factors like drafts or heat sources',
      'Trim affected areas to prevent spread if necessary',
      'Address any underlying nutrient imbalances'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'leaf',
    color: '#FFA000'
  },
  'buds-dying': {
    id: 'buds-dying',
    title: 'Buds dying',
    description: 'Flower buds failing to develop or dying off',
    symptoms: [
      'Buds turning brown or black',
      'Buds failing to open or develop properly',
      'Buds dropping off the plant prematurely',
      'Discoloration starting at the tip and spreading',
      'Soft or mushy texture in affected buds',
      'Lack of new bud formation'
    ],
    solutions: [
      'Check for overwatering which can cause bud rot',
      'Ensure proper air circulation around buds',
      'Monitor humidity levels (high humidity can promote fungal growth)',
      'Inspect for pest damage or disease',
      'Ensure proper nutrient balance, especially phosphorus during flowering',
      'Check for light burn if buds are close to grow lights',
      'Consider environmental stress factors like temperature extremes',
      'Remove affected buds to prevent spread of disease'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'flower',
    color: '#D32F2F'
  },
  'buds-look-odd': {
    id: 'buds-look-odd',
    title: 'Buds look odd',
    description: 'Abnormal appearance or development of buds',
    symptoms: [
      'Misshapen or deformed buds',
      'Unusual coloring or patterning',
      'Foxtailing (abnormal elongation of buds)',
      'Buds with unusual texture or density',
      'Buds developing in unexpected locations',
      'Buds with abnormal growth patterns'
    ],
    solutions: [
      'Check for light stress or light leaks during dark period',
      'Monitor temperature fluctuations, especially during flowering',
      'Ensure proper nutrient balance, particularly during bud development',
      'Check for genetic factors or strain characteristics',
      'Inspect for pest damage or disease',
      'Consider environmental stress factors',
      'Ensure proper light cycle timing',
      'Check for hormone imbalances or plant stress'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'alert-circle',
    color: '#7B1FA2'
  },
  'bugs-visible': {
    id: 'bugs-visible',
    title: 'Bugs are visible',
    description: 'Pests or insects visible on the plant',
    symptoms: [
      'Visible insects on leaves, stems, or soil',
      'Small moving dots on plant surfaces',
      'Webbing or silk-like material on plants',
      'Sticky residue (honeydew) on leaves',
      'Small white, black, brown, or colored insects',
      'Insects flying around the plant when disturbed'
    ],
    solutions: [
      'Identify the specific pest for targeted treatment',
      'Use insecticidal soap or neem oil for treatment',
      'Consider biological controls like beneficial insects',
      'Remove heavily infested leaves or stems',
      'Improve air circulation around plants',
      'Check for and eliminate pest breeding sites',
      'Isolate affected plants to prevent spread',
      'Maintain plant health to improve resistance to pests'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'bug',
    color: '#388E3C'
  },
  'curling-clawing-leaves': {
    id: 'curling-clawing-leaves',
    title: 'Curling or clawing leaves',
    description: 'Leaves showing abnormal curling or clawing',
    symptoms: [
      'Leaves curling upward or downward',
      'Leaf edges turning inward or outward',
      'Leaves taking on a claw-like appearance',
      'Discoloration may accompany curling',
      'Multiple leaves affected, often starting with newer growth',
      'Leaves may feel stiff or leathery'
    ],
    solutions: [
      'Check for overwatering or underwatering',
      'Monitor nutrient levels, especially nitrogen',
      'Ensure proper light exposure (not too intense)',
      'Check for heat stress or temperature issues',
      'Inspect for pest damage or disease',
      'Consider environmental stress factors',
      'Adjust pH if necessary',
      'Flush soil if nutrient lockout is suspected'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'git-branch',
    color: '#00796B'
  },
  'dark-leaves': {
    id: 'dark-leaves',
    title: 'Dark leaves',
    description: 'Leaves appearing darker than normal',
    symptoms: [
      'Leaves taking on a darker green or purple hue',
      'Darkening may be uniform or patchy',
      'Leaves may appear glossy or shiny',
      'Darkening may affect entire plant or specific leaves',
      'May be accompanied by other symptoms like curling',
      'Dark areas may feel thicker or leathery'
    ],
    solutions: [
      'Check for nitrogen excess (dark green leaves)',
      'Monitor phosphorus levels (purple leaves can indicate deficiency)',
      'Ensure proper light exposure',
      'Check temperature conditions (cold can cause purple coloring)',
      'Inspect for genetic factors (some strains naturally have darker leaves)',
      'Consider environmental stress factors',
      'Adjust nutrient regimen if necessary',
      'Monitor for other symptoms that may develop'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'contrast',
    color: '#424242'
  },
  'drooping-plant': {
    id: 'drooping-plant',
    title: 'Drooping plant',
    description: 'Plant showing signs of wilting or drooping',
    symptoms: [
      'Leaves and stems hanging downward',
      'Lack of turgor (firmness) in plant tissues',
      'Plant appears limp or wilted',
      'Drooping may affect entire plant or specific branches',
      'Leaves may feel soft or floppy',
      'Plant may recover temporarily after watering'
    ],
    solutions: [
      'Check for underwatering and water thoroughly if soil is dry',
      'Inspect for overwatering if soil is consistently wet',
      'Ensure proper drainage to prevent waterlogging',
      'Check for root damage or disease',
      'Monitor temperature conditions (heat stress can cause wilting)',
      'Inspect for pest damage',
      'Consider environmental stress factors',
      'Ensure proper light exposure (not too intense)'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'trending-down',
    color: '#5D4037'
  },
  'holes-in-leaves': {
    id: 'holes-in-leaves',
    title: 'Holes in leaves',
    description: 'Leaves with holes or damage',
    symptoms: [
      'Circular or irregular holes in leaves',
      'Chewed edges or missing sections of leaves',
      'Holes may be scattered or concentrated in specific areas',
      'Damage may appear on new or old growth',
      'May be accompanied by other signs of pest activity',
      'Holes may have brown or yellow edges'
    ],
    solutions: [
      'Inspect for visible pests (caterpillars, beetles, etc.)',
      'Check for slug or snail damage (especially in outdoor gardens)',
      'Look for signs of larger pests (birds, rodents)',
      'Consider using physical barriers or traps',
      'Apply appropriate pest control measures',
      'Remove heavily damaged leaves',
      'Monitor for new damage after treatment',
      'Consider companion planting to deter pests'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'remove-circle',
    color: '#689F38'
  },
  'mold-powder': {
    id: 'mold-powder',
    title: 'Mold or powder',
    description: 'Powdery or moldy growth on plant surfaces',
    symptoms: [
      'White or gray powdery coating on leaves, stems, or buds',
      'Powdery substance that can be rubbed off',
      'May appear as spots that spread and merge',
      'Affected areas may turn yellow or brown over time',
      'Leaves may become distorted or stunted',
      'Buds may develop mold, especially in dense areas'
    ],
    solutions: [
      'Improve air circulation around plants',
      'Reduce humidity levels, especially in indoor environments',
      'Remove affected leaves or plant parts',
      'Apply appropriate fungicide treatments',
      'Ensure proper spacing between plants',
      'Avoid overhead watering that wets leaves',
      'Monitor for signs of spread after treatment',
      'Consider preventive measures for future grows'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'snow',
    color: '#90A4AE'
  },
  'pink-purple-leaves': {
    id: 'pink-purple-leaves',
    title: 'Pink or purple on leaves',
    description: 'Unusual pink or purple discoloration on leaves',
    symptoms: [
      'Pink or purple coloring on leaves or stems',
      'Discoloration may be uniform or appear as spots or streaks',
      'May affect entire plant or specific leaves',
      'Coloring may be more intense on the underside of leaves',
      'May be accompanied by other symptoms like curling',
      'Discoloration may develop gradually or appear suddenly'
    ],
    solutions: [
      'Check for phosphorus deficiency (purple leaves)',
      'Monitor temperature conditions (cold can cause purple coloring)',
      'Inspect for genetic factors (some strains naturally have purple coloring)',
      'Ensure proper light exposure',
      'Check pH levels and adjust if necessary',
      'Consider environmental stress factors',
      'Adjust nutrient regimen if needed',
      'Monitor for other symptoms that may develop'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'color-filter',
    color: '#E91E63'
  },
  'red-stems': {
    id: 'red-stems',
    title: 'Red stems',
    description: 'Stems showing unusual red coloring',
    symptoms: [
      'Red or purple coloring on stems or branches',
      'Coloring may be uniform or appear as streaks',
      'May affect entire plant or specific stems',
      'Coloring may be more intense on the underside of stems',
      'May be accompanied by other symptoms like leaf discoloration',
      'Stems may feel woody or stiff'
    ],
    solutions: [
      'Check for phosphorus deficiency (red stems)',
      'Monitor temperature conditions (cold can cause red coloring)',
      'Inspect for genetic factors (some strains naturally have red stems)',
      'Ensure proper light exposure',
      'Check pH levels and adjust if necessary',
      'Consider environmental stress factors',
      'Adjust nutrient regimen if needed',
      'Monitor for other symptoms that may develop'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'git-branch',
    color: '#F44336'
  },
  'shiny-smooth-leaves': {
    id: 'shiny-smooth-leaves',
    title: 'Shiny or smooth leaves',
    description: 'Leaves with unusual shine or texture',
    symptoms: [
      'Leaves appearing unusually shiny or glossy',
      'Smooth texture that differs from normal leaf texture',
      'May affect entire plant or specific leaves',
      'Leaves may feel sticky or tacky to the touch',
      'May be accompanied by other symptoms like discoloration',
      'Shine may be more noticeable on the underside of leaves'
    ],
    solutions: [
      'Check for pest activity (some pests produce honeydew that makes leaves shiny)',
      'Inspect for disease that may alter leaf texture',
      'Monitor environmental conditions',
      'Check for genetic factors (some strains naturally have shinier leaves)',
      'Ensure proper nutrient balance',
      'Consider environmental stress factors',
      'Monitor for other symptoms that may develop',
      'Clean leaves gently if sticky residue is present'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'sunny',
    color: '#FFC107'
  },
  'spots-markings': {
    id: 'spots-markings',
    title: 'Spots or markings',
    description: 'Unusual spots or markings on plant surfaces',
    symptoms: [
      'Brown, yellow, white, or black spots on leaves or stems',
      'Spots may be circular, irregular, or have specific patterns',
      'Markings may have defined edges or fade into surrounding tissue',
      'Spots may grow in size or number over time',
      'Affected areas may feel different from healthy tissue',
      'May be accompanied by other symptoms like leaf drop'
    ],
    solutions: [
      'Identify the specific cause (disease, nutrient issue, pest damage, etc.)',
      'Remove affected leaves if the problem is localized',
      'Apply appropriate treatments based on the cause',
      'Improve air circulation around plants',
      'Ensure proper watering practices',
      'Check for nutrient imbalances',
      'Monitor for signs of spread after treatment',
      'Consider preventive measures for future grows'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'color-wand',
    color: '#9C27B0'
  },
  'twisted-growth': {
    id: 'twisted-growth',
    title: 'Twisted growth',
    description: 'Abnormal twisting or contortion of plant parts',
    symptoms: [
      'Stems or leaves growing in twisted or contorted patterns',
      'Abnormal curling or spiraling of plant parts',
      'Growth may appear stunted or distorted',
      'May affect entire plant or specific branches',
      'Twisting may be accompanied by other symptoms like discoloration',
      'New growth may appear normal while older growth remains twisted'
    ],
    solutions: [
      'Check for herbicide damage (common cause of twisted growth)',
      'Inspect for pest damage or disease',
      'Monitor environmental conditions',
      'Check for genetic factors or mutations',
      'Ensure proper nutrient balance',
      'Consider environmental stress factors',
      'Remove severely affected parts if necessary',
      'Monitor new growth for signs of recovery'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'sync',
    color: '#009688'
  },
  'webbing': {
    id: 'webbing',
    title: 'Webbing',
    description: 'Web-like structures on the plant',
    symptoms: [
      'Fine, silky webbing on leaves, stems, or between plant parts',
      'Webbing may be sparse or dense, covering large areas',
      'Small insects may be visible within or near the webbing',
      'Affected areas may show discoloration or damage',
      'Leaves may appear dusty or dirty',
      'Webbing may be more noticeable in the morning or under certain lighting'
    ],
    solutions: [
      'Identify the specific pest (likely spider mites)',
      'Increase humidity if possible (spider mites prefer dry conditions)',
      'Use a strong spray of water to dislodge mites and webbing',
      'Apply appropriate miticide treatments',
      'Consider biological controls like predatory mites',
      'Remove heavily infested leaves or plant parts',
      'Isolate affected plants to prevent spread',
      'Monitor for signs of recovery after treatment'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'globe',
    color: '#607D8B'
  },
  'wilting-leaves': {
    id: 'wilting-leaves',
    title: 'Wilting leaves',
    description: 'Leaves showing signs of wilting or dehydration',
    symptoms: [
      'Leaves appearing limp, droopy, or wrinkled',
      'Lack of turgor (firmness) in leaf tissues',
      'Wilting may affect entire plant or specific leaves',
      'Leaves may feel soft or papery',
      'Wilting may be more pronounced during hot periods',
      'Plant may recover temporarily after watering'
    ],
    solutions: [
      'Check for underwatering and water thoroughly if soil is dry',
      'Inspect for overwatering if soil is consistently wet',
      'Ensure proper drainage to prevent waterlogging',
      'Check for root damage or disease',
      'Monitor temperature conditions (heat stress can cause wilting)',
      'Inspect for pest damage',
      'Consider environmental stress factors',
      'Ensure proper light exposure (not too intense)'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'water',
    color: '#2196F3'
  },
  'yellow-between-veins': {
    id: 'yellow-between-veins',
    title: 'Yellow between leaf veins',
    description: 'Yellowing occurring between leaf veins',
    symptoms: [
      'Yellow discoloration between leaf veins while veins remain green',
      'Pattern may resemble a "fishbone" or "checkerboard" appearance',
      'Yellowing may affect entire leaf or specific areas',
      'May start on newer or older leaves depending on the cause',
      'Affected areas may eventually turn brown or die',
      'May be accompanied by other symptoms like leaf drop'
    ],
    solutions: [
      'Check for iron deficiency (common cause of interveinal yellowing)',
      'Monitor magnesium levels (can also cause interveinal yellowing)',
      'Ensure proper pH levels (nutrient lockout can occur at incorrect pH)',
      'Check for zinc or manganese deficiencies',
      'Inspect for pest damage or disease',
      'Consider environmental stress factors',
      'Adjust nutrient regimen if necessary',
      'Monitor for signs of recovery after treatment'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'leaf',
    color: '#FFEB3B'
  },
  'yellow-leaves': {
    id: 'yellow-leaves',
    title: 'Yellow leaves',
    description: 'General yellowing of leaves',
    symptoms: [
      'Yellow discoloration affecting entire leaves or large portions',
      'Yellowing may be uniform or appear as patches',
      'May start on newer or older leaves depending on the cause',
      'Leaves may eventually turn brown or die',
      'Yellowing may be accompanied by other symptoms like leaf drop',
      'Plant may appear generally unhealthy or stunted'
    ],
    solutions: [
      'Check for nitrogen deficiency (common cause of yellowing)',
      'Monitor watering practices (overwatering or underwatering can cause yellowing)',
      'Ensure proper light exposure',
      'Check for pest damage or disease',
      'Consider environmental stress factors',
      'Inspect root health',
      'Adjust nutrient regimen if necessary',
      'Monitor for signs of recovery after treatment'
    ],
    images: [],
    source: 'GrowIt Plant Care Guide',
    icon: 'warning',
    color: '#FF9800'
  }
};

export default function PlantIssueScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [issue, setIssue] = useState<PlantIssue | null>(null);
  const [loading, setLoading] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [noImagesAvailable, setNoImagesAvailable] = useState(false);
  const [localImages, setLocalImages] = useState<ImageSourcePropType[]>([]);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [imageDescriptions, setImageDescriptions] = useState<{[key: number]: {title: string, description: string}}>({});
  const [loadingDescriptions, setLoadingDescriptions] = useState(false);
  const [showBackButton, setShowBackButton] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (id && typeof id === 'string') {
      console.log(`Loading issue with ID: ${id}`);
      const currentIssue = PLANT_ISSUES[id];
      setIssue(currentIssue);
      
      // Try to load online images as a fallback
      generateImages(id);
      
      // For bronze-brown-patches, we know there are 15 images
      if (id === 'bronze-brown-patches') {
        try {
          // Load all 15 images using static require statements
          const images = [];
          
          // Load each image individually with static require statements
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-1.jpg'));
            console.log('Successfully loaded image 1');
          } catch (e) { console.log('Failed to load image 1'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-2.jpg'));
            console.log('Successfully loaded image 2');
          } catch (e) { console.log('Failed to load image 2'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-3.jpg'));
            console.log('Successfully loaded image 3');
          } catch (e) { console.log('Failed to load image 3'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-4.jpg'));
            console.log('Successfully loaded image 4');
          } catch (e) { console.log('Failed to load image 4'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-5.jpg'));
            console.log('Successfully loaded image 5');
          } catch (e) { console.log('Failed to load image 5'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-6.jpg'));
            console.log('Successfully loaded image 6');
          } catch (e) { console.log('Failed to load image 6'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-7.jpg'));
            console.log('Successfully loaded image 7');
          } catch (e) { console.log('Failed to load image 7'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-8.jpg'));
            console.log('Successfully loaded image 8');
          } catch (e) { console.log('Failed to load image 8'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-9.jpg'));
            console.log('Successfully loaded image 9');
          } catch (e) { console.log('Failed to load image 9'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-10.jpg'));
            console.log('Successfully loaded image 10');
          } catch (e) { console.log('Failed to load image 10'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-11.jpg'));
            console.log('Successfully loaded image 11');
          } catch (e) { console.log('Failed to load image 11'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-12.jpg'));
            console.log('Successfully loaded image 12');
          } catch (e) { console.log('Failed to load image 12'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-13.jpg'));
            console.log('Successfully loaded image 13');
          } catch (e) { console.log('Failed to load image 13'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-14.jpg'));
            console.log('Successfully loaded image 14');
          } catch (e) { console.log('Failed to load image 14'); }
          
          try {
            images.push(require('../../assets/images/plant-issues/bronze-brown-patches/bronze-brown-patches-15.jpg'));
            console.log('Successfully loaded image 15');
          } catch (e) { console.log('Failed to load image 15'); }
          
          if (images.length > 0) {
            setLocalImages(images);
            setDebugInfo(`Found ${images.length} Results\nClick an image for more details`);
            console.log(`Successfully loaded ${images.length} images`);
            
            // Load descriptions from file
            loadDescriptionsFromFile(id);
            
            // Log the descriptions after a short delay to ensure they're loaded
            setTimeout(() => {
              logImageDescriptions();
            }, 500);
          } else {
            setNoImagesAvailable(true);
            setDebugInfo('No images could be loaded');
          }
        } catch (error) {
          console.error('Error loading images:', error);
          setDebugInfo(`Error loading images: ${error}`);
          setNoImagesAvailable(true);
        }
      } else {
        setNoImagesAvailable(true);
      }
    }
  }, [id]);

  const generateImages = async (issueId: string) => {
    try {
      setLoading(true);
      
      // Fallback to Unsplash for issues without local images
      let searchTerms = 'plant';
      
      switch(issueId) {
        case 'nutrients':
          searchTerms = 'plant+nutrient+deficiency,plant+yellow+leaves';
          break;
        case 'pests':
          searchTerms = 'plant+pest+insect,plant+leaf+damage';
          break;
        case 'bronze-brown-patches':
          searchTerms = 'plant+brown+patches,plant+leaf+damage';
          break;
        case 'brown-slimy-roots':
          searchTerms = 'plant+root+rot,plant+unhealthy+roots';
          break;
        case 'brown-yellow-tips':
          searchTerms = 'plant+brown+leaf+tips,plant+leaf+damage';
          break;
        case 'buds-dying':
          searchTerms = 'plant+dying+buds,plant+flower+problems';
          break;
        case 'buds-look-odd':
          searchTerms = 'plant+abnormal+buds,plant+flower+problems';
          break;
        case 'bugs-visible':
          searchTerms = 'plant+insects,plant+pests';
          break;
        case 'curling-clawing-leaves':
          searchTerms = 'plant+curling+leaves,plant+leaf+problems';
          break;
        case 'dark-leaves':
          searchTerms = 'plant+dark+leaves,plant+leaf+color';
          break;
        case 'drooping-plant':
          searchTerms = 'plant+drooping,plant+wilting';
          break;
        case 'holes-in-leaves':
          searchTerms = 'plant+holes+in+leaves,plant+leaf+damage';
          break;
        case 'mold-powder':
          searchTerms = 'plant+mold,plant+powdery+mildew';
          break;
        case 'pink-purple-leaves':
          searchTerms = 'plant+purple+leaves,plant+leaf+color';
          break;
        case 'red-stems':
          searchTerms = 'plant+red+stems,plant+stem+color';
          break;
        case 'shiny-smooth-leaves':
          searchTerms = 'plant+shiny+leaves,plant+leaf+texture';
          break;
        case 'spots-markings':
          searchTerms = 'plant+leaf+spots,plant+leaf+markings';
          break;
        case 'twisted-growth':
          searchTerms = 'plant+twisted+growth,plant+abnormal+growth';
          break;
        case 'webbing':
          searchTerms = 'plant+spider+mites,plant+webbing';
          break;
        case 'wilting-leaves':
          searchTerms = 'plant+wilting+leaves,plant+leaf+problems';
          break;
        case 'yellow-between-veins':
          searchTerms = 'plant+chlorosis,plant+leaf+yellowing';
          break;
        case 'yellow-leaves':
          searchTerms = 'plant+yellow+leaves,plant+leaf+problems';
          break;
        default:
          searchTerms = 'plant+care';
      }
      
      // Create multiple image URLs using Unsplash Source API
      const imageUrls = [
        `https://source.unsplash.com/featured/?${searchTerms.split(',')[0]}`,
        `https://source.unsplash.com/featured/?${searchTerms.split(',')[1]}`,
        `https://source.unsplash.com/featured/?${searchTerms.split(',')[2] || 'plant+care'}`
      ];
      
      setGeneratedImages(imageUrls);
      
    } catch (error) {
      console.error('Error setting up images:', error);
      
      // Use fallback images in case of error
      const fallbackImages = [
        'https://source.unsplash.com/featured/?plant',
        'https://source.unsplash.com/featured/?garden',
        'https://source.unsplash.com/featured/?plant+care'
      ];
      
      setGeneratedImages(fallbackImages);
    } finally {
      setLoading(false);
    }
  };

  // Function to load descriptions from a text file
  const loadDescriptionsFromFile = async (issueId: string) => {
    try {
      setLoadingDescriptions(true);
      
      // For React Native, we need to use a different approach to load text files
      // We'll create a static object with the descriptions for each image
      // This is a more reliable approach than trying to load a text file at runtime
      
      // Define descriptions for bronze-brown-patches
      if (issueId === 'bronze-brown-patches') {
        const descriptions: {[key: number]: {title: string, description: string}} = {
          0: {
            title: "Boron Deficiency",
            description: "Problem: A boron deficiency in cannabis is relatively rare unless a plant is underwatered or in a really dry environment, and is usually accompanied by other types of nutrient or pH problems that appear as problems with the leaves. The first signs of a cannabis boron deficiency is abnormal or thick growth tips along with brown or yellow spotting on new leaves."
          },
          1: {
            title: "Broad Mites",
            description: "Broad mites (Polyphagotarsonemus latus) on your cannabis plants are so tiny they are practically impossible to spot with the naked eye. They are about 1/3 the size of a spider mite, and therefore broad mites are even difficult to see under a microscope. Most growers notice the symptoms of broad mites on their cannabis plants long before they ever see a broad mite or broad mite eggs."
          },
          2: {
            title: "Calcium Deficiency",
            description: "Calcium is an important nutrient that helps provide structure to the cannabis plant and helps it withstand stress like heat. When your cannabis plant has a calcium deficiency, the main symptom that you'll be able to notice is brown or bronze splotches or spots on your leaves. It is also often associated with yellowing (though leaves don't always turn yellow). Calcium deficiencies are often caused from low pH at the roots, but also occasionally from growing in overly pure or filtered water."
          },
          3: {
            title: "Copper Deficiency",
            description: "A cannabis copper deficiency appears with leaf symptoms such as dark leaves that take on blue or even purple undertones. The tips and edges of leaves turn pale yellow or white in stark contrast to the rest of the leaves which have turned dark. In flowering it's important to correct a cannabis copper deficiency as soon as possible because buds may stop maturing if the plant isn't fixed up right away. Copper doesn't move easily through the plant and is considered \"low-mobile\" which means the yellowing leaves might not necessarily turn green again, but the problem should stop spreading to new marijuana leaves."
          },
          4: {
            title: "Four-lined plant bug",
            description: "The four-lined plant bug is a common cannabis pest in the United States (also commonly written \"fourlined plant bug\" and officially Poecilocapsus lineatus). The four-lined plant bug leaves spotting injuries where it sucks out all the juices from inside the hemp or cannabis leaf. Each spot on your cannabis leaves were essentially the meal of a four-lined-plant bug."
          },
          5: {
            title: "Heat Stress",
            description: "Your cannabis plant can only withstand a certain amount of heat and light. After a certain point, your cannabis will start exhibiting signs of stress on the leaves near the sources of light and/or heat. Your leaves will get yellow or brown brown spotting and may appear generally burnt in places when there's too much light. It's also common for leaves to curl up or down, fold inward like conoes or tacos, and for the serrated edges of leaves to start flipping up"
          },
          6: {
            title: "Over-Watering",
            description: "After watering, your plants start drooping. Usually the droopy leaves will feel firm and appear curled down (the whole leaf will be curled, not just the tips, which is often a sign of nitrogen toxicity). With overwatered cannabis plants, you may also notice Chlorosis (leaf yellowing that is similar to a nitrogen deficiency)."
          },
          7: {
            title: "Manganese Deficiency",
            description: "Leaves may become yellow in between the veins, with mottled brown spots on the affected leaves. These brown dead patches may spread and eventually kill the leaf. Leaves may also shred and fall apart."
          },
          8: {
            title: "pH Fluctuations",
            description: "Managing pH is crucial for cannabis plants to be able to take up nutrients through their roots. When the pH around the roots jumps up and down, it can stress the plant and cause brown spots to appear on the leaves. Spotting on the leaves as a results of pH fluctuations is more common in hydroponic setups (where the pH tends to go up and down), but it is possible it can also happen in soil. This seems to often happen when the pH swings too high or low."
          },
          9: {
            title: "Planthoppers",
            description: "Planthoppers can attack and infest cannabis, leaving a unique cotton-like growth while they suck the life out of your plants! Planthoppers also spread plant diseases, so you want them away from your plants as soon as possible."
          },
          10: {
            title: "Potassium Deficiency",
            description: "Leaf Problem / Symptoms: With a marijuana potassium deficiency, you'll generally see symptoms on older leaves, but not always. Sometimes you'll see the symptoms at the top of the plant. Leaves with a potassium deficiency get yellow, brown, or burnt edges and tips. The burnt edges may look a little like nutrient burn, except the affected leaves also start turning yellow in the margins."
          },
          11: {
            title: "Phosphorus Deficiency",
            description: "A cannabis phosphorus deficiency generally appears on leaves from the lower/older parts of the plant. The lower leaves may turn dark green or yellow, and start getting spots or big splotches that look brown, bronze or even a little blue. The leaves may thicken and curl, and the affected leaves feel stiff. Sometimes the stems of the plant turn bright red or purple, but not always."
          },
          12: {
            title: "Root Problems",
            description: "Cannabis Plants With Root Problems Show Many Symptoms. Cannabis may appear overwatered or droopy. Curling or cupping of leaves. Wilting – either individual stems wilt or the whole plant may wilt. Slow growth, or staying very small for weeks. Leaf yellowing, or sometimes even other colors like purple or red. Brown spots / Burnt spots. Other strange nutrient problems. Brown or slimy roots – this is often a sign of root rot. Smelly runoff water (smells rotting or musty). Leaves may start dying and falling off rapidly. Plants drink much less water than usual"
          },
          13: {
            title: "Root Rot",
            description: "The curled, drooping, unhealthy leaves are the result of the plant not being able to get enough oxygen through the roots. Root rot symptoms often look like a soil plant that has been severely over or under-watered. Every infection looks a little different, but brown roots are usually the main symptom. It may affect all or just parts of the roots, and the sick sections usually become slimy or mushy and start twisting together."
          },
          14: {
            title: "Slugs or Snails",
            description: "Ewwww slugs and snails are eating the leaves and buds of my cannabis plants!!!! Although not the most common cannabis pest, slugs and snails are unwelcome visitors when they do show up! Sure signs of snail or slug damage include a trail of slime on leaves, and new holes often have \"scalloped\" edges where the unwelcome guest has been taking bites out of your leaves with its tiny mouth (often confused with caterpillar damage). After damage has been there a while, the edges start to look more smooth. They are most likely to attack your plants at night."
          }
        };
        
        setImageDescriptions(descriptions);
        console.log(`Loaded ${Object.keys(descriptions).length} descriptions for ${issueId}`);
        
        // Log the descriptions to verify they're being set correctly
        setTimeout(() => {
          logImageDescriptions();
        }, 100);
      } else {
        // For other plant issues, use default descriptions
        createDefaultDescriptions(issueId);
      }
    } catch (error) {
      console.error('Error loading descriptions:', error);
      // Use default descriptions in case of error
      createDefaultDescriptions(issueId);
    } finally {
      setLoadingDescriptions(false);
    }
  };

  // Helper function to create default descriptions
  const createDefaultDescriptions = (issueId: string) => {
    const defaultDescriptions: {[key: number]: {title: string, description: string}} = {};
    
    // Add default descriptions for each image
    for (let i = 0; i < 15; i++) {
      defaultDescriptions[i] = {
        title: `${issueId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} - Image ${i+1}`,
        description: `This image shows an example of ${issueId.replace(/-/g, ' ')}. Click on different images to see more examples.`
      };
    }
    
    setImageDescriptions(defaultDescriptions);
  };

  // Add this function to log the current state of imageDescriptions
  const logImageDescriptions = () => {
    console.log('Current image descriptions:', imageDescriptions);
    console.log('Number of descriptions:', Object.keys(imageDescriptions).length);
  };

  // Update the handleImagePress function to ensure descriptions are loaded
  const handleImagePress = (index: number) => {
    console.log(`Image ${index} pressed`);
    console.log('Description for this image:', imageDescriptions[index]);
    
    // If descriptions aren't loaded yet, load them now
    if (Object.keys(imageDescriptions).length === 0 && id) {
      loadDescriptionsFromFile(id as string);
    }
    
    setSelectedImage(index);
    setModalVisible(true);
  };

  // Add a useEffect hook to monitor the imageDescriptions state
  useEffect(() => {
    console.log('imageDescriptions state changed:', imageDescriptions);
    console.log('Number of descriptions:', Object.keys(imageDescriptions).length);
  }, [imageDescriptions]);

  const handleScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    // Show back button when scrolled down more than 200px
    setShowBackButton(offsetY > 200);
  };

  if (!issue) {
    return (
      <View style={styles.container}>
        <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
          <Header />
        </SafeAreaView>
        <View style={styles.content}>
          <Text style={styles.errorText}>Issue not found</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
        <Footer currentScreen="home" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView 
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          {/* Hero Section */}
          <View style={styles.hero}>
            <Image 
              source={require('../../assets/images/seedling.jpg')}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <View style={styles.heroContent}>
              <Text style={styles.heroTitle}>Plant Diagnosis</Text>
              <Text style={styles.heroDescription}>Identify and fix plant health issues</Text>
            </View>
          </View>

          {/* Issue Card */}
          <View style={styles.issueCardContainer}>
            <View style={styles.issueCard}>
              <View style={styles.issueContent}>
                <Text style={styles.issueTitle}>{issue.title}</Text>
                <Text style={styles.issueDescription}>{issue.description}</Text>
              </View>
            </View>
          </View>

          <View style={styles.content}>
            {/* Symptoms section moved here, right after the issue card */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Symptoms</Text>
              {issue.symptoms.map((symptom, index) => (
                <View key={index} style={styles.bulletPoint}>
                  <Text style={styles.bullet}>•</Text>
                  <Text style={styles.bulletText}>{symptom}</Text>
                </View>
              ))}
            </View>
            
            {/* Debug info */}
            {debugInfo ? (
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>{debugInfo.split('\n')[0]}</Text>
                <Text style={styles.debugSubtitle}>{debugInfo.split('\n')[1]}</Text>
              </View>
            ) : null}
            
            {/* Show local images if available */}
            {!loading && localImages.length > 0 && (
              <View style={styles.imageGallery}>
                {localImages.map((image, index) => (
                  <View key={index} style={styles.imageContainer}>
                    <Text style={styles.imageTitle}>
                      {imageDescriptions[index]?.title || `Image ${index + 1}`}
                    </Text>
                    <Text style={styles.imagePreviewDescription}>
                      {imageDescriptions[index]?.description.split('.')[0] || ''}
                    </Text>
                    <TouchableOpacity 
                      onPress={() => handleImagePress(index)}
                    >
                      <Image
                        source={image}
                        style={styles.galleryImage}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
            
            {/* Only show online images if no local images are available */}
            {!loading && localImages.length === 0 && generatedImages.length > 0 && (
              <Image
                source={{ uri: generatedImages[0] }}
                style={styles.featuredImage}
                resizeMode="cover"
              />
            )}
            
            {loading && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#4CAF50" />
                <Text style={styles.loadingText}>Loading images...</Text>
              </View>
            )}
            
            {noImagesAvailable && localImages.length === 0 && (
              <View style={styles.noImagesContainer}>
                <Text style={styles.noImagesText}>No images available for this issue yet.</Text>
                <Text style={styles.noImagesSubtext}>Images will be added in a future update.</Text>
              </View>
            )}

            <Text style={styles.source}>Source: {issue.source}</Text>
          </View>
        </ScrollView>
        
        {/* Floating Back Button - Only visible when scrolled down */}
        {showBackButton && (
          <TouchableOpacity 
            style={styles.floatingBackButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
        )}
        
        <Footer currentScreen="home" />
      </SafeAreaView>
      
      {/* Image Detail Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedImage !== null && localImages[selectedImage] && (
              <>
                <Text style={styles.modalTitle}>
                  {imageDescriptions[selectedImage]?.title || `Image ${selectedImage + 1}`}
                </Text>
                <Image
                  source={localImages[selectedImage]}
                  style={styles.modalImage}
                  resizeMode="contain"
                />
                <ScrollView style={styles.modalScrollView}>
                  <View style={styles.modalTextContainer}>
                    {loadingDescriptions ? (
                      <ActivityIndicator size="small" color="#4CAF50" />
                    ) : (
                      <Text style={styles.modalDescription}>
                        {imageDescriptions[selectedImage]?.description || 
                         "This image shows an example of the plant issue. Click on different images to see more examples."}
                      </Text>
                    )}
                  </View>
                </ScrollView>
              </>
            )}
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  safeArea: {
    backgroundColor: '#fff',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    // Remove padding to allow hero to be full width
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginVertical: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginTop: 12,
  },
  backButtonText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  issueCardContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  issueCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f7f0',
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  issueContent: {
    flex: 1,
  },
  issueTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  issueDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  bulletPoint: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  bullet: {
    fontSize: 16,
    color: '#333',
    marginRight: 8,
  },
  bulletText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
  },
  featuredImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 16,
  },
  source: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 20,
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  floatingBackButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    backgroundColor: 'rgba(129, 199, 132, 0.7)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 50,
    height: 50,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3.84,
    elevation: 3,
    zIndex: 10,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '85%',
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
    marginBottom: 12,
  },
  modalScrollView: {
    width: '100%',
    maxHeight: 250,
  },
  modalTextContainer: {
    width: '100%',
    marginBottom: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1B5E20',
    marginBottom: 4,
    textAlign: 'left',
    width: '100%',
  },
  modalDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  modalCloseButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 20,
  },
  modalCloseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  imageGallery: {
    flexDirection: 'column',
    justifyContent: 'center',
    marginBottom: 16,
  },
  imageContainer: {
    width: '100%',
    marginBottom: 24,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    backgroundColor: '#fff',
    padding: 12,
    position: 'relative',
  },
  imageTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
    textAlign: 'left',
  },
  imagePreviewDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    textAlign: 'left',
    fontStyle: 'italic',
  },
  galleryImage: {
    width: '100%',
    height: 250,
    borderRadius: 0,
    resizeMode: 'contain',
  },
  debugContainer: {
    padding: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginBottom: 16,
  },
  debugTitle: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginBottom: 4,
  },
  debugSubtitle: {
    fontSize: 14,
    color: '#666',
    fontWeight: '400',
  },
  noImagesContainer: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  noImagesText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  noImagesSubtext: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
}); 