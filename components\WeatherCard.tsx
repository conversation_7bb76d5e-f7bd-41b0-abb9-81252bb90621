import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { OPENWEATHER_API_KEY } from '../app/config';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface WeatherData {
  main: {
    temp: number;
    humidity: number;
  };
  weather: {
    main: string;
    description: string;
    icon: string;
  }[];
  wind: {
    speed: number;
  };
  name?: string;
  sys?: {
    country?: string;
  };
}

interface CachedWeatherData {
  data: WeatherData;
  locationName: string;
  locationState: string;
  timestamp: number;
}

const TEMP_UNIT_PREF_KEY = '@temp_unit_preference';
const WEATHER_CACHE_KEY = '@weather_cache';
const CACHE_EXPIRATION_MS = 60 * 60 * 1000; // 1 hour in milliseconds

// Weather icon mapping based on OpenWeather icon codes
const getWeatherIcon = (iconCode: string): string => {
  // Map OpenWeather icon codes to colorful Flaticon weather icons
  const iconMap: Record<string, string> = {
    // Sunny/Clear
    '01d': 'https://cdn-icons-png.flaticon.com/512/869/869869.png',
    '01n': 'https://cdn-icons-png.flaticon.com/512/869/869869.png',
    
    // Partly Cloudy
    '02d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '02n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    
    // Cloudy
    '03d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '03n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    
    // Overcast
    '04d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '04n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    
    // Light Rain
    '09d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '09n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    
    // Rain with Sun
    '10d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '10n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    
    // Thunderstorm
    '11d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '11n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    
    // Snow
    '13d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '13n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    
    // Fog/Mist
    '50d': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
    '50n': 'https://cdn-icons-png.flaticon.com/512/414/414927.png',
  };
  
  return iconMap[iconCode] || 'https://cdn-icons-png.flaticon.com/512/869/869869.png';
};

export default function WeatherCard() {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUsingMockData, setIsUsingMockData] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [tempUnit, setTempUnit] = useState<'C' | 'F'>('F'); // Default to Fahrenheit
  const [locationName, setLocationName] = useState<string>('');
  const [locationState, setLocationState] = useState<string>('');
  const [iconLoading, setIconLoading] = useState(true);
  const MAX_RETRIES = 3;

  useEffect(() => {
    loadTempPreference();
    loadWeatherData();
    
    // Set up interval to refresh weather data hourly
    const intervalId = setInterval(() => {
      fetchWeather(true); // Force refresh
    }, CACHE_EXPIRATION_MS);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  const loadTempPreference = async () => {
    try {
      const savedUnit = await AsyncStorage.getItem(TEMP_UNIT_PREF_KEY);
      if (savedUnit === 'C' || savedUnit === 'F') {
        setTempUnit(savedUnit);
      }
    } catch (error) {
      console.error('Error loading temperature preference:', error);
    }
  };

  const loadWeatherData = async () => {
    try {
      // Try to load cached weather data first
      const cachedData = await AsyncStorage.getItem(WEATHER_CACHE_KEY);
      
      if (cachedData) {
        const parsedCache: CachedWeatherData = JSON.parse(cachedData);
        const now = Date.now();
        
        // Check if cache is still valid (less than 1 hour old)
        if (now - parsedCache.timestamp < CACHE_EXPIRATION_MS) {
          console.log('Using cached weather data');
          setWeather(parsedCache.data);
          setLocationName(parsedCache.locationName);
          setLocationState(parsedCache.locationState);
          setLoading(false);
          return;
        } else {
          console.log('Cached weather data expired');
        }
      }
      
      // If no valid cache, fetch fresh data
      await fetchWeather(false);
    } catch (error) {
      console.error('Error loading weather data:', error);
      // If loading from cache fails, fetch fresh data
      await fetchWeather(false);
    }
  };

  const convertTemp = (celsius: number): number => {
    if (tempUnit === 'F') {
      return Math.round((celsius * 9/5) + 32);
    }
    return Math.round(celsius);
  };

  const fetchWeather = async (forceRefresh: boolean = false) => {
    try {
      setLoading(true);
      setError(null);
      setIsUsingMockData(false);
      setIconLoading(true);

      // Check if API key is configured
      if (!OPENWEATHER_API_KEY) {
        console.log('OpenWeather API key not configured. Using mock data.');
        const mockData = {
          main: {
            temp: 22,
            humidity: 65,
          },
          weather: [{
            main: 'Clear',
            description: 'clear sky',
            icon: '01d',
          }],
          wind: {
            speed: 12,
          },
          name: 'San Francisco',
          sys: {
            country: 'US',
          },
        };
        
        setWeather(mockData);
        setLocationName('San Francisco');
        setLocationState('CA');
        setIsUsingMockData(true);
        setLoading(false);
        
        // Cache the mock data
        await cacheWeatherData(mockData, 'San Francisco', 'CA');
        return;
      }

      // Get location permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setError('Location permission denied');
        setLoading(false);
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      // Fetch weather data
      console.log(`Fetching weather data for coordinates: ${latitude}, ${longitude}`);
      const apiUrl = `https://api.openweathermap.org/data/3.0/onecall?lat=${latitude}&lon=${longitude}&units=metric&appid=${OPENWEATHER_API_KEY}`;
      console.log(`API URL: ${apiUrl.replace(OPENWEATHER_API_KEY, 'API_KEY_HIDDEN')}`);
      
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API response not OK: ${response.status} ${response.statusText}`);
        console.error(`Error details: ${errorText}`);
        
        // Check for specific API key errors
        if (response.status === 401) {
          try {
            const errorData = JSON.parse(errorText);
            if (errorData.message && errorData.message.includes('Invalid API key')) {
              console.error('API key is invalid or not yet activated. New API keys can take up to 2 hours to activate.');
              throw new Error('API key is invalid or not yet activated. Please try again later.');
            }
          } catch (e) {
            // If parsing fails, just use the original error
          }
        }
        
        // If we haven't exceeded max retries, try again
        if (retryCount < MAX_RETRIES) {
          console.log(`Retrying (${retryCount + 1}/${MAX_RETRIES})...`);
          setRetryCount(prev => prev + 1);
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
          return fetchWeather(forceRefresh);
        }
        
        throw new Error(`Failed to fetch weather data: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Weather data received successfully');
      
      // Get location name from reverse geocoding
      let cityName = 'Unknown';
      let stateCode = 'Unknown';
      
      try {
        // Use reverse geocoding to get location details
        const reverseGeocodeUrl = `https://api.openweathermap.org/geo/1.0/reverse?lat=${latitude}&lon=${longitude}&limit=1&appid=${OPENWEATHER_API_KEY}`;
        const geoResponse = await fetch(reverseGeocodeUrl);
        
        if (geoResponse.ok) {
          const geoData = await geoResponse.json();
          if (geoData && geoData.length > 0) {
            cityName = geoData[0].name || 'Unknown';
            stateCode = geoData[0].state || geoData[0].country || 'Unknown';
            console.log(`Location identified: ${cityName}, ${stateCode}`);
          }
        }
      } catch (geoError) {
        console.error('Error getting location details:', geoError);
        // Fallback to timezone if reverse geocoding fails
        cityName = data.timezone.split('/')[1]?.replace('_', ' ') || 'Unknown';
        stateCode = data.timezone.split('/')[0] || 'Unknown';
      }
      
      // Transform the data to match our expected format
      const transformedData = {
        main: {
          temp: data.current.temp,
          humidity: data.current.humidity,
        },
        weather: [{
          main: data.current.weather[0].main,
          description: data.current.weather[0].description,
          icon: data.current.weather[0].icon,
        }],
        wind: {
          speed: data.current.wind_speed,
        },
        name: cityName,
        sys: {
          country: stateCode,
        },
      };
      
      setWeather(transformedData);
      
      // Set location name and state
      setLocationName(cityName);
      setLocationState(stateCode);
      
      // Cache the weather data
      await cacheWeatherData(transformedData, cityName, stateCode);
      
      setRetryCount(0); // Reset retry count on success
    } catch (err) {
      console.error('Weather fetch error:', err);
      if (err instanceof Error) {
        console.error('Error message:', err.message);
        console.error('Error stack:', err.stack);
      }
      setError(err instanceof Error ? err.message : 'Failed to fetch weather');
      
      // Provide mock data as fallback
      const mockData = {
        main: {
          temp: 22,
          humidity: 65,
        },
        weather: [{
          main: 'Clear',
          description: 'clear sky',
          icon: '01d',
        }],
        wind: {
          speed: 12,
        },
        name: 'San Francisco',
        sys: {
          country: 'US',
        },
      };
      
      setWeather(mockData);
      setLocationName('San Francisco');
      setLocationState('CA');
      setIsUsingMockData(true);
      
      // Cache the mock data
      await cacheWeatherData(mockData, 'San Francisco', 'CA');
    } finally {
      setLoading(false);
    }
  };

  const cacheWeatherData = async (data: WeatherData, cityName: string, stateCode: string) => {
    try {
      const cacheData: CachedWeatherData = {
        data,
        locationName: cityName,
        locationState: stateCode,
        timestamp: Date.now(),
      };
      
      await AsyncStorage.setItem(WEATHER_CACHE_KEY, JSON.stringify(cacheData));
      console.log('Weather data cached successfully');
    } catch (error) {
      console.error('Error caching weather data:', error);
    }
  };

  const handleIconLoad = () => {
    setIconLoading(false);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <Text style={styles.loadingText}>Loading weather data...</Text>
        </View>
      </View>
    );
  }

  if (error && !isUsingMockData) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={24} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity 
          style={styles.retryButton} 
          onPress={() => fetchWeather(false)}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!weather) {
    return null;
  }

  const weatherIcon = weather.weather[0].icon ? getWeatherIcon(weather.weather[0].icon) : 'https://cdn-icons-png.flaticon.com/512/869/869869.png';

  return (
    <View style={styles.container}>
      <View style={styles.weatherContainer}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <View style={styles.titleRow}>
              <View style={styles.iconContainer}>
                {iconLoading && (
                  <ActivityIndicator size="small" color="#4CAF50" style={styles.iconLoader} />
                )}
                <Image 
                  source={{ uri: weatherIcon }} 
                  style={styles.weatherIcon} 
                  resizeMode="contain"
                  onLoad={handleIconLoad}
                />
              </View>
              <Text style={styles.title}>Current Weather</Text>
            </View>
            <View style={styles.locationContainer}>
              <Ionicons name="location-outline" size={20} color="#4CAF50" />
              <Text style={styles.locationText}>
                {locationName}, {locationState}
              </Text>
            </View>
          </View>
          <TouchableOpacity 
            style={styles.refreshButton} 
            onPress={() => fetchWeather(true)}
          >
            <Ionicons name="refresh-outline" size={20} color="#4CAF50" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.content}>
          <View style={styles.mainInfo}>
            <Text style={styles.temperature}>{convertTemp(weather.main.temp)}°{tempUnit}</Text>
            <Text style={styles.description}>{weather.weather[0].description}</Text>
          </View>

          <View style={styles.details}>
            <View style={styles.detailItem}>
              <Ionicons name="water" size={20} color="#4CAF50" />
              <Text style={styles.detailText}>{weather.main.humidity}%</Text>
              <Text style={styles.detailLabel}>Humidity</Text>
            </View>
            
            <View style={styles.detailItem}>
              <Ionicons name="cloudy" size={20} color="#4CAF50" />
              <Text style={styles.detailText}>{weather.wind.speed} km/h</Text>
              <Text style={styles.detailLabel}>Wind</Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 0,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  weatherContainer: {
    gap: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  iconContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginRight: 8,
  },
  iconLoader: {
    position: 'absolute',
  },
  weatherIcon: {
    width: 40,
    height: 40,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 48, // Align with the title text (40px icon width + 8px margin)
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  content: {
    gap: 16,
  },
  mainInfo: {
    alignItems: 'center',
  },
  temperature: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textTransform: 'capitalize',
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  detailItem: {
    alignItems: 'center',
  },
  detailText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginTop: 4,
  },
  detailLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: '#f44336',
    marginLeft: 8,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    alignSelf: 'center',
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  refreshButton: {
    padding: 4,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    color: '#666',
    marginLeft: 8,
  },
}); 