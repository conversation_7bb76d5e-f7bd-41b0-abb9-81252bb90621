import React, { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, Modal, Alert, Pressable, Platform, ActivityIndicator, RefreshControl, GestureResponderEvent } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useState, useEffect, useCallback, useRef } from 'react';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter } from 'expo-router';
import PlantInputModal from '../components/PlantInputModal';
import WeatherCard from '../components/WeatherCard';
import { scheduleLocalNotification, showWebNotification, getNotificationCapabilityInfo } from '../utils/notificationUtils';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

interface Plant {
  id: number;
  name: string;
  dateAdded: string;
  lastWatered: string;
  light: string;
  environment: string;
  stage: string;
  lastFeeding: string;
  history?: {
    id: string;
    type: 'watering' | 'feeding' | 'photo' | 'stage' | 'log';
    date: string;
    description: string;
    photoUrl?: string;
    stage?: string;
  }[];
}

const STORAGE_KEY = '@plants_data';
const NOTIFICATIONS_STORAGE_KEY = '@notifications_data';

export default function HomeScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [environments, setEnvironments] = useState<string[]>([]);
  const [showAddPlantModal, setShowAddPlantModal] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [hasShownTodayAlert, setHasShownTodayAlert] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState<{
    title: string;
    message: string;
    emoji: string;
  }>({
    title: '',
    message: '',
    emoji: ''
  });
  const [stats, setStats] = useState({
    totalPlants: 0,
    needsWatering: 0,
    needsFeeding: 0,
    notifications: 0,
  });
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);
  const [showActionMenu, setShowActionMenu] = useState(false);
  const [showNotificationInfo, setShowNotificationInfo] = useState(false);
  const actionButtonRef = useRef<View>(null);

  // Load plants and notifications from storage when component mounts
  useEffect(() => {
    loadPlants();
    loadNotifications();
  }, []);

  // Update stats whenever plants or notifications change
  useEffect(() => {
    updateStats();
  }, [plants, notifications]);

  const loadPlants = async () => {
    try {
      console.log('Loading plants from storage...');
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      console.log('Stored plants data:', storedPlants);
      if (storedPlants) {
        const parsedPlants: Plant[] = JSON.parse(storedPlants).map((plant: any) => ({
          id: String(plant.id),
          name: plant.name,
          species: plant.species || 'Unknown',
          wateringFrequency: plant.wateringFrequency || 7,
          image: plant.image || '',
          lastWatered: new Date(plant.lastWatered),
          nextWatering: new Date(plant.nextWatering),
          health: plant.health || 'healthy',
          location: plant.location || 'Unknown',
          notes: plant.notes || '',
          createdAt: new Date(plant.createdAt),
          updatedAt: new Date(plant.updatedAt)
        }));
        console.log('Parsed plants:', parsedPlants);
        setPlants(parsedPlants);
      } else {
        console.log('No plants found in storage');
        setPlants([]);
      }
    } catch (error) {
      console.error('Error loading plants:', error);
      Alert.alert('Error', 'Failed to load plants. Please try again.');
    }
  };

  const loadNotifications = async () => {
    try {
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      if (storedData) {
        const data = JSON.parse(storedData);
        if (Array.isArray(data)) {
          setNotifications(data);
        }
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  const updateStats = () => {
    console.log('Updating stats with plants:', plants.length);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayNotifications = notifications.filter(notification => {
      const notificationDate = new Date(notification.time);
      return notificationDate >= today && notificationDate < tomorrow;
    });

    const stats = {
      totalPlants: plants.length,
      needsWatering: plants.filter(plant => {
        const lastWatered = new Date(plant.lastWatered);
        const daysSinceWatering = Math.floor((now.getTime() - lastWatered.getTime()) / (1000 * 60 * 60 * 24));
        return daysSinceWatering >= 7;
      }).length,
      needsFeeding: plants.filter(plant => {
        const lastWatered = new Date(plant.lastWatered);
        const daysSinceWatering = Math.floor((now.getTime() - lastWatered.getTime()) / (1000 * 60 * 60 * 24));
        return daysSinceWatering >= 30;
      }).length,
      notifications: todayNotifications.length,
    };
    console.log('Updated stats:', stats);
    setStats(stats);

    // Show system notification if there are tasks due today and we haven't shown it yet
    if (todayNotifications.length > 0 && !hasShownTodayAlert) {
      const taskTypes = todayNotifications.map(task => {
        const emoji = task.type === 'watering' ? '💧' :
                     task.type === 'feeding' ? '🌿' :
                     task.type === 'schedule' ? '📅' :
                     task.type === 'pruning' ? '✂️' : '📋';
        return `${emoji} ${task.title} for ${task.plantName}`;
      });

      if (Platform.OS === 'web') {
        // For web, use browser notifications
        showWebNotification(
          'Tasks Due Today',
          `You have ${todayNotifications.length} task${todayNotifications.length > 1 ? 's' : ''} due today:\n${taskTypes.join('\n')}`
        );
      } else {
        // For mobile, use our utility function
        await scheduleLocalNotification(
          'Tasks Due Today',
          `You have ${todayNotifications.length} task${todayNotifications.length > 1 ? 's' : ''} due today:\n${taskTypes.join('\n')}`,
          { type: 'due_tasks', tasks: todayNotifications }
        );
      }
      setHasShownTodayAlert(true);
    }
  };

  const scheduleTodayNotification = async (taskCount: number) => {
    try {
      const taskWord = taskCount === 1 ? 'task' : 'tasks';

      if (Platform.OS === 'web') {
        showWebNotification(
          'Tasks Due Today',
          `You have ${taskCount} ${taskWord} due today. Don't forget to take care of your plants!`
        );
      } else {
        await scheduleLocalNotification(
          'Tasks Due Today',
          `You have ${taskCount} ${taskWord} due today. Don't forget to take care of your plants!`,
          { screen: 'notifications' }
        );
      }
    } catch (error) {
      console.error('Error scheduling notification:', error);
    }
  };

  // Add notification response handler
  useEffect(() => {
    const subscription = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      if (data?.screen === 'notifications' || data?.type === 'due_tasks') {
        router.push('/notifications');
      }
    });

    return () => {
      subscription.remove();
    };
  }, [router]);

  const handleAddPlant = async (plantData: { name: string; environment: string; stage: string }) => {
    try {
      const now = new Date().toISOString();
      const newPlant = {
        id: Date.now(),
        name: plantData.name,
        dateAdded: now,
        lastWatered: 'Not watered yet',
        light: 'Medium',
        history: [{
          id: `add-${Date.now()}`,
          type: 'log' as const,
          date: now,
          description: 'Plant added to collection'
        }],
        environment: plantData.environment,
        stage: plantData.stage,
        lastFeeding: 'Not fed yet'
      };

      const updatedPlants = [newPlant, ...plants];
      setPlants(updatedPlants);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPlants));

      router.push(`/plant/${newPlant.id}`);
    } catch (error) {
      console.error('Error adding plant:', error);
      Alert.alert('Error', 'Failed to add plant. Please try again.');
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    // Add your refresh logic here
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const handleSettingsPress = () => {
    router.push('/settings');
  };

  const handleActionButtonPress = () => {
    setShowActionMenu(true);
  };

  const handlePhotoPress = () => {
    setShowActionMenu(false);
    router.push('/photo-assign');
  };

  const handleLogPress = () => {
    setShowActionMenu(false);
    router.push('/log-entry');
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#4CAF50']}
              tintColor="#4CAF50"
            />
          }
        >
          <View style={styles.hero}>
            <Image
              source={require('../assets/images/seedling.jpg')}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <View style={styles.heroContent}>
              <Text style={styles.heroTitle}>Welcome to GrowIt</Text>
              <Text style={styles.heroDescription}>Your personal plant care companion</Text>
            </View>
          </View>

          {/* Notification Info Banner */}
          {(() => {
            const notificationInfo = getNotificationCapabilityInfo();
            if (!notificationInfo.pushNotifications && Platform.OS === 'android') {
              return (
                <View style={styles.notificationBanner}>
                  <View style={styles.notificationBannerContent}>
                    <Ionicons name="information-circle" size={20} color="#FF9800" />
                    <View style={styles.notificationBannerText}>
                      <Text style={styles.notificationBannerTitle}>Notification Info</Text>
                      <Text style={styles.notificationBannerMessage}>
                        {notificationInfo.message}
                      </Text>
                    </View>
                    <TouchableOpacity
                      onPress={() => setShowNotificationInfo(true)}
                      style={styles.notificationBannerButton}
                    >
                      <Text style={styles.notificationBannerButtonText}>Learn More</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              );
            }
            return null;
          })()}

          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={handleActionButtonPress}
            >
              <View
                style={styles.actionButtonIcon}
                ref={actionButtonRef}
              >
                <Ionicons name="add-circle" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Action</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, plants.length === 0 && styles.actionButtonDisabled]}
              activeOpacity={0.7}
              disabled={plants.length === 0}
              onPress={handlePhotoPress}
            >
              <View style={[styles.actionButtonIcon, plants.length === 0 && styles.actionButtonIconDisabled]}>
                <Ionicons name="camera-outline" size={24} color={plants.length === 0 ? "#9E9E9E" : "#4CAF50"} />
              </View>
              <Text style={[styles.actionButtonText, plants.length === 0 && styles.actionButtonTextDisabled]}>Photo</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, plants.length === 0 && styles.actionButtonDisabled]}
              activeOpacity={0.7}
              disabled={plants.length === 0}
              onPress={handleLogPress}
            >
              <View style={[styles.actionButtonIcon, plants.length === 0 && styles.actionButtonIconDisabled]}>
                <Ionicons name="clipboard-outline" size={24} color={plants.length === 0 ? "#9E9E9E" : "#4CAF50"} />
              </View>
              <Text style={[styles.actionButtonText, plants.length === 0 && styles.actionButtonTextDisabled]}>Log</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={() => router.push('/diagnose')}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="search" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Diagnose</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.cardContainer}>
            <View style={styles.statsCard}>
              <View style={styles.statsGrid}>
                <TouchableOpacity
                  style={styles.statItem}
                  onPress={() => router.push('/plants')}
                >
                  <Text style={styles.statValue}>{stats.totalPlants}</Text>
                  <Text style={styles.statLabel}>Total Plants</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.statItem}
                  onPress={() => router.push('/notifications')}
                >
                  <Text style={styles.statValue}>{stats.notifications}</Text>
                  <Text style={styles.statLabel}>Notifications</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View style={styles.weatherSection}>
            <WeatherCard />
          </View>
        </ScrollView>
        <Footer currentScreen="home" plantCount={plants.length} />
      </SafeAreaView>

      <PlantInputModal
        visible={showAddPlantModal}
        onClose={() => setShowAddPlantModal(false)}
        onSubmit={handleAddPlant}
        availableEnvironments={environments}
      />

      {/* Action Menu */}
      <Modal
        visible={showActionMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowActionMenu(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowActionMenu(false)}
        >
          <View style={styles.actionMenuContainer}>
            <TouchableOpacity
              style={styles.actionMenuItem}
              onPress={() => {
                setShowActionMenu(false);
                setShowAddPlantModal(true);
              }}
            >
              <Ionicons name="leaf-outline" size={20} color="#2E7D32" />
              <Text style={styles.actionMenuItemText}>Add New Plant</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionMenuItem}
              onPress={() => {
                setShowActionMenu(false);
                router.push('/environment');
              }}
            >
              <Ionicons name="home-outline" size={20} color="#2E7D32" />
              <Text style={styles.actionMenuItemText}>Add New Environment</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionMenuItem, !plants.length && styles.actionMenuItemDisabled]}
              onPress={handlePhotoPress}
              disabled={!plants.length}
            >
              <Ionicons name="camera-outline" size={20} color={plants.length ? "#2E7D32" : "#9E9E9E"} />
              <Text style={[styles.actionMenuItemText, !plants.length && styles.actionMenuItemTextDisabled]}>
                Take Photo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionMenuItem, plants.length === 0 && styles.actionMenuItemDisabled]}
              disabled={plants.length === 0}
              onPress={handleLogPress}
            >
              <Ionicons name="clipboard-outline" size={20} color={plants.length === 0 ? "#9E9E9E" : "#2E7D32"} />
              <Text style={[styles.actionMenuItemText, plants.length === 0 && styles.actionMenuItemTextDisabled]}>Add Log</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionMenuItem}
              onPress={() => {
                setShowActionMenu(false);
                router.push('/diagnose');
              }}
            >
              <Ionicons name="search" size={20} color="#2E7D32" />
              <Text style={styles.actionMenuItemText}>Diagnose Plant</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {showSuccessAlert && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowSuccessAlert(false)}
        >
          <View style={styles.alertOverlay}>
            <View style={styles.alertContent}>
              <Text style={styles.alertEmoji}>{successMessage.emoji}</Text>
              <Text style={styles.alertTitle}>{successMessage.title}</Text>
              <Text style={styles.alertMessage}>{successMessage.message}</Text>
            </View>
          </View>
        </Modal>
      )}

      {/* Notification Info Modal */}
      <Modal
        visible={showNotificationInfo}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowNotificationInfo(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.notificationInfoModal}>
            <View style={styles.notificationInfoHeader}>
              <Text style={styles.notificationInfoTitle}>Notification Support</Text>
              <TouchableOpacity
                onPress={() => setShowNotificationInfo(false)}
                style={styles.notificationInfoCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.notificationInfoContent}>
              {(() => {
                const info = getNotificationCapabilityInfo();
                return (
                  <View>
                    <View style={styles.notificationInfoSection}>
                      <Text style={styles.notificationInfoSectionTitle}>Current Status</Text>
                      <View style={styles.notificationInfoItem}>
                        <Ionicons
                          name={info.localNotifications ? "checkmark-circle" : "close-circle"}
                          size={20}
                          color={info.localNotifications ? "#4CAF50" : "#F44336"}
                        />
                        <Text style={styles.notificationInfoItemText}>
                          Local Notifications: {info.localNotifications ? "Supported" : "Not Supported"}
                        </Text>
                      </View>
                      <View style={styles.notificationInfoItem}>
                        <Ionicons
                          name={info.pushNotifications ? "checkmark-circle" : "close-circle"}
                          size={20}
                          color={info.pushNotifications ? "#4CAF50" : "#F44336"}
                        />
                        <Text style={styles.notificationInfoItemText}>
                          Push Notifications: {info.pushNotifications ? "Supported" : "Not Supported"}
                        </Text>
                      </View>
                      <View style={styles.notificationInfoItem}>
                        <Ionicons
                          name={info.backgroundNotifications ? "checkmark-circle" : "close-circle"}
                          size={20}
                          color={info.backgroundNotifications ? "#4CAF50" : "#F44336"}
                        />
                        <Text style={styles.notificationInfoItemText}>
                          Background Notifications: {info.backgroundNotifications ? "Supported" : "Not Supported"}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.notificationInfoSection}>
                      <Text style={styles.notificationInfoSectionTitle}>What This Means</Text>
                      <Text style={styles.notificationInfoDescription}>
                        {info.message}
                      </Text>

                      {Platform.OS === 'android' && !info.pushNotifications && (
                        <View style={styles.notificationInfoHighlight}>
                          <Text style={styles.notificationInfoHighlightText}>
                            📱 With Expo SDK 53, Android push notifications are no longer supported in Expo Go.
                            Local notifications (like task reminders) still work perfectly!
                          </Text>
                        </View>
                      )}
                    </View>

                    <View style={styles.notificationInfoSection}>
                      <Text style={styles.notificationInfoSectionTitle}>What Still Works</Text>
                      <Text style={styles.notificationInfoDescription}>
                        • Task reminders and alerts{'\n'}
                        • Plant care notifications{'\n'}
                        • In-app notifications{'\n'}
                        • All core app functionality
                      </Text>
                    </View>

                    {Platform.OS === 'android' && !info.pushNotifications && (
                      <View style={styles.notificationInfoSection}>
                        <Text style={styles.notificationInfoSectionTitle}>For Full Notification Support</Text>
                        <Text style={styles.notificationInfoDescription}>
                          Consider using a development build for complete notification functionality, including:
                          {'\n'}• Background push notifications{'\n'}
                          • Advanced notification scheduling{'\n'}
                          • Custom notification sounds
                        </Text>
                      </View>
                    )}
                  </View>
                );
              })()}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32', // Grass green
  },
  safeArea: {
    backgroundColor: '#2E7D32', // Grass green
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
    gap: 4,
  },
  actionButtonIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  actionButtonText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  cardContainer: {
    padding: 16,
  },
  statsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 12,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    justifyContent: 'center',
  },
  statItem: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 12,
    marginBottom: 12,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#E8F5E9',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: '#4CAF50',
    textAlign: 'center',
  },
  emptyPlantsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 8,
    borderRadius: 12,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  emptyPlantsIcon: {
    marginBottom: 8,
  },
  emptyPlantsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  emptyPlantsText: {
    fontSize: 16,
    color: '#4CAF50',
    textAlign: 'center',
    marginBottom: 16,
  },
  addPlantButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    marginBottom: 16,
  },
  addPlantButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  actionButtonDisabled: {
    opacity: 0.7,
  },
  actionButtonIconDisabled: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
  },
  actionButtonTextDisabled: {
    color: '#9E9E9E',
  },
  addButtonContainer: {
    alignItems: 'center',
    padding: 20,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  weatherSection: {
    marginBottom: 24,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  actionMenuContainer: {
    position: 'absolute',
    top: 150, // Position below the action button
    left: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 10,
    width: 250,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  actionMenuItemDisabled: {
    opacity: 0.7,
  },
  actionMenuItemText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
  },
  actionMenuItemTextDisabled: {
    color: '#9E9E9E',
  },
  alertOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertContent: {
    backgroundColor: '#F1F8E9',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    width: '80%',
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  alertEmoji: {
    fontSize: 48,
    marginBottom: 16,
  },
  alertTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 8,
    textAlign: 'center',
  },
  alertMessage: {
    fontSize: 16,
    color: '#2E7D32',
    opacity: 0.9,
    textAlign: 'center',
  },
  // Notification Banner Styles
  notificationBanner: {
    backgroundColor: '#FFF3E0',
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  notificationBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    gap: 12,
  },
  notificationBannerText: {
    flex: 1,
  },
  notificationBannerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E65100',
    marginBottom: 2,
  },
  notificationBannerMessage: {
    fontSize: 12,
    color: '#BF360C',
    lineHeight: 16,
  },
  notificationBannerButton: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  notificationBannerButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },
  // Notification Info Modal Styles
  notificationInfoModal: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginVertical: 60,
    borderRadius: 12,
    maxHeight: '80%',
    overflow: 'hidden',
  },
  notificationInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  notificationInfoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  notificationInfoCloseButton: {
    padding: 4,
  },
  notificationInfoContent: {
    padding: 20,
  },
  notificationInfoSection: {
    marginBottom: 20,
  },
  notificationInfoSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  notificationInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  notificationInfoItemText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  notificationInfoDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  notificationInfoHighlight: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#2196F3',
  },
  notificationInfoHighlightText: {
    fontSize: 14,
    color: '#1565C0',
    lineHeight: 20,
  },
});