# Firebase Setup for GrowIt Strains Feature

This document provides instructions on how to set up Firebase for the strains feature in the GrowIt app.

## 1. Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" and follow the setup wizard
3. Give your project a name (e.g., "GrowIt")
4. Choose whether to enable Google Analytics (recommended)
5. Accept the terms and click "Create project"

## 2. Register Your App

1. In the Firebase console, click on the web icon (</>) to add a web app
2. Register your app with a nickname (e.g., "GrowIt Web")
3. You can optionally set up Firebase Hosting
4. Click "Register app"

## 3. Get Your Firebase Configuration

After registering your app, Firebase will provide you with a configuration object that looks like this:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef1234567890"
};
```

## 4. Update the Firebase Configuration in the App

1. Open the `firebaseConfig.ts` file in your project
2. Replace the placeholder values with your actual Firebase configuration:

```typescript
const firebaseConfig = {
  apiKey: "YOUR_ACTUAL_API_KEY",
  authDomain: "YOUR_ACTUAL_AUTH_DOMAIN",
  projectId: "YOUR_ACTUAL_PROJECT_ID",
  storageBucket: "YOUR_ACTUAL_STORAGE_BUCKET",
  messagingSenderId: "YOUR_ACTUAL_MESSAGING_SENDER_ID",
  appId: "YOUR_ACTUAL_APP_ID"
};
```

## 5. Set Up Firestore Database

1. In the Firebase console, go to "Firestore Database" in the left sidebar
2. Click "Create database"
3. Choose "Start in production mode" or "Start in test mode" (test mode is easier for development)
4. Select a location for your database (choose the one closest to your users)
5. Click "Enable"

## 6. Create the Strains Collection

1. In the Firestore Database, click "Start collection"
2. Enter "strains" as the Collection ID
3. Add a document with the following fields:
   - name (string)
   - description (string)
   - effects (string)
   - flavor (string)
   - thc (string)
   - cbd (string)
   - imageUrl (string, optional)
4. Click "Save"

## 7. Set Up Security Rules

For development, you can use these basic rules:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

For production, you should implement proper authentication and more restrictive rules.

## 8. Test Your Setup

1. Run your app
2. Navigate to the Strains page
3. Verify that your strains from Firebase are displayed correctly

## Troubleshooting

- If you see "Firebase App named '[DEFAULT]' already exists" error, make sure you're only initializing Firebase once in your app.
- If you can't connect to Firebase, check your internet connection and verify your configuration values.
- If you're getting permission errors, check your Firestore security rules.

## Additional Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Documentation](https://firebase.google.com/docs/firestore)
- [React Native Firebase](https://rnfirebase.io/) 