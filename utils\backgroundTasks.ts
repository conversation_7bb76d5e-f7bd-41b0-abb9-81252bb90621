import * as BackgroundTask from 'expo-background-task';
import * as TaskManager from 'expo-task-manager';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BACKGROUND_TASK_NAME = 'background-task';

// Define the task
TaskManager.defineTask(BACKGROUND_TASK_NAME, async () => {
  try {
    // Get all tasks from AsyncStorage
    const tasksJson = await AsyncStorage.getItem('tasks');
    if (!tasksJson) return BackgroundTask.BackgroundTaskResult.NoData;

    const tasks = JSON.parse(tasksJson);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Filter tasks that are due today and not completed
    const dueTasks = tasks.filter((task: any) => {
      const dueDate = new Date(task.dueDate);
      dueDate.setHours(0, 0, 0, 0);
      return dueDate.getTime() === today.getTime() && !task.completed;
    });

    if (dueTasks.length === 0) {
      return BackgroundTask.BackgroundTaskResult.NoData;
    }

    // Request notification permissions if not already granted
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      const { status: newStatus } = await Notifications.requestPermissionsAsync();
      if (newStatus !== 'granted') {
        return BackgroundTask.BackgroundTaskResult.Failed;
      }
    }

    // Schedule notifications for each due task
    for (const task of dueTasks) {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: `Task Due: ${task.plantName}`,
          body: task.description,
          data: { taskId: task.id },
        },
        trigger: null, // Send immediately
      });
    }

    return BackgroundTask.BackgroundTaskResult.NewData;
  } catch (error) {
    console.error('Background task error:', error);
    return BackgroundTask.BackgroundTaskResult.Failed;
  }
});

// Register the background task
export async function registerBackgroundTask() {
  try {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_TASK_NAME);
    if (!isRegistered) {
      await BackgroundTask.registerTaskAsync(BACKGROUND_TASK_NAME, {
        minimumInterval: 60 * 60, // 1 hour in seconds
      });
      console.log('Background task registered successfully');
    } else {
      console.log('Background task already registered');
    }
  } catch (err) {
    console.error('Background task registration failed:', err);
  }
}

// Unregister the background task
export async function unregisterBackgroundTask() {
  try {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_TASK_NAME);
    if (isRegistered) {
      await BackgroundTask.unregisterTaskAsync(BACKGROUND_TASK_NAME);
      console.log('Background task unregistered successfully');
    } else {
      console.log('No background task to unregister');
    }
  } catch (err) {
    console.error('Background task unregistration failed:', err);
  }
}