{"name": "growit", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@google/generative-ai": "^0.24.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/slider": "^4.5.6", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "axios": "^1.8.4", "date-fns": "^4.1.0", "expo": "^53.0.6", "expo-background-fetch": "~13.1.5", "expo-background-task": "~1.0.0", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-media-library": "~17.1.6", "expo-notifications": "~0.31.1", "expo-router": "~5.0.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-task-manager": "~13.1.5", "expo-web-browser": "~14.1.6", "firebase": "^11.6.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~53.0.4", "react-native-dotenv": "^3.4.11", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}}