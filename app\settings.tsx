import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Platform, Image, Switch, Alert, Modal, Clipboard } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Link } from 'expo-router';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';
import PlantInputModal from '../components/PlantInputModal';
import { Linking } from 'react-native';

interface Settings {
  notifications: boolean;
  darkMode: boolean;
  language: string;
  measurementUnit: 'C' | 'F';
  wateringReminders: boolean;
  feedingReminders: boolean;
}

const STORAGE_KEY = '@settings_data';
const TEMP_UNIT_PREF_KEY = '@temp_unit_preference';

const defaultSettings: Settings = {
  notifications: true,
  darkMode: false,
  language: 'English',
  measurementUnit: 'F',
  wateringReminders: true,
  feedingReminders: true,
};

const ToggleSwitch = ({ value, onToggle }: { value: boolean; onToggle: () => void }) => (
  <TouchableOpacity 
    onPress={onToggle}
    style={[
      styles.toggleSwitch,
      value && styles.toggleSwitchActive
    ]}
  >
    <View style={[
      styles.toggleThumb,
      value && styles.toggleThumbActive
    ]} />
  </TouchableOpacity>
);

export default function SettingsScreen() {
  const [settings, setSettings] = useState<Settings>(defaultSettings);
  const [useCelsius, setUseCelsius] = useState(false);
  const [plants, setPlants] = useState([]);
  const [showAboutModal, setShowAboutModal] = useState(false);
  const [showDonateModal, setShowDonateModal] = useState(false);
  const bitcoinAddress = '******************************************';

  // Load settings when component mounts
  useEffect(() => {
    loadSettings();
    loadTempPreference();
    loadPlants();
  }, []);

  // Reload settings when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Use a ref to track if the component is mounted
      const mountedRef = { current: true };
      
      const loadData = async () => {
        // Only load data if the component is still mounted
        if (mountedRef.current) {
          try {
            // Load data sequentially to avoid race conditions
            const storedSettings = await AsyncStorage.getItem(STORAGE_KEY);
            if (mountedRef.current && storedSettings) {
              setSettings(JSON.parse(storedSettings));
            }
            
            const storedPlants = await AsyncStorage.getItem('@plants_data');
            if (mountedRef.current && storedPlants) {
              setPlants(JSON.parse(storedPlants));
            }
          } catch (error) {
            console.error('Error loading data:', error);
          }
        }
      };

      // Load data with a slight delay to allow navigation to complete
      const timeoutId = setTimeout(loadData, 100);
      
      // Cleanup function
      return () => {
        mountedRef.current = false;
        clearTimeout(timeoutId);
      };
    }, [])
  );

  // Save settings to storage whenever they change
  useEffect(() => {
    saveSettings();
  }, [settings]);

  const loadSettings = async () => {
    try {
      const storedSettings = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedSettings) {
        setSettings(JSON.parse(storedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const saveSettings = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const loadTempPreference = async () => {
    try {
      const savedUnit = await AsyncStorage.getItem(TEMP_UNIT_PREF_KEY);
      setUseCelsius(savedUnit === 'C');
    } catch (error) {
      console.error('Error loading temperature preference:', error);
    }
  };

  const toggleTemperatureUnit = async () => {
    const newUnit = useCelsius ? 'F' : 'C';
    try {
      await AsyncStorage.setItem(TEMP_UNIT_PREF_KEY, newUnit);
      setUseCelsius(!useCelsius);
    } catch (error) {
      console.error('Error saving temperature preference:', error);
    }
  };

  const toggleSetting = (setting: keyof Settings) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const toggleNotifications = () => {
    setSettings(prev => ({
      ...prev,
      notifications: !prev.notifications,
      wateringReminders: !prev.notifications ? false : prev.wateringReminders,
      feedingReminders: !prev.notifications ? false : prev.feedingReminders,
    }));
  };

  const toggleWateringReminders = () => {
    setSettings(prev => ({
      ...prev,
      wateringReminders: !prev.wateringReminders,
      notifications: true,
    }));
  };

  const toggleFeedingReminders = () => {
    setSettings(prev => ({
      ...prev,
      feedingReminders: !prev.feedingReminders,
      notifications: true,
    }));
  };

  const toggleDarkMode = () => toggleSetting('darkMode');

  const handleBack = () => {
    // We'll use Link component instead of router.back()
  };

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem('@plants_data');
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const copyToClipboard = (text: string) => {
    Clipboard.setString(text);
    Alert.alert('Copied!', 'Bitcoin address copied to clipboard');
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Settings</Text>
            <Text style={styles.heroDescription}>Customize your app preferences</Text>
          </View>
        </View>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            <View style={styles.settingsCard}>
              <TouchableOpacity style={styles.settingButton} onPress={toggleDarkMode}>
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={styles.iconContainer}>
                      <Ionicons name="moon-outline" size={24} color="#4CAF50" />
                    </View>
                    <View>
                      <Text style={styles.settingText}>Dark Mode</Text>
                      <Text style={styles.settingDescription}>Toggle dark theme for the app</Text>
                    </View>
                  </View>
                  <View style={styles.settingValueContainer}>
                    <ToggleSwitch value={settings.darkMode} onToggle={toggleDarkMode} />
                  </View>
                </View>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton} 
              >
                <Link href="/notifications" asChild>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={styles.iconContainer}>
                        <Ionicons name="notifications-outline" size={24} color="#4CAF50" />
                      </View>
                      <View>
                        <Text style={styles.settingText}>Notifications</Text>
                      </View>
                    </View>
                    <View style={styles.settingValueContainer}>
                      <Text style={styles.settingValue}>{settings.notifications ? 'On' : 'Off'}</Text>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </View>
                </Link>
              </TouchableOpacity>

              <TouchableOpacity style={styles.settingButton}>
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={styles.iconContainer}>
                      <Ionicons name="language-outline" size={24} color="#4CAF50" />
                    </View>
                    <View>
                      <Text style={styles.settingText}>Language</Text>
                      <Text style={styles.settingDescription}>Change app language</Text>
                    </View>
                  </View>
                  <View style={styles.settingValueContainer}>
                    <Text style={styles.settingValue}>{settings.language}</Text>
                    <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                  </View>
                </View>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton}
                onPress={() => setSettings(prev => ({
                  ...prev,
                  measurementUnit: prev.measurementUnit === 'F' ? 'C' : 'F'
                }))}
              >
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={styles.iconContainer}>
                      <Ionicons name="thermometer-outline" size={24} color="#4CAF50" />
                    </View>
                    <View>
                      <Text style={styles.settingText}>Temperature Unit</Text>
                      <Text style={styles.settingDescription}>Switch between °F and °C</Text>
                    </View>
                  </View>
                  <View style={styles.settingValueContainer}>
                    <Text style={styles.settingValue}>{settings.measurementUnit === 'F' ? '°F' : '°C'}</Text>
                    <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                  </View>
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <View>
                  <Text style={styles.cardTitle}>Grow Settings</Text>
                  <Text style={styles.cardDescription}>Manage your plant care routines</Text>
                </View>
              </View>
            </View>

            <View style={styles.settingsCard}>
              <TouchableOpacity 
                style={styles.settingButton}
              >
                <Link href="/lighting" asChild>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={styles.iconContainer}>
                        <Ionicons name="sunny-outline" size={24} color="#4CAF50" />
                      </View>
                      <View>
                        <Text style={styles.settingText}>Lighting</Text>
                        <Text style={styles.settingDescription}>Set up light exposure schedules</Text>
                      </View>
                    </View>
                    <View style={styles.settingValueContainer}>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </View>
                </Link>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton}
              >
                <Link href="/watering" asChild>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={styles.iconContainer}>
                        <Ionicons name="water-outline" size={24} color="#4CAF50" />
                      </View>
                      <View>
                        <Text style={styles.settingText}>Watering</Text>
                        <Text style={styles.settingDescription}>Configure watering routines</Text>
                      </View>
                    </View>
                    <View style={styles.settingValueContainer}>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </View>
                </Link>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton}
              >
                <Link href="/feeding" asChild>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={styles.iconContainer}>
                        <Ionicons name="nutrition-outline" size={24} color="#4CAF50" />
                      </View>
                      <View>
                        <Text style={styles.settingText}>Feeding</Text>
                        <Text style={styles.settingDescription}>Set up fertilization schedules</Text>
                      </View>
                    </View>
                    <View style={styles.settingValueContainer}>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </View>
                </Link>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton}
              >
                <Link href="/environment" asChild>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={styles.iconContainer}>
                        <Ionicons name="thermometer-outline" size={24} color="#4CAF50" />
                      </View>
                      <View>
                        <Text style={styles.settingText}>Environment</Text>
                        <Text style={styles.settingDescription}>Manage temperature and humidity</Text>
                      </View>
                    </View>
                    <View style={styles.settingValueContainer}>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </View>
                </Link>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={[styles.card, { marginTop: 20 }]}>
            <Text style={styles.cardTitle}>About GrowIt</Text>
            <Text style={[styles.cardDescription, { marginBottom: 16 }]}>Learn more about the app and its features</Text>
            <TouchableOpacity 
              style={styles.settingButton}
              onPress={() => setShowAboutModal(true)}
            >
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <View style={styles.iconContainer}>
                    <Ionicons name="information-circle-outline" size={24} color="#4CAF50" />
                  </View>
                  <View>
                    <Text style={styles.settingText}>App Information</Text>
                    <Text style={styles.settingDescription}>Version, credits, and more</Text>
                  </View>
                </View>
                <View style={styles.settingValueContainer}>
                  <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                </View>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.settingButton, { marginTop: 12 }]}
              onPress={() => setShowDonateModal(true)}
            >
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <View style={styles.iconContainer}>
                    <Ionicons name="qr-code-outline" size={24} color="#4CAF50" />
                  </View>
                  <View>
                    <Text style={styles.settingText}>Help GrowIt Grow</Text>
                    <Text style={styles.settingDescription}>Donate Bitcoin or Buy us a Coffee!</Text>
                  </View>
                </View>
                <View style={styles.settingValueContainer}>
                  <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                </View>
              </View>
            </TouchableOpacity>
          </View>

          <View style={[styles.card, { marginTop: 20 }]}>
            <Text style={styles.cardTitle}>Share GrowIt</Text>
            <Text style={styles.cardDescription}>Share GrowIt with friends and family!</Text>
            <TouchableOpacity 
              style={styles.supportButton}
              onPress={() => {
                Linking.openURL('https://growit.webwerkscloud.com/');
              }}
            >
              <Text style={styles.supportButtonText}>Visit Website</Text>
            </TouchableOpacity>
          </View>

          <View style={[styles.card, { marginTop: 20 }]}>
            <Text style={styles.cardTitle}>Help GrowIt Grow</Text>
            <Text style={styles.cardDescription}>Buy us a Coffee or Send us some Bitcoin!</Text>
            <TouchableOpacity 
              style={styles.supportButton}
              onPress={() => {
                Linking.openURL('https://buymeacoffee.com/thomaswfryh');
              }}
            >
              <Text style={styles.supportButtonText}>Buy us a Coffee</Text>
            </TouchableOpacity>
            
            <View style={styles.bitcoinContainer}>
              <Image 
                source={{ uri: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=bitcoin:******************************************` }}
                style={styles.bitcoinQR}
              />
              <Text style={styles.bitcoinAddress}>******************************************</Text>
              <TouchableOpacity 
                style={styles.copyButton}
                onPress={() => copyToClipboard(bitcoinAddress)}
              >
                <View style={styles.copyButtonContent}>
                  <Ionicons name="copy-outline" size={20} color="#fff" />
                  <Text style={styles.copyButtonText}>Copy Address</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="settings" plantCount={plants.length} />
      </SafeAreaView>
      
      {/* About Modal */}
      <Modal
        visible={showAboutModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowAboutModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <View style={styles.modalIconContainer}>
                <Ionicons name="leaf" size={32} color="#4CAF50" />
              </View>
              <Text style={styles.modalTitle}>About GrowIt</Text>
              <TouchableOpacity 
                style={styles.modalCloseButton}
                onPress={() => setShowAboutModal(false)}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={styles.modalText}>
                GrowIt is your personal plant care assistant, helping you grow healthy and beautiful plants.
              </Text>
              
              <Text style={styles.modalSectionTitle}>Features</Text>
              <View style={styles.featureList}>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Plant health monitoring</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Watering schedule tracking</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Feeding reminders</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Environment management</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Disease diagnosis</Text>
                </View>
              </View>
              
              <View style={styles.modalFooter}>
                <Text style={styles.modalVersion}>Version: 1.0.42</Text>
                <Text style={styles.modalCopyright}>© 2025 GrowIt App</Text>
              </View>
            </ScrollView>
            
            <TouchableOpacity 
              style={styles.modalButton}
              onPress={() => setShowAboutModal(false)}
            >
              <Text style={styles.modalButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      
      {/* Donate Modal */}
      <Modal
        visible={showDonateModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDonateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <View style={styles.modalIconContainer}>
                <Ionicons name="heart" size={32} color="#4CAF50" />
              </View>
              <Text style={styles.modalTitle}>Help GrowIt Grow</Text>
              <TouchableOpacity 
                style={styles.modalCloseButton}
                onPress={() => setShowDonateModal(false)}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={styles.modalSectionTitle}>Buy Us a Coffee</Text>
              <Text style={styles.modalText}>
                Support GrowIt with a one-time donation through Buy Us a Coffee.
              </Text>
              <TouchableOpacity 
                style={[styles.coffeeButton, { marginBottom: 20 }]}
                onPress={() => Linking.openURL('https://buymeacoffee.com/thomaswfryh')}
              >
                <Ionicons name="cafe-outline" size={20} color="#fff" />
                <Text style={styles.coffeeButtonText}>Buy us a Coffee</Text>
              </TouchableOpacity>
              
              <Text style={styles.modalText}>
                Your support helps us continue developing and improving GrowIt. Thank you for your generosity!
              </Text>
              
              <Text style={styles.modalSectionTitle}>Bitcoin Donation</Text>
              <View style={styles.qrContainer}>
                <Image 
                  source={{ uri: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${bitcoinAddress}` }}
                  style={styles.qrCode}
                />
              </View>
              
              <View style={styles.addressContainer}>
                <Text style={styles.addressLabel}>Bitcoin Address:</Text>
                <Text style={styles.addressText}>{bitcoinAddress}</Text>
                <TouchableOpacity 
                  style={styles.copyButton}
                  onPress={() => copyToClipboard(bitcoinAddress)}
                >
                  <View style={styles.copyButtonContent}>
                    <Ionicons name="copy-outline" size={20} color="#fff" />
                    <Text style={styles.copyButtonText}>Copy Address</Text>
                  </View>
                </TouchableOpacity>
              </View>
              
              <Text style={styles.modalNote}>
                Scan the QR code with your Bitcoin wallet app or copy the address to make a donation.
              </Text>
            </ScrollView>
            
            <TouchableOpacity 
              style={styles.modalButton}
              onPress={() => setShowDonateModal(false)}
            >
              <Text style={styles.modalButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 120,
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: '#E8F5E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#2E7D32',
  },
  cardDescription: {
    fontSize: 14,
    color: '#4CAF50',
  },
  settingsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 8,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    gap: 8,
  },
  settingButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: 16,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  settingText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  settingValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 2,
  },
  settingValue: {
    fontSize: 14,
    color: '#666',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  toggleSwitch: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#E0E0E0',
    padding: 2,
    justifyContent: 'center',
    marginTop: 2,
  },
  toggleSwitchActive: {
    backgroundColor: '#4CAF50',
  },
  toggleThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    boxShadow: '0px 2px 2px rgba(0, 0, 0, 0.2)',
    elevation: 3,
  },
  toggleThumbActive: {
    transform: [{ translateX: 22 }],
  },
  divider: {
    height: 1,
    backgroundColor: '#E8F5E9',
    marginVertical: 12,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  sectionTitleText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E8F5E9',
    paddingBottom: 15,
  },
  modalIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
    flex: 1,
    textAlign: 'center',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalBody: {
    marginBottom: 20,
  },
  modalText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 20,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 15,
  },
  featureList: {
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  modalFooter: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  modalVersion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  modalCopyright: {
    fontSize: 14,
    color: '#666',
  },
  modalButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  qrContainer: {
    alignItems: 'center',
    marginVertical: 20,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  qrCode: {
    width: 200,
    height: 200,
  },
  addressContainer: {
    marginBottom: 20,
  },
  addressLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  addressText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  copyButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
    alignSelf: 'center',
  },
  copyButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  copyButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: 'bold',
    marginLeft: 6,
  },
  modalNote: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 10,
  },
  coffeeButton: {
    flexDirection: 'row',
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    width: '80%',
    alignSelf: 'center',
  },
  coffeeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
    textAlign: 'center',
  },
  supportButton: {
    backgroundColor: '#2E7D32',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 20,
    alignSelf: 'center',
    width: '70%',
  },
  supportButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  centeredText: {
    textAlign: 'center',
  },
  bitcoinContainer: {
    alignItems: 'center',
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    width: '100%',
  },
  bitcoinQR: {
    width: 160,
    height: 160,
    marginBottom: 10,
  },
  bitcoinAddress: {
    fontSize: 14,
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
}); 