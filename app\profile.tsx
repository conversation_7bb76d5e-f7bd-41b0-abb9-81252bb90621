import React, { StyleSheet, Text, View, TouchableOpacity, ScrollView, Platform, Image, Modal } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Link } from 'expo-router';

// Helper function to format time for human-readable display
const formatTime = (isoString: string) => {
  const date = new Date(isoString);
  const now = new Date();
  
  // Set both dates to midnight to get accurate day difference
  const dateMidnight = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const nowMidnight = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const diffInDays = Math.floor((nowMidnight.getTime() - dateMidnight.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    return `Today, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric' })}`;
  } else if (diffInDays === 1) {
    return `Yesterday, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric' })}`;
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  }
};

interface Plant {
  id: number;
  name: string;
  dateAdded: string;
  lastWatered: string;
  light: string;
  history?: { 
    id: string;
    type: 'watering' | 'feeding' | 'photo' | 'stage' | 'log';
    date: string;
    description: string;
    photoUrl?: string;
    stage?: string;
  }[];
}

const STORAGE_KEY = '@plants_data';
const NOTIFICATIONS_STORAGE_KEY = '@notifications_data';

export default function ProfileScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState({
    totalPlants: 0,
    notifications: 0,
    totalWaterings: 0,
    totalFeedings: 0,
    totalPhotos: 0,
    totalLogs: 0,
    streakDays: 0,
    lastActivityDate: null as string | null,
  });
  const [showAboutModal, setShowAboutModal] = useState(false);

  // Load plants and notifications from storage when component mounts
  useEffect(() => {
    loadPlants();
    loadNotifications();
  }, []);

  // Reload plants and notifications when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Use a ref to track if the component is mounted
      const mountedRef = { current: true };
      
      const loadData = async () => {
        // Only load data if the component is still mounted
        if (mountedRef.current) {
          try {
            // Load data sequentially to avoid race conditions
            const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
            if (mountedRef.current && storedPlants) {
              setPlants(JSON.parse(storedPlants));
            }
            
            const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
            if (mountedRef.current && storedData) {
              const data = JSON.parse(storedData);
              if (Array.isArray(data)) {
                setNotifications(data);
              }
            }
          } catch (error) {
            console.error('Error loading data:', error);
          }
        }
      };

      // Load data with a slight delay to allow navigation to complete
      const timeoutId = setTimeout(loadData, 100);
      
      // Cleanup function
      return () => {
        mountedRef.current = false;
        clearTimeout(timeoutId);
      };
    }, [])
  );

  // Update stats whenever plants or notifications change
  useEffect(() => {
    updateStats();
  }, [plants, notifications]);

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const loadNotifications = async () => {
    try {
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      if (storedData) {
        const data = JSON.parse(storedData);
        if (Array.isArray(data)) {
          setNotifications(data);
        }
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  const updateStats = () => {
    const now = new Date();
    let totalWaterings = 0;
    let totalFeedings = 0;
    let totalPhotos = 0;
    let totalLogs = 0;
    let lastActivityDate: string | null = null;
    
    // Calculate activity counts
    plants.forEach(plant => {
      if (plant.history && plant.history.length > 0) {
        plant.history.forEach(activity => {
          if (activity.type === 'watering') totalWaterings++;
          if (activity.type === 'feeding') totalFeedings++;
          if (activity.type === 'photo') totalPhotos++;
          if (activity.type === 'log') totalLogs++;
          
          // Track most recent activity date
          if (!lastActivityDate || new Date(activity.date) > new Date(lastActivityDate)) {
            lastActivityDate = activity.date;
          }
        });
      }
    });
    
    // Calculate streak (simplified - can be enhanced with more complex logic)
    let streakDays = 0;
    if (lastActivityDate) {
      const lastActivity = new Date(lastActivityDate);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - lastActivity.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      // If activity was today or yesterday, count as streak
      if (diffDays <= 1) {
        streakDays = 1;
      }
    }
    
    const stats = {
      totalPlants: plants.length,
      notifications: notifications.length,
      totalWaterings,
      totalFeedings,
      totalPhotos,
      totalLogs,
      streakDays,
      lastActivityDate,
    };
    setStats(stats);
  };

  // Function to get the most recent activity for each plant
  const getRecentActivities = () => {
    const activities: {
      plantId: number;
      plantName: string;
      lastActivity: {
        type: 'watering' | 'feeding' | 'photo' | 'stage' | 'log';
        date: string;
        description: string;
      };
    }[] = [];

    plants.forEach(plant => {
      if (plant.history && plant.history.length > 0) {
        // Sort history by date (most recent first)
        const sortedHistory = [...plant.history].sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        
        // Get the most recent activity
        const lastActivity = sortedHistory[0];
        
        activities.push({
          plantId: plant.id,
          plantName: plant.name,
          lastActivity: {
            type: lastActivity.type,
            date: lastActivity.date,
            description: lastActivity.description
          }
        });
      }
    });

    // Sort activities by date (most recent first)
    return activities.sort((a, b) => 
      new Date(b.lastActivity.date).getTime() - new Date(a.lastActivity.date).getTime()
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Profile</Text>
            <Text style={styles.heroDescription}>Your plant care journey</Text>
          </View>
        </View>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            {/* User Profile Section */}
            <View style={styles.profileCard}>
              <View style={styles.profileAvatarContainer}>
                <View style={styles.profileAvatar}>
                  <Ionicons name="person" size={40} color="#4CAF50" />
                </View>
                <View style={styles.profileInfo}>
                  <Text style={styles.profileName}>Plant Enthusiast</Text>
                  <Text style={styles.profileBio}>Caring for {stats.totalPlants} plants</Text>
                </View>
              </View>
              <View style={styles.streakContainer}>
                <Ionicons name="flame" size={20} color="#FF9800" />
                <Text style={styles.streakText}>{stats.streakDays} day streak</Text>
              </View>
            </View>
            
            {/* Stats Card */}
            <View style={styles.statsCard}>
              <TouchableOpacity 
                style={styles.statItem}
              >
                <View style={styles.statIconContainer}>
                  <Ionicons name="leaf" size={24} color="#4CAF50" />
                </View>
                <View>
                  <Text style={styles.statValue}>{stats.totalPlants}</Text>
                  <Text style={styles.statLabel}>Total Plants</Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.statItem}
              >
                <View style={styles.statIconContainer}>
                  <Ionicons name="notifications-outline" size={24} color="#4CAF50" />
                </View>
                <View>
                  <Text style={styles.statValue}>{stats.notifications}</Text>
                  <Text style={styles.statLabel}>Tasks</Text>
                </View>
              </TouchableOpacity>
            </View>
            
            {/* Activity Stats Card */}
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <View>
                  <Text style={styles.cardTitle}>Activity Stats</Text>
                  <Text style={styles.cardDescription}>Your plant care activities</Text>
                </View>
              </View>
            </View>
            
            <View style={styles.activityStatsCard}>
              <View style={styles.activityStatItem}>
                <View style={[styles.activityIconContainer, { backgroundColor: '#E3F2FD' }]}>
                  <Ionicons name="water" size={20} color="#2196F3" />
                </View>
                <View>
                  <Text style={styles.activityStatValue}>{stats.totalWaterings}</Text>
                  <Text style={styles.activityStatLabel}>Waterings</Text>
                </View>
              </View>
              
              <View style={styles.activityStatItem}>
                <View style={[styles.activityIconContainer, { backgroundColor: '#FFF3E0' }]}>
                  <Ionicons name="nutrition" size={20} color="#FF9800" />
                </View>
                <View>
                  <Text style={styles.activityStatValue}>{stats.totalFeedings}</Text>
                  <Text style={styles.activityStatLabel}>Feedings</Text>
                </View>
              </View>
              
              <View style={styles.activityStatItem}>
                <View style={[styles.activityIconContainer, { backgroundColor: '#E8F5E9' }]}>
                  <Ionicons name="camera" size={20} color="#4CAF50" />
                </View>
                <View>
                  <Text style={styles.activityStatValue}>{stats.totalPhotos}</Text>
                  <Text style={styles.activityStatLabel}>Photos</Text>
                </View>
              </View>
              
              <View style={styles.activityStatItem}>
                <View style={[styles.activityIconContainer, { backgroundColor: '#F3E5F5' }]}>
                  <Ionicons name="document-text" size={20} color="#9C27B0" />
                </View>
                <View>
                  <Text style={styles.activityStatValue}>{stats.totalLogs}</Text>
                  <Text style={styles.activityStatLabel}>Logs</Text>
                </View>
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <View>
                  <Text style={styles.cardTitle}>Recent Activity</Text>
                  <Text style={styles.cardDescription}>Your latest plant care actions</Text>
                </View>
              </View>
            </View>

            <View style={styles.settingsCard}>
              <TouchableOpacity 
                style={styles.settingButton}
              >
                <Link href="/notifications" asChild>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={[styles.iconContainer, { backgroundColor: '#E3F2FD' }]}>
                        <Ionicons name="notifications-outline" size={24} color="#2196F3" />
                      </View>
                      <View>
                        <Text style={styles.settingText}>Notification Settings</Text>
                        <Text style={styles.settingDescription}>Manage your notification preferences</Text>
                      </View>
                    </View>
                    <View style={styles.settingValueContainer}>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </View>
                </Link>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.settingButton}
              >
                <Link href="/settings" asChild>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={[styles.iconContainer, { backgroundColor: '#E8F5E9' }]}>
                        <Ionicons name="color-palette-outline" size={24} color="#4CAF50" />
                      </View>
                      <View>
                        <Text style={styles.settingText}>Appearance</Text>
                        <Text style={styles.settingDescription}>Customize the app's look and feel</Text>
                      </View>
                    </View>
                    <View style={styles.settingValueContainer}>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </View>
                </Link>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.settingButton}
                onPress={() => setShowAboutModal(true)}
              >
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={[styles.iconContainer, { backgroundColor: '#FFF3E0' }]}>
                      <Ionicons name="help-circle-outline" size={24} color="#FF9800" />
                    </View>
                    <View>
                      <Text style={styles.settingText}>Help & Support</Text>
                      <Text style={styles.settingDescription}>Get help with using the app</Text>
                    </View>
                  </View>
                  <View style={styles.settingValueContainer}>
                    <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="profile" plantCount={plants.length} />
      </SafeAreaView>
      
      {/* About Modal */}
      <Modal
        visible={showAboutModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowAboutModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <View style={styles.modalIconContainer}>
                <Ionicons name="leaf" size={32} color="#4CAF50" />
              </View>
              <Text style={styles.modalTitle}>About GrowIt</Text>
              <TouchableOpacity 
                style={styles.modalCloseButton}
                onPress={() => setShowAboutModal(false)}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={styles.modalText}>
                GrowIt is your personal plant care assistant, helping you grow healthy and beautiful plants.
              </Text>
              
              <Text style={styles.modalSectionTitle}>Features</Text>
              <View style={styles.featureList}>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Plant health monitoring</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Watering schedule tracking</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Feeding reminders</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Environment management</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Disease diagnosis</Text>
                </View>
              </View>
              
              <View style={styles.modalFooter}>
                <Text style={styles.modalVersion}>Version: 1.0.42</Text>
                <Text style={styles.modalCopyright}>© 2025 GrowIt App</Text>
              </View>
            </ScrollView>
            
            <TouchableOpacity 
              style={styles.modalButton}
              onPress={() => setShowAboutModal(false)}
            >
              <Text style={styles.modalButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 100,
  },
  cardContainer: {
    gap: 12,
    paddingTop: 16,
  },
  card: {
    backgroundColor: '#E8F5E9',
    borderRadius: 10,
    padding: 12,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#2E7D32',
    textAlign: 'center',
  },
  cardDescription: {
    fontSize: 13,
    color: '#4CAF50',
    textAlign: 'center',
  },
  profileCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 16,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  profileAvatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  profileAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  profileInfo: {
    flexDirection: 'column',
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  profileBio: {
    fontSize: 14,
    color: '#666',
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  streakText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FF9800',
  },
  statsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 12,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 1,
    marginBottom: 8,
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 2,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  activityStatsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 12,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'space-between',
  },
  activityStatItem: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 1,
    marginBottom: 8,
  },
  activityIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activityStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  activityStatLabel: {
    fontSize: 12,
    color: '#666',
  },
  settingsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 10,
    padding: 6,
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.1)',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    gap: 6,
  },
  settingButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 1,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 10,
    flex: 1,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  wateringIcon: {
    backgroundColor: '#E3F2FD',
  },
  feedingIcon: {
    backgroundColor: '#FFF3E0',
  },
  photoIcon: {
    backgroundColor: '#E8F5E9',
  },
  stageIcon: {
    backgroundColor: '#F3E5F5',
  },
  logIcon: {
    backgroundColor: '#ECEFF1',
  },
  settingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 2,
    textAlign: 'left',
  },
  settingDescription: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    textAlign: 'left',
  },
  settingValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E8F5E9',
    paddingBottom: 15,
  },
  modalIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
    flex: 1,
    textAlign: 'center',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalBody: {
    marginBottom: 20,
  },
  modalText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 20,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 15,
  },
  featureList: {
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  modalFooter: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  modalVersion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  modalCopyright: {
    fontSize: 14,
    color: '#666',
  },
  modalButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 