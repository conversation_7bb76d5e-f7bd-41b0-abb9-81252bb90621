import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import Footer from '../../components/Footer';

const SYMPTOM_CATEGORIES = {
  leaf: {
    title: 'Leaf Problems',
    description: 'Issues affecting leaves, such as discoloration, spots, or deformities',
    icon: 'leaf',
    symptoms: [
      {
        id: 'yellow-leaves',
        title: 'Yellow Leaves',
        description: 'Leaves turning yellow, often starting from the tips or edges',
        causes: [
          'Overwatering',
          'Nutrient deficiency',
          'Poor drainage',
          'Root problems'
        ]
      },
      {
        id: 'brown-spots',
        title: 'Brown Spots',
        description: 'Brown or black spots appearing on leaves',
        causes: [
          'Fungal infection',
          'Bacterial infection',
          'Sunburn',
          'Nutrient burn'
        ]
      },
      {
        id: 'leaf-curling',
        title: 'Leaf Curling',
        description: 'Leaves curling up or down',
        causes: [
          'Water stress',
          'Temperature stress',
          'Pest infestation',
          'Virus infection'
        ]
      }
    ]
  },
  growth: {
    title: 'Growth Issues',
    description: 'Problems with plant growth and development',
    icon: 'trending-up',
    symptoms: [
      {
        id: 'stunted-growth',
        title: 'Stunted Growth',
        description: 'Plant not growing to expected size',
        causes: [
          'Poor soil quality',
          'Insufficient light',
          'Root bound',
          'Nutrient deficiency'
        ]
      },
      {
        id: 'leggy-growth',
        title: 'Leggy Growth',
        description: 'Long, thin stems with sparse leaves',
        causes: [
          'Insufficient light',
          'Temperature too high',
          'Over-fertilization',
          'Genetic factors'
        ]
      }
    ]
  }
};

export default function SymptomCategoryScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const category = SYMPTOM_CATEGORIES[id as keyof typeof SYMPTOM_CATEGORIES];

  if (!category) {
    return (
      <SafeAreaView style={styles.container}>
        <Header showBackButton />
        <View style={styles.content}>
          <Text style={styles.errorText}>Category not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const handleSymptomPress = (symptomId: string) => {
    router.push(`/plant-issue/${symptomId}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header showBackButton />
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <View style={styles.categoryIcon}>
            <Ionicons name={category.icon as any} size={32} color="#4CAF50" />
          </View>
          <Text style={styles.title}>{category.title}</Text>
          <Text style={styles.description}>{category.description}</Text>
        </View>

        <View style={styles.symptomsContainer}>
          {category.symptoms.map((symptom) => (
            <TouchableOpacity
              key={symptom.id}
              style={styles.symptomCard}
              onPress={() => handleSymptomPress(symptom.id)}
            >
              <View style={styles.symptomContent}>
                <Text style={styles.symptomTitle}>{symptom.title}</Text>
                <Text style={styles.symptomDescription}>{symptom.description}</Text>
                <View style={styles.causesContainer}>
                  <Text style={styles.causesTitle}>Common Causes:</Text>
                  {symptom.causes.map((cause, index) => (
                    <View key={index} style={styles.causeItem}>
                      <Ionicons name="alert-circle" size={16} color="#FF9800" />
                      <Text style={styles.causeText}>{cause}</Text>
                    </View>
                  ))}
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#C8E6C9" />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
      <Footer />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  symptomsContainer: {
    gap: 16,
  },
  symptomCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  symptomContent: {
    flex: 1,
  },
  symptomTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  symptomDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 20,
  },
  causesContainer: {
    marginTop: 8,
  },
  causesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  causeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  causeText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
}); 