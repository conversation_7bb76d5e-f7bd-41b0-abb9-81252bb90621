import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Modal, Alert, Image } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import Header from '../components/Header';
import Footer from '../components/Footer';

interface FeedingSchedule {
  id: string;
  plantName: string;
  frequency: string;
  amount: string;
  days: string[];
  description: string;
  nutrients: string[];
}

const STORAGE_KEY = '@feeding_schedules';

const sampleSchedules: FeedingSchedule[] = [
  {
    id: '1',
    plantName: 'Seedling Stage',
    frequency: 'Weekly',
    amount: '1/4 strength',
    days: ['Monday'],
    description: 'Weeks 1-2: Light feeding to establish roots',
    nutrients: ['Grow (N-P-K: 2-1-1)']
  },
  {
    id: '2',
    plantName: 'Vegetative Stage',
    frequency: 'Twice Weekly',
    amount: 'Full strength',
    days: ['Monday', 'Thursday'],
    description: 'Weeks 2-8: Regular feeding for growth',
    nutrients: ['Grow (N-P-K: 3-1-1)', 'Cal-Mag']
  },
  {
    id: '3',
    plantName: 'Flowering Stage',
    frequency: 'Twice Weekly',
    amount: 'Full strength',
    days: ['Monday', 'Thursday'],
    description: 'Weeks 8-16: Balanced feeding for flowering',
    nutrients: ['Bloom (N-P-K: 1-3-2)', 'Cal-Mag', 'PK Boost']
  },
  {
    id: '4',
    plantName: 'Late Flowering',
    frequency: 'Weekly',
    amount: '1/2 strength',
    days: ['Monday'],
    description: 'Final 1-2 weeks: Reduced feeding for flush',
    nutrients: ['Bloom (N-P-K: 1-3-2)', 'Cal-Mag']
  }
];

export default function FeedingScreen() {
  const [schedules, setSchedules] = useState<FeedingSchedule[]>([]);
  const [selectedSchedule, setSelectedSchedule] = useState<FeedingSchedule | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [showComingSoonModal, setShowComingSoonModal] = useState(false);
  const [plants, setPlants] = useState([]);
  const router = useRouter();

  useEffect(() => {
    loadSchedules();
    loadPlants();
  }, []);

  useEffect(() => {
    saveSchedules();
  }, [schedules]);

  const loadSchedules = async () => {
    try {
      // Clear existing schedules
      await AsyncStorage.removeItem(STORAGE_KEY);
      console.log('Cleared existing schedules');
      
      // Set new default schedules
      console.log('Setting new default schedules');
      setSchedules(sampleSchedules);
      await saveSchedules();
    } catch (error) {
      console.error('Error loading feeding schedules:', error);
      console.log('Setting sample schedules due to error');
      setSchedules(sampleSchedules);
    }
  };

  const saveSchedules = async () => {
    try {
      console.log('Saving schedules:', schedules);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(schedules));
    } catch (error) {
      console.error('Error saving feeding schedules:', error);
    }
  };

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem('@plants_data');
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const handleSchedulePress = (schedule: FeedingSchedule) => {
    setSelectedSchedule(schedule);
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSelectedSchedule(null);
  };

  const handleAddSchedule = () => {
    // Show custom modal instead of Alert.alert
    setShowComingSoonModal(true);
  };

  const closeAddModal = () => {
    setIsAddModalVisible(false);
  };

  const closeComingSoonModal = () => {
    setShowComingSoonModal(false);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Feeding Schedules</Text>
            <Text style={styles.heroDescription}>Manage your plant nutrient schedules</Text>
          </View>
        </View>
        
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <View>
                  <Text style={styles.cardTitle}></Text>
                  <Text style={styles.cardDescription}></Text>
                </View>
                <TouchableOpacity 
                  style={styles.addButton}
                  onPress={handleAddSchedule}
                >
                  <Ionicons name="flask-outline" size={16} color="#fff" />
                  <Text style={styles.addButtonText}>Nutrient Recipes</Text>
                </TouchableOpacity>
              </View>
              
              {schedules.length === 0 ? (
                <View style={styles.emptyState}>
                  <Ionicons name="nutrition-outline" size={48} color="#4CAF50" />
                  <Text style={styles.emptyStateText}>No feeding schedules yet</Text>
                  <Text style={styles.emptyStateDescription}>
                    Add your first feeding schedule to get started
                  </Text>
                  <TouchableOpacity 
                    style={styles.emptyStateButton}
                    onPress={handleAddSchedule}
                  >
                    <Text style={styles.emptyStateButtonText}>Add Schedule</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.scheduleList}>
                  {schedules.map((schedule) => (
                    <TouchableOpacity 
                      key={schedule.id} 
                      style={styles.scheduleItem}
                      onPress={() => handleSchedulePress(schedule)}
                    >
                      <View style={styles.scheduleIcon}>
                        <Ionicons 
                          name="nutrition" 
                          size={24} 
                          color="#4CAF50" 
                        />
                      </View>
                      <View style={styles.scheduleInfo}>
                        <Text style={styles.scheduleName}>{schedule.plantName}</Text>
                        <Text style={styles.scheduleDescription}>{schedule.description}</Text>
                        <View style={styles.scheduleDetails}>
                          <Text style={styles.scheduleDetail}>
                            <Ionicons name="time-outline" size={16} color="#666" /> {schedule.frequency}
                          </Text>
                          <Text style={styles.scheduleDetail}>
                            <Ionicons name="flask-outline" size={16} color="#666" /> {schedule.amount}
                          </Text>
                        </View>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="home" plantCount={plants.length} />

        <Modal
          animationType="fade"
          transparent={true}
          visible={isModalVisible}
          onRequestClose={closeModal}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{selectedSchedule?.plantName}</Text>
                <TouchableOpacity onPress={closeModal}>
                  <Ionicons name="close" size={24} color="#4CAF50" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.modalBody}>
                <View style={styles.detailRow}>
                  <Ionicons name="nutrition-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Schedule</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.description}</Text>
                  </View>
                </View>

                <View style={styles.detailRow}>
                  <Ionicons name="time-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Frequency</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.frequency}</Text>
                  </View>
                </View>

                <View style={styles.detailRow}>
                  <Ionicons name="flask-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Amount</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.amount}</Text>
                  </View>
                </View>

                <View style={styles.detailRow}>
                  <Ionicons name="calendar-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Days</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.days.join(', ')}</Text>
                  </View>
                </View>

                <View style={styles.detailRow}>
                  <Ionicons name="leaf-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Nutrients</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.nutrients.join(', ')}</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </Modal>

        {/* Coming Soon Modal */}
        <Modal
          visible={showComingSoonModal}
          transparent={true}
          animationType="fade"
          onRequestClose={closeComingSoonModal}
        >
          <View style={styles.comingSoonModalContainer}>
            <View style={styles.comingSoonModalContent}>
              <View style={styles.comingSoonIconContainer}>
                <Ionicons name="flask-outline" size={40} color="#4CAF50" />
              </View>
              <Text style={styles.comingSoonTitle}>Nutrient Recipes</Text>
              <Text style={styles.comingSoonText}>This feature is coming soon! Stay tuned for updates.</Text>
              <TouchableOpacity 
                style={styles.comingSoonButton}
                onPress={closeComingSoonModal}
              >
                <Text style={styles.comingSoonButtonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 120,
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  cardDescription: {
    fontSize: 14,
    color: '#4CAF50',
  },
  addButton: {
    flexDirection: 'row',
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  emptyStateButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scheduleList: {
    gap: 12,
  },
  scheduleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  scheduleIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  scheduleInfo: {
    flex: 1,
  },
  scheduleName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  scheduleDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  scheduleDetails: {
    flexDirection: 'row',
    gap: 12,
  },
  scheduleDetail: {
    fontSize: 12,
    color: '#666',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalBody: {
    gap: 20,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailText: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: '#9E9E9E',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#2E7D32',
    fontWeight: '500',
  },
  // Coming Soon Modal Styles
  comingSoonModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  comingSoonModalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    width: '80%',
    maxWidth: 350,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  comingSoonIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  comingSoonTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 12,
    textAlign: 'center',
  },
  comingSoonText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  comingSoonButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  comingSoonButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 