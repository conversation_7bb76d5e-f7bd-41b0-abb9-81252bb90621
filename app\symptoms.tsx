import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Header from '../components/Header';

interface SymptomCategory {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
}

const symptomCategories: SymptomCategory[] = [
  {
    id: 'leaves',
    title: 'Leaf Problems',
    description: 'Issues with leaf color, shape, spots, or damage',
    icon: 'leaf',
    color: '#4CAF50'
  },
  {
    id: 'growth',
    title: 'Growth Issues',
    description: 'Problems with plant growth, size, or development',
    icon: 'trending-up',
    color: '#2196F3'
  },
  {
    id: 'stem',
    title: 'Stem Problems',
    description: 'Issues with stem health, strength, or appearance',
    icon: 'git-branch',
    color: '#9C27B0'
  },
  {
    id: 'roots',
    title: 'Root Issues',
    description: 'Problems with root health, growth, or damage',
    icon: 'water',
    color: '#FF9800'
  },
  {
    id: 'flowers',
    title: 'Flower Problems',
    description: 'Issues with flowering, buds, or fruit development',
    icon: 'flower',
    color: '#E91E63'
  }
];

export default function SymptomsScreen() {
  const router = useRouter();

  const handleCategoryPress = (categoryId: string) => {
    router.push(`/symptoms/${categoryId}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <Header title="Plant Symptoms" />
      <ScrollView style={styles.content}>
        <Text style={styles.introText}>
          Select a category to learn more about specific plant symptoms and their solutions.
        </Text>
        <View style={styles.categoriesContainer}>
          {symptomCategories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[styles.categoryCard, { borderLeftColor: category.color }]}
              onPress={() => handleCategoryPress(category.id)}
            >
              <View style={[styles.iconContainer, { backgroundColor: category.color }]}>
                <Ionicons name={category.icon as any} size={24} color="#fff" />
              </View>
              <View style={styles.categoryContent}>
                <Text style={styles.categoryTitle}>{category.title}</Text>
                <Text style={styles.categoryDescription}>{category.description}</Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color="#666" />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  introText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    lineHeight: 24,
  },
  categoriesContainer: {
    gap: 16,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  categoryContent: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
}); 