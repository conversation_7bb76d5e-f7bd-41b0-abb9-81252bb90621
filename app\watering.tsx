import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Modal, Alert, Image, TextInput } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import Header from '../components/Header';
import Footer from '../components/Footer';

interface WateringSchedule {
  id: string;
  plantName: string;
  frequency: string;
  amount: string;
  days: string[];
  description: string;
}

const STORAGE_KEY = '@watering_schedules';

const sampleSchedules: WateringSchedule[] = [
  {
    id: '1',
    plantName: 'Seedling Stage',
    frequency: 'Daily',
    amount: '100ml',
    days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    description: 'Weeks 1-2: Keep soil moist but not waterlogged'
  },
  {
    id: '2',
    plantName: 'Vegetative Stage',
    frequency: 'Every 2 days',
    amount: '200ml',
    days: ['Monday', 'Wednesday', 'Friday', 'Sunday'],
    description: 'Weeks 2-8: Regular watering schedule'
  },
  {
    id: '3',
    plantName: 'Flowering Stage',
    frequency: 'Every 3 days',
    amount: '300ml',
    days: ['Monday', 'Thursday', 'Sunday'],
    description: 'Weeks 8-16: Reduced watering frequency'
  },
  {
    id: '4',
    plantName: 'Late Flowering',
    frequency: 'Every 4 days',
    amount: '200ml',
    days: ['Monday', 'Friday'],
    description: 'Final 1-2 weeks: Minimal watering'
  }
];

export default function WateringScreen() {
  const [schedules, setSchedules] = useState<WateringSchedule[]>([]);
  const [selectedSchedule, setSelectedSchedule] = useState<WateringSchedule | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [plants, setPlants] = useState([]);
  const [newSchedule, setNewSchedule] = useState<WateringSchedule>({
    id: '',
    plantName: '',
    frequency: '',
    amount: '',
    days: [],
    description: '',
  });
  const [frequencyDays, setFrequencyDays] = useState('1');
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const router = useRouter();

  useEffect(() => {
    loadSchedules();
    loadPlants();
  }, []);

  useEffect(() => {
    saveSchedules();
  }, [schedules]);

  const loadSchedules = async () => {
    try {
      // Clear existing schedules
      await AsyncStorage.removeItem(STORAGE_KEY);
      console.log('Cleared existing schedules');
      
      // Set new default schedules
      console.log('Setting new default schedules');
      setSchedules(sampleSchedules);
      await saveSchedules();
    } catch (error) {
      console.error('Error loading watering schedules:', error);
      console.log('Setting sample schedules due to error');
      setSchedules(sampleSchedules);
    }
  };

  const saveSchedules = async () => {
    try {
      console.log('Saving schedules:', schedules);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(schedules));
    } catch (error) {
      console.error('Error saving watering schedules:', error);
    }
  };

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem('@plants_data');
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const handleSchedulePress = (schedule: WateringSchedule) => {
    setSelectedSchedule(schedule);
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSelectedSchedule(null);
  };

  const handleDeleteSchedule = (scheduleId: string) => {
    const isDefaultSchedule = sampleSchedules.some(schedule => schedule.id === scheduleId);
    if (isDefaultSchedule) {
      Alert.alert('Error', 'Default schedules cannot be deleted.');
      return;
    }
    setSchedules(prevSchedules => prevSchedules.filter(schedule => schedule.id !== scheduleId));
    closeModal();
  };

  const handleEditSchedule = (schedule: WateringSchedule) => {
    // Extract the number from the frequency string (e.g., "Every 2 days" -> "2")
    const frequencyMatch = schedule.frequency.match(/Every (\d+) day/);
    const daysValue = frequencyMatch ? frequencyMatch[1] : '1';
    
    setNewSchedule({
      ...schedule,
      plantName: schedule.plantName,
      description: schedule.description,
      amount: schedule.amount,
    });
    setFrequencyDays(daysValue);
    setSelectedDays(schedule.days);
    setIsEditMode(true);
    setIsAddModalVisible(true);
    closeModal();
  };

  const handleAddSchedule = () => {
    setIsAddModalVisible(true);
  };

  const closeAddModal = () => {
    setIsAddModalVisible(false);
    setIsEditMode(false);
    // Reset the form
    setNewSchedule({
      id: '',
      plantName: '',
      frequency: '',
      amount: '',
      days: [],
      description: '',
    });
    setFrequencyDays('1');
    setSelectedDays([]);
  };

  const toggleDay = (day: string) => {
    setSelectedDays(prev => {
      if (prev.includes(day)) {
        return prev.filter(d => d !== day);
      } else {
        return [...prev, day];
      }
    });
  };

  const saveNewSchedule = (newSchedule: WateringSchedule) => {
    if (isEditMode) {
      // Update existing schedule
      const updatedSchedule = {
        ...newSchedule,
        frequency: `Every ${frequencyDays} ${parseInt(frequencyDays) === 1 ? 'day' : 'days'}`,
        days: selectedDays
      };
      
      setSchedules(prevSchedules => 
        prevSchedules.map(schedule => 
          schedule.id === newSchedule.id ? updatedSchedule : schedule
        )
      );
      
      setIsEditMode(false);
    } else {
      // Create new schedule
      const scheduleWithId = {
        ...newSchedule,
        id: Date.now().toString(),
        frequency: `Every ${frequencyDays} ${parseInt(frequencyDays) === 1 ? 'day' : 'days'}`,
        days: selectedDays
      };
      setSchedules(prevSchedules => [...prevSchedules, scheduleWithId]);
    }
    
    setIsAddModalVisible(false);
    // Reset the form
    setNewSchedule({
      id: '',
      plantName: '',
      frequency: '',
      amount: '',
      days: [],
      description: '',
    });
    setFrequencyDays('1');
    setSelectedDays([]);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Watering Schedules</Text>
            <Text style={styles.heroDescription}>Manage your plant watering schedules</Text>
          </View>
        </View>
        
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            <View style={styles.card}>
              <View style={styles.cardHeader}>
                {/* Removed the title, description, and button */}
              </View>
              <TouchableOpacity style={styles.addCustomButton} onPress={handleAddSchedule}>
                <Ionicons name="add-circle-outline" size={18} color="#fff" style={styles.addButtonIcon} />
                <Text style={styles.addCustomButtonText}>Add Schedule</Text>
              </TouchableOpacity>
              {schedules.length === 0 ? (
                <View style={styles.emptyState}>
                  <Ionicons name="water-outline" size={48} color="#4CAF50" />
                  <Text style={styles.emptyStateText}>No watering schedules yet</Text>
                  <Text style={styles.emptyStateDescription}>
                    Add your first watering schedule to get started
                  </Text>
                  <TouchableOpacity 
                    style={styles.emptyStateButton}
                    onPress={handleAddSchedule}
                  >
                    <Text style={styles.emptyStateButtonText}>Add Schedule</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.scheduleList}>
                  {schedules.map((schedule) => (
                    <TouchableOpacity 
                      key={schedule.id} 
                      style={styles.scheduleItem}
                      onPress={() => handleSchedulePress(schedule)}
                    >
                      <View style={styles.scheduleIcon}>
                        <Ionicons 
                          name="water" 
                          size={24} 
                          color="#4CAF50" 
                        />
                      </View>
                      <View style={styles.scheduleInfo}>
                        <Text style={styles.scheduleName}>{schedule.plantName}</Text>
                        <Text style={styles.scheduleDescription}>{schedule.description}</Text>
                        <View style={styles.scheduleDetails}>
                          <Text style={styles.scheduleDetail}>
                            <Ionicons name="time-outline" size={16} color="#666" /> {schedule.frequency}
                          </Text>
                          <Text style={styles.scheduleDetail}>
                            <Ionicons name="flask-outline" size={16} color="#666" /> {schedule.amount}
                          </Text>
                        </View>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="home" plantCount={plants.length} />

        <Modal
          animationType="slide"
          transparent={true}
          visible={isModalVisible}
          onRequestClose={closeModal}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{selectedSchedule?.plantName}</Text>
                <View style={styles.modalActions}>
                  {selectedSchedule && !sampleSchedules.some(s => s.id === selectedSchedule.id) && (
                    <>
                      <TouchableOpacity 
                        style={styles.modalActionButton} 
                        onPress={() => handleEditSchedule(selectedSchedule)}
                      >
                        <Ionicons name="create-outline" size={24} color="#4CAF50" />
                      </TouchableOpacity>
                      <TouchableOpacity 
                        style={styles.modalActionButton} 
                        onPress={() => {
                          Alert.alert(
                            'Delete Schedule',
                            'Are you sure you want to delete this schedule?',
                            [
                              { text: 'Cancel', style: 'cancel' },
                              { 
                                text: 'Delete', 
                                style: 'destructive',
                                onPress: () => handleDeleteSchedule(selectedSchedule.id)
                              }
                            ]
                          );
                        }}
                      >
                        <Ionicons name="trash-outline" size={24} color="#F44336" />
                      </TouchableOpacity>
                    </>
                  )}
                  <TouchableOpacity onPress={closeModal}>
                    <Ionicons name="close" size={24} color="#4CAF50" />
                  </TouchableOpacity>
                </View>
              </View>
              <ScrollView style={styles.modalBody}>
                <View style={styles.detailRow}>
                  <Ionicons name="water-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Schedule</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.description}</Text>
                  </View>
                </View>
                <View style={styles.detailRow}>
                  <Ionicons name="time-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Frequency</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.frequency}</Text>
                  </View>
                </View>
                <View style={styles.detailRow}>
                  <Ionicons name="flask-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Amount</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.amount}</Text>
                  </View>
                </View>
                <View style={styles.detailRow}>
                  <Ionicons name="calendar-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Days</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.days.join(', ')}</Text>
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        </Modal>

        <Modal
          animationType="slide"
          transparent={true}
          visible={isAddModalVisible}
          onRequestClose={closeAddModal}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{isEditMode ? 'Edit Schedule' : 'Add Schedule'}</Text>
                <TouchableOpacity onPress={closeAddModal}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.modalBody}>
                <View style={styles.formGroup}>
                  <Text style={styles.inputLabel}>Schedule Name</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter schedule name"
                    value={newSchedule.plantName}
                    onChangeText={(text) => setNewSchedule(prev => ({ ...prev, plantName: text }))}
                  />
                </View>
                <View style={styles.formGroup}>
                  <Text style={styles.inputLabel}>Description</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter description"
                    value={newSchedule.description}
                    onChangeText={(text) => setNewSchedule(prev => ({ ...prev, description: text }))}
                  />
                </View>
                <View style={styles.formGroup}>
                  <Text style={styles.inputLabel}>Frequency</Text>
                  <View style={styles.frequencyInputContainer}>
                    <Text style={styles.frequencyText}>Every</Text>
                    <TextInput
                      style={styles.frequencyNumberInput}
                      value={frequencyDays}
                      onChangeText={setFrequencyDays}
                      keyboardType="number-pad"
                      maxLength={2}
                    />
                    <Text style={styles.frequencyText}>days</Text>
                  </View>
                </View>
                <View style={styles.formGroup}>
                  <Text style={styles.inputLabel}>Amount</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter amount"
                    value={newSchedule.amount}
                    onChangeText={(text) => setNewSchedule(prev => ({ ...prev, amount: text }))}
                  />
                </View>
                <View style={styles.formGroup}>
                  <Text style={styles.inputLabel}>Days</Text>
                  <View style={styles.daysContainer}>
                    {weekDays.map((day) => (
                      <TouchableOpacity
                        key={day}
                        style={[
                          styles.dayCheckbox,
                          selectedDays.includes(day) && styles.dayCheckboxSelected
                        ]}
                        onPress={() => toggleDay(day)}
                      >
                        <Text style={[
                          styles.dayText,
                          selectedDays.includes(day) && styles.dayTextSelected
                        ]}>
                          {day.substring(0, 3)}
                        </Text>
                        {selectedDays.includes(day) && (
                          <Ionicons name="checkmark" size={16} color="#fff" />
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
                <TouchableOpacity style={styles.saveButton} onPress={() => saveNewSchedule(newSchedule)}>
                  <Text style={styles.saveButtonText}>{isEditMode ? 'Update Schedule' : 'Save Schedule'}</Text>
                </TouchableOpacity>
              </ScrollView>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 120,
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    paddingTop: 0,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  cardDescription: {
    fontSize: 14,
    color: '#4CAF50',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  emptyStateButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scheduleList: {
    gap: 12,
  },
  scheduleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  scheduleIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  scheduleInfo: {
    flex: 1,
  },
  scheduleName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  scheduleDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  scheduleDetails: {
    flexDirection: 'row',
    gap: 12,
  },
  scheduleDetail: {
    fontSize: 12,
    color: '#666',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    padding: 16,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalBody: {
    padding: 16,
    gap: 20,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginTop: 16,
  },
  detailText: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: '#9E9E9E',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#2E7D32',
    fontWeight: '500',
  },
  addCustomButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
    marginTop: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-end',
  },
  addButtonIcon: {
    marginRight: 6,
  },
  addCustomButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  input: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignSelf: 'flex-end',
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  formGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    color: '#2E7D32',
    marginBottom: 4,
  },
  inputText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
  frequencyInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
  },
  frequencyText: {
    fontSize: 16,
    color: '#333',
    marginHorizontal: 8,
  },
  frequencyNumberInput: {
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    width: 50,
    textAlign: 'center',
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  dayCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: '#fff',
    minWidth: 80,
  },
  dayCheckboxSelected: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  dayText: {
    fontSize: 14,
    color: '#333',
    marginRight: 4,
  },
  dayTextSelected: {
    color: '#fff',
  },
  modalActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalActionButton: {
    marginRight: 16,
  },
}); 