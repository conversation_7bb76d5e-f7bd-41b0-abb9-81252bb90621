import React, { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity, Modal, ImageSourcePropType, Alert, Platform, Linking, TextInput, ActivityIndicator, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import { useLocalSearchParams, useRouter } from 'expo-router';
import * as MediaLibrary from 'expo-media-library';
import * as ImagePicker from 'expo-image-picker';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { GEMINI_API_KEY } from '@env';
import DateTimePicker from '@react-native-community/datetimepicker';

interface HistoryItem {
  id: string;
  type: 'watering' | 'feeding' | 'photo' | 'stage' | 'log';
  date: string;
  description: string;
  photoUrl?: string;
  stage?: string;
}

interface Plant {
  id: number;
  name: string;
  dateAdded: string;
  lastWatered: string;
  light: string;
  history: HistoryItem[];
  environment: string;
  stage: 'Seedling Stage' | 'Vegetative Stage' | 'Flowering Stage' | 'Late Flowering' | 'Drying' | 'Curing';
  lastFeeding: string;
  stageStartDate: string;
}

interface Environment {
  id: string;
  name: string;
  type: 'indoor' | 'outdoor';
  description: string;
  temperature: {
    min: number;
    max: number;
    unit: 'C' | 'F';
  };
  humidity: {
    min: number;
    max: number;
    unit: '%';
  };
  lightIntensity: {
    level: 'low' | 'medium' | 'high' | 'very_high';
    description: string;
  };
  notes: string;
  location: string;
  plants?: any[];
}

interface Notification {
  id: string;
  type: 'watering' | 'feeding' | 'schedule' | 'pruning' | 'check' | 'custom';
  title: string;
  time: string;
  description?: string;
  plantName: string;
  frequency?: string;
  isRecurring?: boolean;
}

const STORAGE_KEY = '@plants_data';
const ENVIRONMENTS_KEY = '@environments_simple';
const NOTIFICATIONS_STORAGE_KEY = '@notifications_data';

const FREQUENCY_OPTIONS = [
  { label: 'Every day', value: '1 day' },
  { label: 'Every 2 days', value: '2 days' },
  { label: 'Every 3 days', value: '3 days' },
  { label: 'Every 4 days', value: '4 days' },
  { label: 'Every 5 days', value: '5 days' },
  { label: 'Every 6 days', value: '6 days' },
  { label: 'Every week', value: '1 week' },
  { label: 'Every 2 weeks', value: '2 weeks' },
  { label: 'Every 3 weeks', value: '3 weeks' },
  { label: 'Every month', value: '1 month' },
  { label: 'Every 2 months', value: '2 months' },
  { label: 'Every 3 months', value: '3 months' },
  { label: 'Every 4 months', value: '4 months' },
  { label: 'Every 5 months', value: '5 months' },
  { label: 'Every 6 months', value: '6 months' }
];

// Helper function to format time for notifications
const formatTime = (isoString: string, frequency?: string) => {
  // Handle the case when the plant hasn't been watered/fed yet
  if (isoString === 'Not watered yet') {
    return 'I\'m Thirsty!';
  }
  if (isoString === 'Not fed yet') {
    return 'Feed me Seymore!';
  }
  
  const date = new Date(isoString);
  const now = new Date();
  
  // Set both dates to midnight to get accurate day difference
  const dateMidnight = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const nowMidnight = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const diffInDays = Math.round((dateMidnight.getTime() - nowMidnight.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    return 'Today';
  } else if (diffInDays === 1) {
    return 'Tomorrow';
  } else if (diffInDays === -1) {
    return 'Yesterday';
  } else if (diffInDays > 1) {
    return `${date.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })} (in ${diffInDays} days)`;
  } else {
    return `${date.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })} (${Math.abs(diffInDays)} days ago)`;
  }
};

// Helper function to calculate age
const calculateAge = (dateAdded: string): string => {
  const addedDate = new Date(dateAdded);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - addedDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 30) {
    return `${diffDays} days`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months !== 1 ? 's' : ''}`;
  } else {
    const years = Math.floor(diffDays / 365);
    return `${years} year${years !== 1 ? 's' : ''}`;
  }
};

// Helper function to generate unique ID for history items
const generateHistoryId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// Helper function to format date for history items
const formatHistoryDate = (date: Date): string => {
  const now = new Date();
  const diffTime = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffTime / (1000 * 60));

  if (diffMinutes < 1) {
    return `Just now`;
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays === 1) {
    return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else if (diffDays < 7) {
    return `${diffDays} days ago, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else {
    return date.toLocaleDateString() + ', ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
};

// Helper function to group history items by date
const groupHistoryByDate = (history: HistoryItem[]) => {
  if (!history || history.length === 0) return [];
  
  // Sort history by date (most recent first)
  const sortedHistory = [...history].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
  
  // Group by date
  const groupedHistory: { date: string; items: HistoryItem[] }[] = [];
  
  sortedHistory.forEach(item => {
    const itemDate = new Date(item.date);
    const dateString = itemDate.toLocaleDateString();
    
    // Check if we already have a group for this date
    const existingGroup = groupedHistory.find(group => group.date === dateString);
    
    if (existingGroup) {
      existingGroup.items.push(item);
    } else {
      groupedHistory.push({
        date: dateString,
        items: [item]
      });
    }
  });
  
  return groupedHistory;
};

export default function PlantDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [plant, setPlant] = useState<Plant | null>(null);
  const [allPlants, setAllPlants] = useState<Plant[]>([]);
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [plantCounts, setPlantCounts] = useState<Record<string, number>>({});
  const [selectedPhoto, setSelectedPhoto] = useState<ImageSourcePropType | null>(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showClearHistoryModal, setShowClearHistoryModal] = useState(false);
  const [showDeletePlantModal, setShowDeletePlantModal] = useState(false);
  const [showEnvironmentModal, setShowEnvironmentModal] = useState(false);
  const [showStageModal, setShowStageModal] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [successMessage, setSuccessMessage] = useState({ title: '', message: '', emoji: '' });
  const [showPhotoOptionsModal, setShowPhotoOptionsModal] = useState(false);
  const [showPhotoViewModal, setShowPhotoViewModal] = useState(false);
  const [selectedHistoryPhoto, setSelectedHistoryPhoto] = useState<string | null>(null);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [showLogModal, setShowLogModal] = useState(false);
  const [logNotes, setLogNotes] = useState('');
  const [showPlantInfoModal, setShowPlantInfoModal] = useState(false);
  const [plantInfo, setPlantInfo] = useState<{
    imageUrl: string;
    description: string;
  } | null>(null);
  const [isLoadingPlantInfo, setIsLoadingPlantInfo] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showTaskTypeModal, setShowTaskTypeModal] = useState(false);
  const [showTaskScheduleModal, setShowTaskScheduleModal] = useState(false);
  const [selectedTaskType, setSelectedTaskType] = useState<{
    type: string;
    title: string;
    description: string;
    emoji: string;
  } | null>(null);
  const [selectedTime, setSelectedTime] = useState(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(7, 0, 0, 0);
    return tomorrow;
  });
  const [isRecurring, setIsRecurring] = useState(true);
  const [frequency, setFrequency] = useState('Daily');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showFrequencyPicker, setShowFrequencyPicker] = useState(false);
  const descriptionInputRef = useRef<TextInput>(null);
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedName, setEditedName] = useState('');
  const [selectedTask, setSelectedTask] = useState<Notification | null>(null);
  const [showTaskDetailsModal, setShowTaskDetailsModal] = useState(false);

  const plantStages = [
    {
      id: '1',
      name: 'Seedling Stage',
      description: 'Weeks 1-2: Initial growth phase',
      emoji: '🌱'
    },
    {
      id: '2',
      name: 'Vegetative Stage',
      description: 'Weeks 2-8: Regular feeding for growth',
      emoji: '🌿'
    },
    {
      id: '3',
      name: 'Flowering Stage',
      description: 'Weeks 8-16: Balanced feeding for flowering',
      emoji: '🌸'
    },
    {
      id: '4',
      name: 'Late Flowering',
      description: 'Final 1-2 weeks: Reduced feeding for flush',
      emoji: '🌺'
    },
    {
      id: '5',
      name: 'Drying',
      description: '1-2 weeks: Proper drying for optimal quality',
      emoji: '🌞'
    },
    {
      id: '6',
      name: 'Curing',
      description: '2-4 weeks: Curing for enhanced flavor and potency',
      emoji: '🏺'
    }
  ];

  // Initialize Gemini API
  const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

  useEffect(() => {
    loadPlant();
    loadEnvironments();
    loadNotifications();
    (async () => {
      try {
        // Request both camera and media library permissions
        const [mediaStatus, cameraStatus] = await Promise.all([
          MediaLibrary.requestPermissionsAsync(),
          ImagePicker.requestCameraPermissionsAsync()
        ]);

        if (mediaStatus.status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please grant permission to access your photos in your device settings.',
            [
              {
                text: 'Open Settings',
                onPress: () => {
                  if (Platform.OS === 'ios') {
                    Linking.openURL('app-settings:');
                  } else {
                    Linking.openSettings();
                  }
                }
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
          return;
        }

        if (cameraStatus.status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please grant permission to access your camera in your device settings.',
            [
              {
                text: 'Open Settings',
                onPress: () => {
                  if (Platform.OS === 'ios') {
                    Linking.openURL('app-settings:');
                  } else {
                    Linking.openSettings();
                  }
                }
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
          return;
        }

        setHasPermission(true);
      } catch (error) {
        console.error('Error requesting permissions:', error);
        Alert.alert('Error', 'Failed to request permissions. Please try again.');
      }
    })();
  }, [id]);

  const loadEnvironments = async () => {
    try {
      const storedEnvironments = await AsyncStorage.getItem(ENVIRONMENTS_KEY);
      if (storedEnvironments) {
        const parsedEnvironments = JSON.parse(storedEnvironments);
        // Only set environments that are available (have a name property)
        const availableEnvironments = parsedEnvironments.filter((env: any) => env && env.name);
        setEnvironments(availableEnvironments);
        
        // Count plants in each environment
        const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedPlants) {
          const plants = JSON.parse(storedPlants);
          const counts: Record<string, number> = {};
          
          // Initialize counts for all environments
          availableEnvironments.forEach((env: Environment) => {
            counts[env.name] = 0;
          });
          
          // Count plants in each environment
          plants.forEach((p: Plant) => {
            if (p.environment && counts[p.environment] !== undefined) {
              counts[p.environment]++;
            }
          });
          
          setPlantCounts(counts);
        }
      } else {
        // If no environments are stored, set an empty array
        setEnvironments([]);
        setPlantCounts({});
      }
    } catch (error) {
      console.error('Error loading environments:', error);
      setEnvironments([]);
      setPlantCounts({});
    }
  };

  const loadPlant = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        const plants = JSON.parse(storedPlants);
        setAllPlants(plants);
        // Convert id to number for comparison
        const plantId = typeof id === 'string' ? parseInt(id, 10) : id;
        const foundPlant = plants.find((p: Plant) => p.id === plantId);
        if (foundPlant) {
          console.log('Found plant:', foundPlant);
          setPlant(foundPlant);
        } else {
          console.log('Plant not found for id:', plantId);
          Alert.alert(
            'Plant Not Found',
            'The plant you are looking for could not be found.',
            [
              {
                text: 'Go Back',
                onPress: () => router.back()
              }
            ]
          );
        }
      } else {
        console.log('No plants stored');
        setAllPlants([]);
        Alert.alert(
          'No Plants',
          'No plants have been added yet.',
          [
            {
              text: 'Go Back',
              onPress: () => router.back()
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error loading plant:', error);
      setAllPlants([]);
      Alert.alert(
        'Error',
        'Failed to load plant information. Please try again.',
        [
          {
            text: 'Go Back',
            onPress: () => router.back()
          }
        ]
      );
    }
  };

  const loadNotifications = async () => {
    try {
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      if (storedData) {
        const data = JSON.parse(storedData);
        if (Array.isArray(data)) {
          // Filter notifications for this specific plant and ensure they have the isRecurring property
          const plantNotifications = data
            .filter((notification: Notification) => notification.plantName === plant?.name)
            .map((notification: Notification) => ({
              ...notification,
              isRecurring: notification.isRecurring !== undefined ? notification.isRecurring : true
            }));
          setNotifications(plantNotifications);
        }
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  // Update notifications when plant changes
  useEffect(() => {
    if (plant) {
      loadNotifications();
    }
  }, [plant]);

  const addHistoryItem = async (item: Omit<HistoryItem, 'id'>) => {
    if (!plant) return;

    try {
      const newHistoryItem: HistoryItem = {
        ...item,
        id: generateHistoryId(),
      };

      const updatedPlant = {
        ...plant,
        history: [newHistoryItem, ...(plant.history || [])],
      };

      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        const plants = JSON.parse(storedPlants);
        const updatedPlants = plants.map((p: Plant) => 
          p.id === plant.id ? updatedPlant : p
        );
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPlants));
        setPlant(updatedPlant);
      }
    } catch (error) {
      console.error('Error adding history item:', error);
    }
  };

  const updatePlant = async (updatedPlant: Plant) => {
    if (!plant) return;

    try {
      console.log('Updating plant with:', updatedPlant);
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        const plants = JSON.parse(storedPlants);
        const updatedPlants = plants.map((p: Plant) => 
          p.id === plant.id ? updatedPlant : p
        );
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPlants));
        console.log('Plant updated in storage');
        setPlant(updatedPlant);
        console.log('Plant state updated');
      }
    } catch (error) {
      console.error('Error updating plant:', error);
    }
  };

  const handleFeedPlant = async () => {
    if (!plant) return;

    const now = new Date();
    const updatedPlant = {
      ...plant,
      lastFeeding: now.toISOString(),
      history: [
        {
          id: generateHistoryId(),
          type: 'feeding' as const,
          date: now.toISOString(),
          description: 'Plant fed'
        },
        ...plant.history
      ]
    };

    await updatePlant(updatedPlant);
    setSuccessMessage({
      title: 'Success!',
      message: 'Plant fed successfully',
      emoji: '🌿'
    });
    setShowSuccessAlert(true);
    setTimeout(() => {
      setShowSuccessAlert(false);
    }, 3000);
  };

  const handleWaterPlant = async () => {
    if (!plant) return;

    const now = new Date();
    const updatedPlant = {
      ...plant,
      lastWatered: now.toISOString(),
      history: [
        {
          id: generateHistoryId(),
          type: 'watering' as const,
          date: now.toISOString(),
          description: 'Plant watered'
        },
        ...plant.history
      ]
    };

    await updatePlant(updatedPlant);
    setSuccessMessage({
      title: 'Success!',
      message: 'Plant watered successfully',
      emoji: '💧'
    });
    setShowSuccessAlert(true);
    setTimeout(() => {
      setShowSuccessAlert(false);
    }, 3000);
  };

  const clearHistory = async () => {
    if (!plant) return;

    try {
      const updatedPlant = {
        ...plant,
        history: [],
      };
      await updatePlant(updatedPlant);
    } catch (error) {
      console.error('Error clearing history:', error);
    }
  };

  const deletePlant = async () => {
    if (!plant) return;

    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        const plants = JSON.parse(storedPlants);
        const updatedPlants = plants.filter((p: Plant) => p.id !== plant.id);
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPlants));
        
        // Remove notifications associated with this plant
        const storedNotifications = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
        if (storedNotifications) {
          const notifications = JSON.parse(storedNotifications);
          const updatedNotifications = notifications.filter(
            (notification: Notification) => notification.plantName !== plant.name
          );
          await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(updatedNotifications));
        }
        
        // Navigate back to plants screen
        router.push('/plants');
      }
    } catch (error) {
      console.error('Error deleting plant:', error);
      Alert.alert('Error', 'Failed to delete plant. Please try again.');
    }
  };

  const handleEnvironmentChange = async (newEnvironment: string) => {
    if (!plant) return;

    const now = new Date();
    const updatedPlant = {
      ...plant,
      environment: newEnvironment,
      history: [
        {
          id: generateHistoryId(),
          type: 'log' as const,
          date: now.toISOString(),
          description: `Environment changed to ${newEnvironment}`
        },
        ...plant.history
      ]
    };

    await updatePlant(updatedPlant);
    
    // Update plant counts
    const newCounts = { ...plantCounts };
    if (plant.environment) {
      newCounts[plant.environment] = Math.max(0, (newCounts[plant.environment] || 0) - 1);
    }
    newCounts[newEnvironment] = (newCounts[newEnvironment] || 0) + 1;
    setPlantCounts(newCounts);
    
    setShowEnvironmentModal(false);
  };

  const handleStageChange = async (newStage: 'Seedling Stage' | 'Vegetative Stage' | 'Flowering Stage' | 'Late Flowering' | 'Drying' | 'Curing') => {
    if (!plant) return;

    const now = new Date();
    const updatedPlant = {
      ...plant,
      stage: newStage,
      stageStartDate: now.toISOString(),
      history: [
        {
          id: generateHistoryId(),
          type: 'stage' as const,
          date: now.toISOString(),
          description: `Stage: ${newStage}`,
          stage: newStage
        },
        ...plant.history
      ]
    };

    await updatePlant(updatedPlant);
    setShowStageModal(false);
  };

  const handlePickImage = async () => {
    try {
      // Skip permissions check on web
      if (Platform.OS !== 'web') {
        // Check permissions again before accessing
        const { status } = await MediaLibrary.getPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please grant permission to access your photos in your device settings.',
            [
              {
                text: 'Open Settings',
                onPress: () => {
                  if (Platform.OS === 'ios') {
                    Linking.openURL('app-settings:');
                  } else {
                    Linking.openSettings();
                  }
                }
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
          return;
        }
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0].uri) {
        // Save to media library only on mobile platforms
        if (Platform.OS !== 'web') {
          await MediaLibrary.saveToLibraryAsync(result.assets[0].uri);
        }
        
        // Add to plant history
        if (plant) {
          const now = new Date();
          const newHistoryItem: HistoryItem = {
            id: generateHistoryId(),
            type: 'photo',
            date: now.toISOString(),
            description: 'New photo added',
            photoUrl: result.assets[0].uri
          };

          const updatedPlant = {
            ...plant,
            history: [newHistoryItem, ...plant.history]
          };

          await updatePlant(updatedPlant);
          setSuccessMessage({
            title: 'Success!',
            message: 'Photo added successfully',
            emoji: '📸'
          });
          setShowSuccessAlert(true);
          setTimeout(() => {
            setShowSuccessAlert(false);
          }, 3000);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to access your photos. Please check your permissions and try again.');
    }
  };

  const handlePhotoButtonPress = () => {
    if (Platform.OS === 'web') {
      // On web, directly open the image picker
      handlePickImage();
    } else {
      // On mobile, directly launch the camera
      handleTakePhoto();
    }
  };

  const handleTakePhoto = async () => {
    setShowPhotoOptionsModal(false);
    
    // Add a small delay before launching the camera to prevent freezing on iOS
    setTimeout(async () => {
      try {
        const result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: false,
          allowsMultipleSelection: false,
          aspect: [4, 3],
          quality: 1,
        });

        if (!result.canceled && result.assets[0].uri) {
          await MediaLibrary.saveToLibraryAsync(result.assets[0].uri);
          
          if (plant) {
            const now = new Date();
            const newHistoryItem: HistoryItem = {
              id: generateHistoryId(),
              type: 'photo',
              date: now.toISOString(),
              description: 'New photo added',
              photoUrl: result.assets[0].uri
            };

            const updatedPlant = {
              ...plant,
              history: [newHistoryItem, ...plant.history]
            };

            await updatePlant(updatedPlant);
            setSuccessMessage({
              title: 'Success!',
              message: 'Photo added successfully',
              emoji: '📸'
            });
            setShowSuccessAlert(true);
            setTimeout(() => {
              setShowSuccessAlert(false);
            }, 3000);
          }
        }
      } catch (error) {
        console.error('Error taking picture:', error);
        Alert.alert('Error', 'Failed to take picture');
      }
    }, 300); // 300ms delay
  };

  const handleAddLog = async () => {
    if (!plant || !logNotes.trim()) return;

    const now = new Date();
    const newHistoryItem: HistoryItem = {
      id: generateHistoryId(),
      type: 'log',
      date: now.toISOString(),
      description: logNotes.trim()
    };

    const updatedPlant = {
      ...plant,
      history: [newHistoryItem, ...plant.history]
    };

    await updatePlant(updatedPlant);
    setLogNotes('');
    setShowLogModal(false);
    
    setSuccessMessage({
      title: 'Success!',
      message: 'Log entry added successfully',
      emoji: '📝'
    });
    setShowSuccessAlert(true);
    setTimeout(() => {
      setShowSuccessAlert(false);
    }, 3000);
  };

  const fetchPlantInfo = async () => {
    if (!plant) return;
    
    setIsLoadingPlantInfo(true);
    try {
      console.log('Starting to fetch plant info for:', plant.name);
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      
      // Create a more specific prompt for the AI
      const prompt = `Please provide detailed information about the plant "${plant.name}". Focus on providing a comprehensive description that includes:
- Physical characteristics
- Care requirements
- Growth habits
- Interesting facts
- Common issues and solutions

Please format your response in a clear, readable way with proper sections and bullet points where appropriate.`;

      console.log('Sending prompt to API');
      const result = await model.generateContent(prompt);
      console.log('Got response from API');
      const response = await result.response;
      const text = response.text();
      console.log('Response text:', text);
      
      // Check if we got a meaningful response
      if (text && text.trim().length > 0) {
        console.log('Setting plant info with description');
        setPlantInfo({
          imageUrl: '',
          description: text.trim()
        });
      } else {
        console.log('No meaningful response received');
        setPlantInfo(null);
      }
    } catch (error) {
      console.error('Error fetching plant info:', error);
      setPlantInfo(null);
      Alert.alert(
        'Error',
        'Failed to fetch plant information. Please check your internet connection and try again.',
        [
          {
            text: 'Try Again',
            onPress: () => fetchPlantInfo()
          },
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
    } finally {
      setIsLoadingPlantInfo(false);
    }
  };

  const formatPlantInfo = (text: string) => {
    // Split the text into sections based on markdown headers
    const sections = text.split(/(?=##|I\.|II\.|III\.|IV\.|V\.)/);
    
    return sections.map((section, index) => {
      // Remove markdown formatting and HTML-like content
      const cleanText = section
        .replace(/[*#]/g, '')
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/css-[^"]*/g, '') // Remove CSS class names
        .replace(/r-[^"]*/g, '') // Remove r- prefixed classes
        .trim();
      
      // Skip empty sections or sections that are just single characters
      if (!cleanText || cleanText.length <= 1) return null;
      
      // Check if this is a header section
      if (section.startsWith('##')) {
        // Split the content into paragraphs
        const paragraphs = cleanText.split('\n\n').filter(p => p.trim());
        
        return (
          <View key={index} style={styles.plantInfoCard}>
            <View style={styles.plantInfoCardHeader}>
              <Text style={styles.plantInfoCardTitle}>{paragraphs[0]}</Text>
            </View>
            {paragraphs.length > 1 && (
              <View style={styles.plantInfoCardContent}>
                {paragraphs.slice(1).map((paragraph, pIndex) => (
                  <Text key={pIndex} style={styles.plantInfoCardText}>
                    {paragraph.trim()}
                  </Text>
                ))}
              </View>
            )}
          </View>
        );
      }
      
      // Check if this is a section header (I., II., etc.)
      if (section.startsWith('I.') || section.startsWith('II.') || 
          section.startsWith('III.') || section.startsWith('IV.') || 
          section.startsWith('V.')) {
        // Extract the title and content
        const title = cleanText.split('\n')[0];
        const content = cleanText.split('\n').slice(1).join('\n').trim();
        
        // Skip if there's no meaningful content
        if (!content) return null;
        
        return (
          <View key={index} style={styles.plantInfoCard}>
            <View style={styles.plantInfoCardHeader}>
              <View style={styles.plantInfoCardIcon}>
                <Text style={styles.plantInfoCardIconText}>
                  {section.startsWith('I.') ? '🌱' :
                   section.startsWith('II.') ? '💧' :
                   section.startsWith('III.') ? '🌿' :
                   section.startsWith('IV.') ? '🌺' :
                   '🌞'}
                </Text>
              </View>
              <Text style={styles.plantInfoCardTitle}>{title}</Text>
            </View>
            <View style={styles.plantInfoCardContent}>
              <Text style={styles.plantInfoCardText}>{content}</Text>
            </View>
          </View>
        );
      }
      
      // Regular content - skip if it's just a single character or empty
      if (cleanText.length <= 1) return null;
      
      return (
        <View key={index} style={styles.plantInfoCard}>
          <View style={styles.plantInfoCardContent}>
            <Text style={styles.plantInfoCardText}>{cleanText}</Text>
          </View>
        </View>
      );
    }).filter(Boolean); // Remove null entries
  };

  const resetTaskForm = () => {
    // Set default date to tomorrow instead of today
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(7, 0, 0, 0);
    setSelectedTime(tomorrow);
    
    setIsRecurring(true);
    setFrequency('Daily');
    setShowDateOptions(false);
    setSelectedDateOption(1);
    setShowFrequencyPicker(false);
  };

  const createTask = (type: string, title: string, description: string, emoji: string) => {
    setSelectedTaskType({ type, title, description, emoji });
    setShowTaskTypeModal(false);
    setShowTaskScheduleModal(true);
    resetTaskForm();
  };

  const isPredefinedTaskType = (type: string) => {
    return ['watering', 'feeding', 'pruning', 'check'].includes(type);
  };

  const getPredefinedTaskInfo = (type: string) => {
    switch (type) {
      case 'watering':
        return {
          title: 'Water Plant',
          description: 'Time to water your plant'
        };
      case 'feeding':
        return {
          title: 'Feed Plant',
          description: 'Time to feed your plant'
        };
      case 'pruning':
        return {
          title: 'Prune Plant',
          description: 'Time to prune your plant'
        };
      case 'check':
        return {
          title: 'Check Plant',
          description: 'Time to check on your plant'
        };
      default:
        return {
          title: '',
          description: ''
        };
    }
  };

  const handleCreateTask = () => {
    if (!selectedTaskType) return;

    // For predefined tasks, use the predefined info
    const taskInfo = isPredefinedTaskType(selectedTaskType.type) 
      ? getPredefinedTaskInfo(selectedTaskType.type)
      : {
          title: selectedTaskType.title,
          description: selectedTaskType.description
        };

    // Create a new task with the selected options
    const newTask: Notification = {
      id: Date.now().toString(),
      title: taskInfo.title,
      description: taskInfo.description,
      time: selectedTime.toISOString(),
      type: selectedTaskType.type as 'watering' | 'feeding' | 'schedule' | 'pruning' | 'check',
      plantName: plant?.name || '',
      frequency: frequency,
      isRecurring: isRecurring
    };
    
    // Save the new task
    AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY)
      .then(storedData => {
        let notifications: Notification[] = [];
        if (storedData) {
          notifications = JSON.parse(storedData);
          
          // Check for duplicate tasks
          const isDuplicate = notifications.some(task => 
            task.plantName === newTask.plantName &&
            task.type === newTask.type &&
            task.title === newTask.title &&
            task.frequency === newTask.frequency &&
            task.isRecurring === newTask.isRecurring &&
            // Compare dates ignoring time
            new Date(task.time).toDateString() === new Date(newTask.time).toDateString()
          );

          if (isDuplicate) {
            throw new Error('A similar task already exists for this date');
          }
        }
        
        notifications.push(newTask);
        return AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(notifications));
      })
      .then(() => {
        loadNotifications(); // Reload notifications to show the new task
        setShowTaskScheduleModal(false);
        setSelectedTaskType(null);
        
        // Reset to tomorrow instead of today
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(7, 0, 0, 0);
        setSelectedTime(tomorrow);
        
        setIsRecurring(true);
        setFrequency('Daily');
        
        // Show success modal with appropriate emoji based on task type
        const emoji = selectedTaskType.type === 'watering' ? '💧' :
                     selectedTaskType.type === 'feeding' ? '🌿' :
                     selectedTaskType.type === 'pruning' ? '✂️' :
                     selectedTaskType.type === 'check' ? '🔍' : '📋';
        
        setSuccessMessage({
          title: 'Success!',
          message: 'Task created successfully',
          emoji: emoji
        });
        setShowSuccessAlert(true);
        setTimeout(() => {
          setShowSuccessAlert(false);
        }, 3000);
      })
      .catch(error => {
        console.error('Error creating task:', error);
        if (error.message === 'A similar task already exists for this date') {
          Alert.alert('Duplicate Task', 'A similar task already exists for this date.');
        } else {
          Alert.alert('Error', 'Failed to create task. Please try again.');
        }
      });
  };

  const handleTaskTitleChange = (title: string) => {
    if (!selectedTaskType) return;
    setSelectedTaskType({
      type: selectedTaskType.type,
      title,
      description: selectedTaskType.description,
      emoji: selectedTaskType.emoji
    });
  };

  const handleTaskDescriptionChange = (description: string) => {
    if (!selectedTaskType) return;
    setSelectedTaskType({
      type: selectedTaskType.type,
      title: selectedTaskType.title,
      description,
      emoji: selectedTaskType.emoji
    });
  };

  const handleTaskTypeChange = (type: string) => {
    if (!selectedTaskType) return;
    setSelectedTaskType({
      ...selectedTaskType,
      type
    });
  };

  const handleTaskEmojiChange = (emoji: string) => {
    if (!selectedTaskType) return;
    setSelectedTaskType({
      ...selectedTaskType,
      emoji
    });
  };

  // Add this function before the return statement in the component
  const getRelativeTimeText = (date: Date): string => {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(now);
    nextWeek.setDate(nextWeek.getDate() + 7);
    const nextMonth = new Date(now);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    if (date.toDateString() === now.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const day = date.getDate();
      const diffInDays = Math.round((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      if (diffInDays > 0) {
        return `${dayOfWeek}, ${month} ${day} (in ${diffInDays} days)`;
      } else {
        return `${dayOfWeek}, ${month} ${day} (${Math.abs(diffInDays)} days ago)`;
      }
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      // Set time to 7 AM
      newDate.setHours(7, 0, 0, 0);
      setSelectedTime(newDate);
    }
  };

  const renderDatePicker = () => {
    if (Platform.OS === 'ios') {
      return (
        <Modal
          visible={showDatePicker}
          transparent={true}
          animationType="slide"
        >
          <View style={styles.modalOverlay}>
            <View style={styles.datePickerContainer}>
              <View style={styles.datePickerHeader}>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Text style={styles.datePickerButton}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Text style={styles.datePickerButton}>Done</Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={selectedTime}
                mode="date"
                display="spinner"
                onChange={handleDateChange}
              />
            </View>
          </View>
        </Modal>
      );
    } else if (Platform.OS === 'android') {
      return showDatePicker ? (
        <DateTimePicker
          value={selectedTime}
          mode="date"
          display="default"
          onChange={handleDateChange}
        />
      ) : null;
    }
    return null;
  };

  const DATE_OPTIONS = [
    { label: 'Tomorrow', value: 1 },
    { label: 'In 2 days', value: 2 },
    { label: 'In 3 days', value: 3 },
    { label: 'In 4 days', value: 4 },
    { label: 'In 5 days', value: 5 },
    { label: 'In 6 days', value: 6 },
    { label: 'In 1 week', value: 7 },
    { label: 'In 2 weeks', value: 14 },
    { label: 'In 3 weeks', value: 21 },
    { label: 'In 1 month', value: 30 },
  ];

  const [showDateOptions, setShowDateOptions] = useState(false);
  const [selectedDateOption, setSelectedDateOption] = useState(1); // Changed default to 1 (Tomorrow)

  const handleDateOptionSelect = (days: number) => {
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + days);
    // Set time to 7 AM
    newDate.setHours(7, 0, 0, 0);
    setSelectedTime(newDate);
    setSelectedDateOption(days);
    setShowDateOptions(false);
  };

  const getDateOptionLabel = (days: number) => {
    return DATE_OPTIONS.find(option => option.value === days)?.label || 'Today';
  };

  // Add this function before the return statement
  const handleTaskPress = (task: Notification) => {
    setSelectedTask(task);
    setShowTaskDetailsModal(true);
  };

  const handleCompleteTask = async () => {
    if (!selectedTask || !plant) return;

    try {
      // Get current notifications
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      let notifications: Notification[] = [];
      if (storedData) {
        notifications = JSON.parse(storedData);
      }

      // Remove the completed task
      const updatedNotifications = notifications.filter(n => n.id !== selectedTask.id);

      // If it's a recurring task, create the next occurrence
      if (selectedTask.isRecurring) {
        const nextDate = new Date();
        const frequency = selectedTask.frequency?.toLowerCase() || 'daily';
        
        // Calculate the next date based on frequency
        switch (frequency) {
          case 'daily':
            nextDate.setDate(nextDate.getDate() + 1);
            break;
          case 'weekly':
            nextDate.setDate(nextDate.getDate() + 7);
            break;
          case 'bi-weekly':
            nextDate.setDate(nextDate.getDate() + 14);
            break;
          case 'monthly':
            nextDate.setMonth(nextDate.getMonth() + 1);
            break;
          default:
            // For custom frequencies like "every 3 days", extract the number
            const match = frequency.match(/every (\d+) days?/i);
            if (match) {
              const days = parseInt(match[1], 10);
              nextDate.setDate(nextDate.getDate() + days);
            } else {
              // Default to daily if frequency format is unknown
              nextDate.setDate(nextDate.getDate() + 1);
            }
        }

        // Check if a similar task already exists for the next date
        const isDuplicate = updatedNotifications.some(task => 
          task.plantName === selectedTask.plantName &&
          task.type === selectedTask.type &&
          task.title === selectedTask.title &&
          task.frequency === selectedTask.frequency &&
          task.isRecurring === selectedTask.isRecurring &&
          // Compare dates ignoring time
          new Date(task.time).toDateString() === nextDate.toDateString()
        );

        // Only create the next task if no duplicate exists
        if (!isDuplicate) {
          const nextTask: Notification = {
            id: Date.now().toString(),
            title: selectedTask.title,
            description: selectedTask.description,
            time: nextDate.toISOString(),
            type: selectedTask.type,
            plantName: selectedTask.plantName,
            frequency: selectedTask.frequency,
            isRecurring: selectedTask.isRecurring
          };
          updatedNotifications.push(nextTask);
        }
      }

      // Save the updated notifications
      await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(updatedNotifications));

      // Update plant history based on task type
      if (selectedTask.type === 'watering') {
        // For watering tasks
        const now = new Date();
        const updatedPlant = {
          ...plant,
          lastWatered: now.toISOString(),
          history: [
            {
              id: generateHistoryId(),
              type: 'watering' as const,
              date: now.toISOString(),
              description: 'Plant watered'
            },
            ...plant.history
          ]
        };

        await updatePlant(updatedPlant);
        setSuccessMessage({
          title: 'Success!',
          message: 'Plant watered successfully',
          emoji: '💧'
        });
        setShowSuccessAlert(true);
        setTimeout(() => {
          setShowSuccessAlert(false);
        }, 3000);
      } else if (selectedTask.type === 'feeding') {
        // For feeding tasks
        const now = new Date();
        const updatedPlant = {
          ...plant,
          lastFeeding: now.toISOString(),
          history: [
            {
              id: generateHistoryId(),
              type: 'feeding' as const,
              date: now.toISOString(),
              description: 'Plant fed'
            },
            ...plant.history
          ]
        };

        await updatePlant(updatedPlant);
        setSuccessMessage({
          title: 'Success!',
          message: 'Plant fed successfully',
          emoji: '🌿'
        });
        setShowSuccessAlert(true);
        setTimeout(() => {
          setShowSuccessAlert(false);
        }, 3000);
      } else if (selectedTask.type === 'pruning') {
        // For pruning tasks
        const now = new Date();
        const updatedPlant = {
          ...plant,
          history: [
            {
              id: generateHistoryId(),
              type: 'log' as const,
              date: now.toISOString(),
              description: 'Plant pruned'
            },
            ...plant.history
          ]
        };

        await updatePlant(updatedPlant);
        setSuccessMessage({
          title: 'Success!',
          message: 'Plant pruned successfully',
          emoji: '✂️'
        });
        setShowSuccessAlert(true);
        setTimeout(() => {
          setShowSuccessAlert(false);
        }, 3000);
      } else {
        // For other tasks, show regular success message
        Alert.alert('Success', 'Task completed successfully!');
      }
      
      // Reload notifications and close modal
      loadNotifications();
      setShowTaskDetailsModal(false);
    } catch (error) {
      console.error('Error completing task:', error);
      Alert.alert('Error', 'Failed to complete task. Please try again.');
    }
  };

  const handleEndTask = async () => {
    if (!selectedTask) return;

    try {
      // Get current notifications
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      let notifications: Notification[] = [];
      if (storedData) {
        notifications = JSON.parse(storedData);
      }

      // Remove the task
      const updatedNotifications = notifications.filter(n => n.id !== selectedTask.id);
      await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(updatedNotifications));
      
      // Reload notifications and close modal
      loadNotifications();
      setShowTaskDetailsModal(false);
      
      // Show success message
      setSuccessMessage({
        title: 'Success!',
        message: 'Task ended successfully',
        emoji: '✅'
      });
      setShowSuccessAlert(true);
      setTimeout(() => {
        setShowSuccessAlert(false);
      }, 3000);
    } catch (error) {
      console.error('Error ending task:', error);
      Alert.alert('Error', 'Failed to end task. Please try again.');
    }
  };

  const handleNameChange = async (newName: string) => {
    if (!plant || newName.trim() === '') return;
    
    try {
      // Create updated plant with new name
      const updatedPlant = { ...plant, name: newName.trim() };
      
      // Update the plant in storage and state
      await updatePlant(updatedPlant);
      
      // Add history entry for name change if name actually changed
      if (newName.trim() !== plant.name) {
        await addHistoryItem({
          type: 'log',
          date: new Date().toISOString(),
          description: `Plant name changed from "${plant.name}" to "${newName.trim()}"`,
        });
        
        // Reload the plant to ensure we have the latest state with updated history
        await loadPlant();
      }
      
      setIsEditingName(false);
      
      // Show success message
      setSuccessMessage({
        title: 'Name Updated',
        message: `Plant name changed to "${newName.trim()}"`,
        emoji: '✏️'
      });
      setShowSuccessAlert(true);
      
      // Auto-hide the success alert after 2 seconds
      setTimeout(() => {
        setShowSuccessAlert(false);
      }, 2000);
    } catch (error) {
      console.error('Error updating plant name:', error);
    }
  };

  if (!plant) {
    return null;
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.hero}>
            <Image 
              source={require('../../assets/images/seedling.jpg')}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <View style={styles.heroContent}>
              {isEditingName ? (
                <View style={styles.nameEditContainer}>
                  <TextInput
                    style={styles.nameInput}
                    value={editedName}
                    onChangeText={setEditedName}
                    autoFocus={true}
                    onSubmitEditing={() => handleNameChange(editedName)}
                    onBlur={() => {
                      if (editedName.trim() !== '') {
                        handleNameChange(editedName);
                      } else {
                        setIsEditingName(false);
                        setEditedName(plant.name);
                      }
                    }}
                    placeholder="Enter plant name"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    returnKeyType="done"
                    returnKeyLabel="Save"
                  />
                </View>
              ) : (
                <TouchableOpacity onPress={() => {
                  setIsEditingName(true);
                  setEditedName(plant.name);
                }}>
                  <Text style={styles.heroTitle}>{plant.name}</Text>
                </TouchableOpacity>
              )}
              <Text style={styles.heroDescription}>{calculateAge(plant.dateAdded)} • Added {new Date(plant.dateAdded).toLocaleDateString()}</Text>
            </View>
          </View>

          <View style={styles.cardContainer}>
            <View style={styles.actionButtonsContainer}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={handleWaterPlant}
              >
                <View style={styles.actionButtonIcon}>
                  <Ionicons name="water" size={24} color="#4CAF50" />
                </View>
                <Text style={styles.actionButtonText}>Water</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.actionButton}
                onPress={handleFeedPlant}
              >
                <View style={styles.actionButtonIcon}>
                  <Text style={styles.actionButtonEmoji}>🌿</Text>
                </View>
                <Text style={styles.actionButtonText}>Feed</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.actionButton}
                onPress={handlePhotoButtonPress}
              >
                <View style={styles.actionButtonIcon}>
                  <Ionicons name="image" size={24} color="#4CAF50" />
                </View>
                <Text style={styles.actionButtonText}>Photo</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => setShowMoreMenu(true)}
              >
                <View style={styles.actionButtonIcon}>
                  <Ionicons name="clipboard-outline" size={24} color="#4CAF50" />
                </View>
                <Text style={styles.actionButtonText}>More</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.settingsCard}>
              <TouchableOpacity 
                style={styles.settingButton}
                onPress={() => setShowEnvironmentModal(true)}
              >
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={styles.iconContainer}>
                      <Ionicons name="home-outline" size={24} color="#4CAF50" />
                    </View>
                    <View>
                      <Text style={styles.settingText}>Environment</Text>
                      <Text style={styles.environmentDescription}>
                        {plant.environment}
                      </Text>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton}
                onPress={() => setShowStageModal(true)}
              >
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={styles.iconContainer}>
                      <Ionicons name="leaf-outline" size={24} color="#4CAF50" />
                    </View>
                    <View>
                      <Text style={styles.settingText}>Plant Stage</Text>
                      <Text style={styles.settingDescription}>
                        {plant.stage} • {calculateAge(plant.stageStartDate || plant.dateAdded)}
                      </Text>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton}
                onPress={handleWaterPlant}
              >
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={styles.iconContainer}>
                      <Ionicons name="water-outline" size={24} color="#4CAF50" />
                    </View>
                    <View>
                      <Text style={styles.settingText}>Last Watered</Text>
                      <Text style={styles.settingDescription}>{formatTime(plant.lastWatered)}</Text>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.settingButton}
                onPress={handleFeedPlant}
              >
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <View style={styles.iconContainer}>
                      <Text style={styles.settingEmoji}>🌿</Text>
                    </View>
                    <View>
                      <Text style={styles.settingText}>Last Feeding</Text>
                      <Text style={styles.settingDescription}>{formatTime(plant.lastFeeding)}</Text>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.historyTitleCard}>
              <View style={styles.historyTitleContainer}>
                <View style={styles.iconContainer}>
                  <Text style={styles.emoji}>📅</Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={styles.historyTitle}>Upcoming Tasks</Text>
                  <Text style={styles.historySubtitle}>Scheduled activities</Text>
                </View>
                <TouchableOpacity 
                  style={styles.addTaskButton}
                  onPress={() => setShowTaskTypeModal(true)}
                >
                  <Ionicons name="add-circle" size={24} color="#2E7D32" />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.settingsCard}>
              {notifications.length > 0 ? (
                notifications.map((notification, index) => (
                  <View key={notification.id}>
                    <TouchableOpacity 
                      style={styles.settingButton}
                      onPress={() => handleTaskPress(notification)}
                    >
                      <View style={styles.settingItem}>
                        <View style={styles.settingInfo}>
                          <View style={styles.iconContainer}>
                            <Text style={styles.settingEmoji}>
                              {notification.type === 'watering' ? '💧' :
                               notification.type === 'feeding' ? '🌿' :
                               notification.type === 'schedule' ? '📅' :
                               notification.type === 'pruning' ? '✂️' :
                               notification.type === 'check' ? '🔍' : '📋'}
                            </Text>
                          </View>
                          <View>
                            <Text style={styles.settingText}>{notification.title}</Text>
                            {notification.type === 'custom' && notification.description && (
                              <Text style={styles.taskDescription}>{notification.description}</Text>
                            )}
                            <Text style={styles.settingDescription}>
                              {(() => {
                                const taskDate = new Date(notification.time);
                                const now = new Date();
                                
                                // Set both dates to midnight to get accurate day difference
                                const taskMidnight = new Date(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate());
                                const nowMidnight = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                                
                                // If task date is today or in the future, it's not overdue
                                if (taskMidnight.getTime() >= nowMidnight.getTime()) {
                                  return `Due ${formatTime(notification.time, notification.frequency)}`;
                                } else {
                                  return `Overdue (${formatTime(notification.time, notification.frequency)})`;
                                }
                              })()}
                            </Text>
                            {notification.isRecurring && (
                              <Text style={[styles.settingDescription, { color: '#2E7D32' }]}>
                                {notification.frequency 
                                  ? `Repeats every ${notification.frequency.toLowerCase()}`
                                  : 'Repeats daily'}
                              </Text>
                            )}
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  </View>
                ))
              ) : (
                <View style={styles.settingButton}>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={styles.iconContainer}>
                        <Text style={styles.settingEmoji}>📅</Text>
                      </View>
                      <View>
                        <Text style={styles.settingText}>No upcoming tasks</Text>
                        <Text style={styles.settingDescription}>All caught up!</Text>
                      </View>
                    </View>
                  </View>
                </View>
              )}
            </View>

            <View style={styles.historyTitleCard}>
              <View style={styles.historyTitleContainer}>
                <View style={styles.iconContainer}>
                  <Text style={styles.emoji}>📋</Text>
                </View>
                <View>
                  <Text style={styles.historyTitle}>Activity History</Text>
                  <Text style={styles.historySubtitle}>Past care activities</Text>
                </View>
              </View>
            </View>

            <View style={styles.settingsCard}>
              {plant?.history && plant.history.length > 0 ? (
                groupHistoryByDate(plant.history).map((group, groupIndex) => (
                  <View key={`group-${groupIndex}`}>
                    <View style={styles.timelineDateHeader}>
                      <Text style={styles.timelineDateText}>{group.date}</Text>
                    </View>
                    {group.items.map((item) => (
                      <View key={item.id} style={styles.timelineItem}>
                        {item.type === 'photo' ? (
                          <TouchableOpacity 
                            style={styles.settingButton}
                            onPress={() => {
                              setSelectedHistoryPhoto(item.photoUrl || null);
                              setShowPhotoViewModal(true);
                            }}
                          >
                            <View style={styles.settingItem}>
                              <View style={styles.settingInfo}>
                                <View style={styles.iconContainer}>
                                  <Text style={styles.settingEmoji}>📸</Text>
                                </View>
                                <View>
                                  <Text style={styles.settingText}>{item.description}</Text>
                                  <Text style={styles.settingDescription}>{formatHistoryDate(new Date(item.date))}</Text>
                                  <Image 
                                    source={{ uri: item.photoUrl }}
                                    style={styles.historyPhoto}
                                  />
                                </View>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ) : (
                          <View style={styles.settingButton}>
                            <View style={styles.settingItem}>
                              <View style={styles.settingInfo}>
                                <View style={styles.iconContainer}>
                                  <Text style={styles.settingEmoji}>
                                    {item.type === 'watering' ? '💧' :
                                     item.type === 'feeding' ? '🌿' :
                                     item.type === 'stage' ? '🌱' :
                                     item.type === 'log' ? '📋' : '📸'}
                                  </Text>
                                </View>
                                <View>
                                  <Text style={styles.settingText}>{item.description}</Text>
                                  <Text style={styles.settingDescription}>{formatHistoryDate(new Date(item.date))}</Text>
                                </View>
                              </View>
                            </View>
                          </View>
                        )}
                      </View>
                    ))}
                  </View>
                ))
              ) : (
                <View style={styles.settingButton}>
                  <View style={styles.settingItem}>
                    <View style={styles.settingInfo}>
                      <View style={styles.iconContainer}>
                        <Text style={styles.settingEmoji}>📋</Text>
                      </View>
                      <View>
                        <Text style={styles.settingText}>No history yet</Text>
                        <Text style={styles.settingDescription}>Start caring for your plant to see{'\n'}activity history</Text>
                      </View>
                    </View>
                  </View>
                </View>
              )}
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={styles.clearHistoryButton}
                onPress={() => {
                  if (Platform.OS === 'web') {
                    setShowClearHistoryModal(true);
                  } else {
                    setShowClearHistoryModal(true);
                  }
                }}
              >
                <Ionicons name="trash-outline" size={20} color="#FF5252" />
                <Text style={styles.clearHistoryText}>Clear History</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.deletePlantButton}
                onPress={() => setShowDeletePlantModal(true)}
              >
                <Ionicons name="close-circle-outline" size={20} color="#FF5252" />
                <Text style={styles.deletePlantText}>Delete Plant</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="plants" plantCount={allPlants.length} />
      </SafeAreaView>

      {showSuccessAlert && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowSuccessAlert(false)}
        >
          <View style={styles.alertOverlay}>
            <View style={styles.alertContent}>
              <Text style={styles.alertEmoji}>{successMessage.emoji}</Text>
              <Text style={styles.alertTitle}>{successMessage.title}</Text>
              <Text style={styles.alertMessage}>{successMessage.message}</Text>
            </View>
          </View>
        </Modal>
      )}

      {showClearHistoryModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowClearHistoryModal(false)}
        >
          <View style={styles.alertOverlay}>
            <View style={[styles.alertContent, { backgroundColor: '#fff' }]}>
              <Text style={[styles.alertEmoji, { color: '#FF5252' }]}>⚠️</Text>
              <Text style={[styles.alertTitle, { color: '#333' }]}>Clear History</Text>
              <Text style={[styles.alertMessage, { color: '#666' }]}>Are you sure you want to clear all history? This action cannot be undone.</Text>
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setShowClearHistoryModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.clearButton]}
                  onPress={() => {
                    clearHistory();
                    setShowClearHistoryModal(false);
                  }}
                >
                  <Text style={styles.clearButtonText}>Clear</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showDeletePlantModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDeletePlantModal(false)}
        >
          <View style={styles.alertOverlay}>
            <View style={[styles.alertContent, { backgroundColor: '#fff' }]}>
              <Text style={[styles.alertEmoji, { color: '#FF5252' }]}>⚠️</Text>
              <Text style={[styles.alertTitle, { color: '#333' }]}>Delete Plant</Text>
              <Text style={[styles.alertMessage, { color: '#666' }]}>Are you sure you want to delete this plant? This action cannot be undone.</Text>
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setShowDeletePlantModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.clearButton]}
                  onPress={() => {
                    deletePlant();
                    setShowDeletePlantModal(false);
                  }}
                >
                  <Text style={styles.clearButtonText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showEnvironmentModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowEnvironmentModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: '#fff', padding: 24, borderRadius: 16 }]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Change Environment</Text>
                <TouchableOpacity 
                  onPress={() => setShowEnvironmentModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.environmentOptions}>
                {environments.length > 0 ? (
                  environments.map((env) => (
                    <TouchableOpacity 
                      key={env.id}
                      style={[
                        styles.environmentOption,
                        plant.environment === env.name && styles.environmentOptionActive
                      ]}
                      onPress={() => handleEnvironmentChange(env.name)}
                    >
                      <View style={styles.environmentInfo}>
                        <Text style={[
                          styles.environmentName,
                          plant.environment === env.name && styles.environmentNameActive
                        ]}>
                          {env.name}
                        </Text>
                        <View style={styles.locationRow}>
                          <Text style={[
                            styles.environmentDescription,
                            plant.environment === env.name && styles.environmentDescriptionActive
                          ]}>
                            {env.location}
                          </Text>
                          <Text style={[
                            styles.plantCount,
                            plant.environment === env.name && styles.plantCountActive
                          ]}>
                            {plantCounts[env.name] || 0} {plantCounts[env.name] === 1 ? 'plant' : 'plants'}
                          </Text>
                        </View>
                        <View style={styles.environmentDetails}>
                          <View style={styles.environmentDetailItem}>
                            <Ionicons name="thermometer-outline" size={16} color={plant.environment === env.name ? '#fff' : '#666'} />
                            <Text style={[
                              styles.environmentDetailText,
                              plant.environment === env.name && styles.environmentDetailTextActive
                            ]}>
                              {env.temperature.min}°{env.temperature.unit} - {env.temperature.max}°{env.temperature.unit}
                            </Text>
                          </View>
                          <View style={styles.environmentDetailItem}>
                            <Ionicons name="water-outline" size={16} color={plant.environment === env.name ? '#fff' : '#666'} />
                            <Text style={[
                              styles.environmentDetailText,
                              plant.environment === env.name && styles.environmentDetailTextActive
                            ]}>
                              {env.humidity.min}% - {env.humidity.max}%
                            </Text>
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.emptyStateContainer}>
                    <Text style={styles.emptyStateText}>No environments available</Text>
                    <Text style={styles.emptyStateDescription}>
                      Please add environments in the Environments tab
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showStageModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowStageModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { 
              backgroundColor: '#fff', 
              padding: 16, 
              paddingBottom: 24, 
              borderRadius: 16,
              maxHeight: '80%',
              width: '90%',
              maxWidth: 400
            }]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Change Plant Stage</Text>
                <TouchableOpacity 
                  onPress={() => setShowStageModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <ScrollView style={{ maxHeight: '80%' }} contentContainerStyle={{ paddingBottom: 8 }}>
                <View style={[styles.environmentOptions, { gap: 8 }]}>
                  {plantStages.map((stage) => (
                    <TouchableOpacity 
                      key={stage.id}
                      style={[
                        styles.environmentOption,
                        { padding: 12 },
                        plant.stage === stage.name && styles.environmentOptionActive
                      ]}
                      onPress={() => handleStageChange(stage.name as 'Seedling Stage' | 'Vegetative Stage' | 'Flowering Stage' | 'Late Flowering' | 'Drying' | 'Curing')}
                    >
                      <View style={[styles.environmentIcon, { width: 40, height: 40, borderRadius: 20 }]}>
                        <Text style={styles.stageEmoji}>{stage.emoji}</Text>
                      </View>
                      <View style={styles.environmentInfo}>
                        <Text style={[
                          styles.environmentOptionText,
                          plant.stage === stage.name && styles.environmentOptionTextActive
                        ]}>{stage.name}</Text>
                        <Text style={[
                          styles.environmentDescription,
                          { fontSize: 12 },
                          plant.stage === stage.name && styles.environmentDescriptionActive
                        ]}>{stage.description}</Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>
          </View>
        </Modal>
      )}

      {showPhotoOptionsModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowPhotoOptionsModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: '#fff', padding: 24, borderRadius: 16 }]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Add Photo</Text>
                <TouchableOpacity 
                  onPress={() => setShowPhotoOptionsModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.photoOptionsContainer}>
                <TouchableOpacity 
                  style={styles.photoOption}
                  onPress={handleTakePhoto}
                >
                  <View style={styles.photoOptionIcon}>
                    <Ionicons name="camera" size={32} color="#4CAF50" />
                  </View>
                  <View style={styles.photoOptionInfo}>
                    <Text style={styles.photoOptionTitle}>Take Photo</Text>
                    <Text style={styles.photoOptionDescription}>Use your camera to take a new photo</Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity 
                  style={styles.photoOption}
                  onPress={() => {
                    setShowPhotoOptionsModal(false);
                    handlePickImage();
                  }}
                >
                  <View style={styles.photoOptionIcon}>
                    <Ionicons name="images" size={32} color="#4CAF50" />
                  </View>
                  <View style={styles.photoOptionInfo}>
                    <Text style={styles.photoOptionTitle}>Choose from Library</Text>
                    <Text style={styles.photoOptionDescription}>Select a photo from your gallery</Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showPhotoViewModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowPhotoViewModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.photoViewContent}>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setShowPhotoViewModal(false)}
              >
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
              {selectedHistoryPhoto && (
                <View style={styles.modalImageContainer}>
                  <Image 
                    source={{ uri: selectedHistoryPhoto }}
                    style={styles.modalImage}
                    resizeMode="contain"
                  />
                </View>
              )}
            </View>
          </View>
        </Modal>
      )}

      {showMoreMenu && (
        <Modal
          visible={true}
          transparent={true}
          animationType="none"
          onRequestClose={() => setShowMoreMenu(false)}
        >
          <TouchableOpacity 
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowMoreMenu(false)}
          >
            <View style={styles.actionMenuContainer}>
              <TouchableOpacity 
                style={styles.actionMenuItem}
                onPress={() => {
                  setShowMoreMenu(false);
                  setTimeout(() => {
                    setShowPlantInfoModal(true);
                    fetchPlantInfo();
                  }, 100);
                }}
              >
                <Ionicons name="leaf-outline" size={20} color="#2E7D32" />
                <Text style={styles.actionMenuItemText}>Plant Information</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.actionMenuItem}
                onPress={() => {
                  setShowMoreMenu(false);
                  setTimeout(() => {
                    setShowLogModal(true);
                  }, 100);
                }}
              >
                <Ionicons name="clipboard-outline" size={20} color="#2E7D32" />
                <Text style={styles.actionMenuItemText}>Add Log</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.actionMenuItem}
                onPress={() => {
                  setShowMoreMenu(false);
                  setTimeout(() => {
                    setShowTaskTypeModal(true);
                  }, 100);
                }}
              >
                <Ionicons name="calendar-outline" size={20} color="#2E7D32" />
                <Text style={styles.actionMenuItemText}>Create Task</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </Modal>
      )}

      {showLogModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowLogModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.logModalContent}>
              <View style={styles.modalHeader}>
                <View>
                  <Text style={styles.modalTitle}>Add Log Entry</Text>
                  <Text style={styles.modalDate}>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</Text>
                </View>
                <TouchableOpacity 
                  onPress={() => setShowLogModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.logForm}>
                <Text style={styles.logLabel}>Notes</Text>
                <TextInput
                  style={styles.logInput}
                  multiline
                  numberOfLines={4}
                  placeholder="Enter your notes here..."
                  value={logNotes}
                  onChangeText={setLogNotes}
                  textAlignVertical="top"
                />
              </View>

              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setShowLogModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.saveButton]}
                  onPress={handleAddLog}
                  disabled={!logNotes.trim()}
                >
                  <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showPlantInfoModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowPlantInfoModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.plantInfoModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Plant Information</Text>
                <TouchableOpacity 
                  onPress={() => setShowPlantInfoModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <View style={styles.plantInfoContentContainer}>
                {isLoadingPlantInfo ? (
                  <View style={styles.loadingContainer}>
                    <Text style={styles.loadingText}>Loading plant information...</Text>
                  </View>
                ) : plantInfo ? (
                  <ScrollView 
                    style={styles.plantInfoScroll}
                    contentContainerStyle={{ paddingBottom: 20 }}
                  >
                    <View style={styles.plantInfoContent}>
                      {formatPlantInfo(plantInfo.description)}
                    </View>
                  </ScrollView>
                ) : (
                  <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>Unable to load plant information</Text>
                    <Text style={styles.errorSubtext}>We encountered an error while trying to fetch information about {plant.name}.</Text>
                    <Text style={styles.errorSubtext}>Please check your internet connection and try again.</Text>
                    <TouchableOpacity 
                      style={[styles.modalButton, styles.saveButton, { marginTop: 16 }]}
                      onPress={fetchPlantInfo}
                    >
                      <Text style={styles.saveButtonText}>Try Again</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showTaskTypeModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowTaskTypeModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.taskTypeModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Create Task</Text>
                <TouchableOpacity 
                  onPress={() => setShowTaskTypeModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.taskTypeOptions}>
                <TouchableOpacity 
                  style={styles.taskTypeOption}
                  onPress={() => {
                    setShowTaskTypeModal(false);
                    createTask('watering', 'Water Plant', 'Time to water your plant', '💧');
                  }}
                >
                  <View style={styles.taskTypeIcon}>
                    <Text style={styles.taskTypeEmoji}>💧</Text>
                  </View>
                  <View style={styles.taskTypeInfo}>
                    <Text style={styles.taskTypeTitle}>Watering</Text>
                    <Text style={styles.taskTypeDescription}>Reminder to water your plant</Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity 
                  style={styles.taskTypeOption}
                  onPress={() => {
                    setShowTaskTypeModal(false);
                    createTask('feeding', 'Feed Plant', 'Time to feed your plant', '🌿');
                  }}
                >
                  <View style={styles.taskTypeIcon}>
                    <Text style={styles.taskTypeEmoji}>🌿</Text>
                  </View>
                  <View style={styles.taskTypeInfo}>
                    <Text style={styles.taskTypeTitle}>Feeding</Text>
                    <Text style={styles.taskTypeDescription}>Reminder to feed your plant</Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity 
                  style={styles.taskTypeOption}
                  onPress={() => {
                    setShowTaskTypeModal(false);
                    createTask('pruning', 'Prune Plant', 'Time to prune your plant', '✂️');
                  }}
                >
                  <View style={styles.taskTypeIcon}>
                    <Text style={styles.taskTypeEmoji}>✂️</Text>
                  </View>
                  <View style={styles.taskTypeInfo}>
                    <Text style={styles.taskTypeTitle}>Pruning</Text>
                    <Text style={styles.taskTypeDescription}>Reminder to prune your plant</Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity 
                  style={styles.taskTypeOption}
                  onPress={() => {
                    setShowTaskTypeModal(false);
                    createTask('check', 'Check Plant', 'Time to check on your plant', '🔍');
                  }}
                >
                  <View style={styles.taskTypeIcon}>
                    <Text style={styles.taskTypeEmoji}>🔍</Text>
                  </View>
                  <View style={styles.taskTypeInfo}>
                    <Text style={styles.taskTypeTitle}>Check-up</Text>
                    <Text style={styles.taskTypeDescription}>Reminder to check on your plant</Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity 
                  style={styles.taskTypeOption}
                  onPress={() => {
                    setShowTaskTypeModal(false);
                    createTask('custom', 'Custom Task', 'Custom plant care task', '📋');
                  }}
                >
                  <View style={styles.taskTypeIcon}>
                    <Text style={styles.taskTypeEmoji}>📋</Text>
                  </View>
                  <View style={styles.taskTypeInfo}>
                    <Text style={styles.taskTypeTitle}>Custom</Text>
                    <Text style={styles.taskTypeDescription}>Create a custom task</Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showTaskScheduleModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowTaskScheduleModal(false)}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.modalOverlay}>
              <View style={styles.taskScheduleModalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>
                    {selectedTaskType?.type === 'watering' ? 'Create Watering Task' :
                     selectedTaskType?.type === 'feeding' ? 'Create Feeding Task' :
                     selectedTaskType?.type === 'pruning' ? 'Create Pruning Task' :
                     selectedTaskType?.type === 'check' ? 'Create Check-up Task' :
                     'Create Task'}
                  </Text>
                  <TouchableOpacity 
                    onPress={() => setShowTaskScheduleModal(false)}
                    style={styles.modalCloseButton}
                  >
                    <Ionicons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                </View>
                
                <View style={styles.taskScheduleForm}>
                  {!isPredefinedTaskType(selectedTaskType?.type || '') && (
                    <>
                      <Text style={styles.taskScheduleLabel}>Task Title</Text>
                      <TextInput
                        style={styles.taskScheduleInput}
                        placeholder="Enter task title"
                        value={selectedTaskType?.title || ''}
                        onChangeText={handleTaskTitleChange}
                        blurOnSubmit={true}
                        returnKeyType="next"
                        onSubmitEditing={() => {
                          if (descriptionInputRef.current) {
                            descriptionInputRef.current.focus();
                          }
                        }}
                      />

                      <Text style={[styles.taskScheduleLabel, { marginTop: 16 }]}>Task Description</Text>
                      <TextInput
                        ref={descriptionInputRef}
                        style={styles.taskScheduleInput}
                        placeholder="Enter task description"
                        value={selectedTaskType?.description || ''}
                        onChangeText={handleTaskDescriptionChange}
                        multiline
                        numberOfLines={3}
                        blurOnSubmit={true}
                        returnKeyType="done"
                        onSubmitEditing={Keyboard.dismiss}
                      />
                    </>
                  )}

                  <View style={styles.taskScheduleSection}>
                    <Text style={[styles.taskScheduleLabel, { marginTop: 16 }]}>When would you like to be reminded?</Text>
                    <TouchableOpacity
                      style={styles.datePickerButton}
                      onPress={() => setShowDateOptions(!showDateOptions)}
                    >
                      <Text style={styles.datePickerButtonText}>
                        {getDateOptionLabel(selectedDateOption)}
                      </Text>
                      <Ionicons 
                        name={showDateOptions ? "chevron-up" : "chevron-down"} 
                        size={24} 
                        color="#2E7D32" 
                      />
                    </TouchableOpacity>

                    {showDateOptions && (
                      <ScrollView style={styles.dateOptions}>
                        {DATE_OPTIONS.map((option) => (
                          <TouchableOpacity
                            key={option.value}
                            style={[
                              styles.dateOption,
                              selectedDateOption === option.value && styles.dateOptionSelected
                            ]}
                            onPress={() => handleDateOptionSelect(option.value)}
                          >
                            <Text style={[
                              styles.dateOptionText,
                              selectedDateOption === option.value && styles.dateOptionTextSelected
                            ]}>
                              {option.label}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    )}

                    <Text style={styles.timeInfoText}>
                      All reminders will be set for 7:00 AM
                    </Text>
                  </View>

                  <View style={styles.taskScheduleSection}>
                    <Text style={styles.taskScheduleLabel}>Would you like this task to repeat?</Text>
                    <TouchableOpacity
                      style={styles.recurringToggle}
                      onPress={() => setIsRecurring(!isRecurring)}
                    >
                      <View style={[styles.toggleOption, isRecurring && styles.toggleOptionActive]}>
                        <Text style={[styles.toggleOptionText, isRecurring && styles.toggleOptionTextActive]}>Yes</Text>
                      </View>
                      <View style={[styles.toggleOption, !isRecurring && styles.toggleOptionActive]}>
                        <Text style={[styles.toggleOptionText, !isRecurring && styles.toggleOptionTextActive]}>No</Text>
                      </View>
                    </TouchableOpacity>
                  </View>

                  {isRecurring && (
                    <View style={styles.taskScheduleSection}>
                      <Text style={styles.taskScheduleLabel}>How often should it repeat?</Text>
                      <TouchableOpacity
                        style={styles.frequencyPickerButton}
                        onPress={() => setShowFrequencyPicker(!showFrequencyPicker)}
                      >
                        <Text style={styles.frequencyPickerButtonText}>
                          {FREQUENCY_OPTIONS.find(opt => opt.value === frequency)?.label || frequency}
                        </Text>
                        <Ionicons 
                          name={showFrequencyPicker ? "chevron-up" : "chevron-down"} 
                          size={24} 
                          color="#2E7D32" 
                        />
                      </TouchableOpacity>

                      {showFrequencyPicker && (
                        <ScrollView style={styles.frequencyOptions}>
                          {FREQUENCY_OPTIONS.map((option) => (
                            <TouchableOpacity
                              key={option.value}
                              style={[
                                styles.frequencyOption,
                                frequency === option.value && styles.frequencyOptionSelected
                              ]}
                              onPress={() => {
                                setFrequency(option.value);
                                setShowFrequencyPicker(false);
                              }}
                            >
                              <Text style={[
                                styles.frequencyOptionText,
                                frequency === option.value && styles.frequencyOptionTextSelected
                              ]}>
                                {option.label}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </ScrollView>
                      )}
                    </View>
                  )}

                  <View style={styles.modalButtons}>
                    <TouchableOpacity 
                      style={[styles.modalButton, styles.cancelButton]}
                      onPress={() => setShowTaskScheduleModal(false)}
                    >
                      <Text style={styles.cancelButtonText}>Cancel</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={[styles.modalButton, styles.saveButton]}
                      onPress={handleCreateTask}
                    >
                      <Text style={styles.saveButtonText}>Save</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}

      {showDatePicker && (
        <Modal
          visible={true}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowDatePicker(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.datePickerContainer}>
              <View style={styles.datePickerHeader}>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Text style={styles.datePickerButton}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Text style={styles.datePickerButton}>Done</Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={selectedTime}
                mode="date"
                display="spinner"
                onChange={handleDateChange}
              />
            </View>
          </View>
        </Modal>
      )}

      {showTimePicker && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowTimePicker(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.timePickerContent}>
              <View style={styles.timePickerHeader}>
                <TouchableOpacity 
                  onPress={() => setShowTimePicker(false)}
                  style={styles.timePickerCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
                <TouchableOpacity 
                  onPress={() => setShowTimePicker(false)}
                  style={styles.timePickerDoneButton}
                >
                  <Text style={styles.timePickerDoneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
              
              <View style={styles.timePickerContainer}>
                <View style={styles.timePickerRow}>
                  <Text style={styles.timePickerLabel}>Hour:</Text>
                  <ScrollView style={styles.timePickerScrollView}>
                    {Array.from({ length: 24 }, (_, i) => (
                      <TouchableOpacity
                        key={i}
                        style={[
                          styles.timePickerOption,
                          selectedTime.getHours() === i && styles.timePickerOptionSelected
                        ]}
                        onPress={() => {
                          const newDate = new Date(selectedTime);
                          newDate.setHours(i);
                          setSelectedTime(newDate);
                        }}
                      >
                        <Text style={[
                          styles.timePickerOptionText,
                          selectedTime.getHours() === i && styles.timePickerOptionTextSelected
                        ]}>
                          {i.toString().padStart(2, '0')}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
                
                <View style={styles.timePickerRow}>
                  <Text style={styles.timePickerLabel}>Minute:</Text>
                  <ScrollView style={styles.timePickerScrollView}>
                    {Array.from({ length: 60 }, (_, i) => (
                      <TouchableOpacity
                        key={i}
                        style={[
                          styles.timePickerOption,
                          selectedTime.getMinutes() === i && styles.timePickerOptionSelected
                        ]}
                        onPress={() => {
                          const newDate = new Date(selectedTime);
                          newDate.setMinutes(i);
                          setSelectedTime(newDate);
                        }}
                      >
                        <Text style={[
                          styles.timePickerOptionText,
                          selectedTime.getMinutes() === i && styles.timePickerOptionTextSelected
                        ]}>
                          {i.toString().padStart(2, '0')}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showTaskDetailsModal && selectedTask && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowTaskDetailsModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.taskDetailsModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Task Details</Text>
                <TouchableOpacity 
                  onPress={() => setShowTaskDetailsModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.taskDetailsContent}>
                <View style={styles.taskDetailRow}>
                  <View style={styles.taskDetailIcon}>
                    <Text style={styles.taskDetailEmoji}>
                      {selectedTask.type === 'watering' ? '💧' :
                       selectedTask.type === 'feeding' ? '🌿' :
                       selectedTask.type === 'schedule' ? '📅' :
                       selectedTask.type === 'pruning' ? '✂️' : '📋'}
                    </Text>
                  </View>
                  <View style={styles.taskDetailInfo}>
                    <Text style={styles.taskDetailTitle}>{selectedTask.title}</Text>
                    <Text style={styles.taskDetailDescription}>
                      {selectedTask.description || 'No description provided'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.taskDetailSection}>
                  <Text style={styles.taskDetailLabel}>Due Date</Text>
                  <Text style={styles.taskDetailValue}>
                    {(() => {
                      const taskDate = new Date(selectedTask.time);
                      const now = new Date();
                      
                      // Set both dates to midnight to get accurate day difference
                      const taskMidnight = new Date(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate());
                      const nowMidnight = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                      
                      // If task date is today or in the future, it's not overdue
                      if (taskMidnight.getTime() >= nowMidnight.getTime()) {
                        return `Due ${formatTime(selectedTask.time, selectedTask.frequency)}`;
                      } else {
                        return `Overdue (${formatTime(selectedTask.time, selectedTask.frequency)})`;
                      }
                    })()}
                  </Text>
                </View>
                
                {selectedTask.isRecurring && (
                  <View style={styles.taskDetailSection}>
                    <Text style={styles.taskDetailLabel}>Recurring</Text>
                    <Text style={[styles.taskDetailValue, { color: '#2E7D32' }]}>
                      {selectedTask.frequency 
                        ? `Repeats every ${selectedTask.frequency.toLowerCase()}`
                        : 'Repeats daily'}
                    </Text>
                  </View>
                )}
                
                <View style={styles.taskDetailSection}>
                  <Text style={styles.taskDetailLabel}>Plant</Text>
                  <Text style={styles.taskDetailValue}>{selectedTask.plantName}</Text>
                </View>
                
                <View style={styles.taskDetailSection}>
                  <Text style={styles.taskDetailLabel}>Task Type</Text>
                  <Text style={styles.taskDetailValue}>
                    {selectedTask.type === 'watering' ? 'Watering' :
                     selectedTask.type === 'feeding' ? 'Feeding' :
                     selectedTask.type === 'schedule' ? 'Scheduled Task' :
                     selectedTask.type === 'pruning' ? 'Pruning' : 'Custom Task'}
                  </Text>
                </View>
              </View>
              
              <View style={styles.modalButtons}>
                {new Date(selectedTask.time).toDateString() === new Date().toDateString() ? (
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.completeButton]}
                    onPress={handleCompleteTask}
                  >
                    <Text style={styles.completeButtonText}>Complete Task</Text>
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.endButton]}
                    onPress={handleEndTask}
                  >
                    <Text style={styles.endButtonText}>End Task</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setShowTaskDetailsModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Close</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  cardContainer: {
    gap: 16,
    padding: 20,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
    gap: 4,
  },
  actionButtonIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  actionButtonText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  settingsCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 8,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    gap: 8,
  },
  settingButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: 16,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    flex: 1,
    paddingRight: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  settingText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    flexWrap: 'wrap',
    flex: 1,
  },
  historyTitleCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    marginBottom: 8,
    marginTop: 24,
  },
  historyTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  historySubtitle: {
    fontSize: 14,
    color: '#666',
  },
  historyList: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
    gap: 12,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 4,
  },
  historyIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  historyContent: {
    flex: 1,
  },
  historyAction: {
    fontSize: 15,
    color: '#2E7D32',
    marginBottom: 2,
  },
  historyDate: {
    fontSize: 13,
    color: '#666',
  },
  historyDescription: {
    fontSize: 13,
    color: '#666',
    marginTop: 4,
  },
  historyDivider: {
    height: 1,
    backgroundColor: '#81C784',
    marginVertical: 8,
  },
  historyPhoto: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#E8F5E9',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: 'transparent',
    position: 'relative',
  },
  modalImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  clearHistoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#FFCDD2',
    flex: 1,
  },
  deletePlantButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    marginBottom: 8,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#FFCDD2',
    flex: 1,
  },
  clearHistoryText: {
    color: '#FF5252',
    fontSize: 16,
    fontWeight: '500',
  },
  deletePlantText: {
    color: '#FF5252',
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  alertOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertContent: {
    backgroundColor: '#F1F8E9',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    width: '80%',
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  alertEmoji: {
    fontSize: 48,
    marginBottom: 16,
  },
  alertTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 8,
    textAlign: 'center',
  },
  alertMessage: {
    fontSize: 16,
    color: '#2E7D32',
    opacity: 0.9,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 24,
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginLeft: 8,
  },
  cancelButton: {
    backgroundColor: '#E8F5E9',
  },
  clearButton: {
    backgroundColor: '#FF5252',
  },
  cancelButtonText: {
    color: '#2E7D32',
    fontSize: 16,
    fontWeight: '500',
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  emoji: {
    fontSize: 24,
  },
  historyEmoji: {
    fontSize: 16,
  },
  actionButtonEmoji: {
    fontSize: 16,
  },
  settingEmoji: {
    fontSize: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  environmentOptions: {
    gap: 16,
  },
  environmentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F1F8E9',
    borderWidth: 1,
    borderColor: '#C8E6C9',
    gap: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  environmentOptionActive: {
    backgroundColor: '#F1F8E9',
    borderColor: '#4CAF50',
    borderWidth: 2,
  },
  environmentIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#C8E6C9',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#81C784',
  },
  environmentIconActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
    borderWidth: 2,
  },
  environmentInfo: {
    flex: 1,
  },
  environmentName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  environmentNameActive: {
    color: '#4CAF50',
  },
  environmentDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  environmentDescriptionActive: {
    color: '#4CAF50',
  },
  locationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  plantCount: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#E8F5E9',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  plantCountActive: {
    color: '#4CAF50',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  environmentDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  environmentDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  environmentDetailText: {
    fontSize: 12,
    color: '#666',
  },
  environmentDetailTextActive: {
    color: '#4CAF50',
  },
  photoOptionsContainer: {
    gap: 16,
    marginTop: 16,
  },
  photoOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F1F8E9',
    borderWidth: 1,
    borderColor: '#C8E6C9',
    gap: 12,
  },
  photoOptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  photoOptionInfo: {
    flex: 1,
  },
  photoOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 4,
  },
  photoOptionDescription: {
    fontSize: 14,
    color: '#666',
  },
  photoViewContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionMenuContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -125 }, { translateY: -75 }], // Half of width and approximate half of height
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 10,
    width: 250,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
  actionMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  actionMenuItemText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
  },
  logModalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logForm: {
    marginTop: 16,
  },
  logLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 8,
  },
  logInput: {
    backgroundColor: '#F1F8E9',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  moreMenuItemEmoji: {
    fontSize: 24,
  },
  plantInfoModalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    height: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  plantInfoContentContainer: {
    flex: 1,
    marginTop: 16,
  },
  plantInfoScroll: {
    flex: 1,
  },
  plantInfoContent: {
    padding: 16,
  },
  plantInfoDescription: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  loadingContainer: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  errorContainer: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
  },
  errorText: {
    fontSize: 16,
    color: '#FF5252',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
  },
  plantInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  plantInfoCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  plantInfoCardIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  plantInfoCardIconText: {
    fontSize: 20,
    color: '#2E7D32',
  },
  plantInfoCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2E7D32',
  },
  plantInfoCardContent: {
    marginTop: 8,
  },
  plantInfoCardText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stageEmoji: {
    fontSize: 24,
  },
  environmentOptionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4CAF50',
  },
  environmentOptionTextActive: {
    color: '#4CAF50',
  },
  modalDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  timelineDateHeader: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    backgroundColor: '#E8F5E9',
    borderRadius: 8,
    marginBottom: 8,
    marginTop: 12,
  },
  timelineDateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E7D32',
  },
  timelineItem: {
    marginBottom: 8,
  },
  taskTypeModalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignSelf: 'center',
    marginHorizontal: 'auto',
    marginVertical: 'auto',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  taskTypeOptions: {
    gap: 16,
    marginTop: 16,
  },
  taskTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F1F8E9',
    borderWidth: 1,
    borderColor: '#C8E6C9',
    gap: 12,
  },
  taskTypeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  taskTypeEmoji: {
    fontSize: 24,
  },
  taskTypeInfo: {
    flex: 1,
  },
  taskTypeTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 4,
  },
  taskTypeDescription: {
    fontSize: 14,
    color: '#666',
  },
  taskScheduleModalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 2,
  },
  taskScheduleForm: {
    marginTop: 16,
  },
  taskScheduleSection: {
    marginBottom: 24,
  },
  taskScheduleLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
    marginBottom: 12,
  },
  taskScheduleInput: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    color: '#333',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  datePickerButtonText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  timePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  timePickerButtonText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  recurringToggle: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 4,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  toggleOption: {
    flex: 1,
    padding: 8,
    borderRadius: 6,
    alignItems: 'center',
  },
  toggleOptionActive: {
    backgroundColor: '#2E7D32',
  },
  toggleOptionText: {
    fontSize: 16,
    color: '#333',
  },
  toggleOptionTextActive: {
    color: '#fff',
  },
  frequencyPickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  frequencyPickerButtonText: {
    fontSize: 16,
    color: '#333',
  },
  frequencyOptions: {
    marginTop: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    maxHeight: 200,
  },
  frequencyOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  frequencyOptionSelected: {
    backgroundColor: '#F5F5F5',
  },
  frequencyOptionText: {
    fontSize: 16,
    color: '#333',
  },
  frequencyOptionTextSelected: {
    color: '#2E7D32',
    fontWeight: '500',
  },
  datePickerContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  datePickerActionButton: {
    fontSize: 16,
    color: '#007AFF',
    padding: 10,
  },
  timePickerContent: {
    width: '100%',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  timePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  timePickerCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  timePickerDoneButton: {
    width: 80,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  timePickerDoneButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  timePickerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  timePickerLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E7D32',
  },
  timePickerScrollView: {
    maxHeight: 150,
  },
  timePickerOption: {
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  timePickerOptionSelected: {
    backgroundColor: '#E8F5E9',
  },
  timePickerOptionText: {
    fontSize: 16,
    color: '#333',
  },
  timePickerOptionTextSelected: {
    color: '#2E7D32',
    fontWeight: '500',
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  datePickerScrollView: {
    maxHeight: 300,
  },
  timePickerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  dateOptions: {
    marginTop: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    maxHeight: 200,
  },
  dateOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  dateOptionSelected: {
    backgroundColor: '#F5F5F5',
  },
  dateOptionText: {
    fontSize: 16,
    color: '#333',
  },
  dateOptionTextSelected: {
    color: '#2E7D32',
    fontWeight: '500',
  },
  timeInfoText: {
    fontSize: 14,
    color: '#666',
    marginTop: 12,
  },
  taskDetailsModalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  taskDetailsContent: {
    marginTop: 16,
  },
  taskDetailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  taskDetailIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  taskDetailEmoji: {
    fontSize: 24,
  },
  taskDetailInfo: {
    flex: 1,
  },
  taskDetailTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  taskDetailDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  taskDetailSection: {
    marginBottom: 16,
  },
  taskDetailLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 4,
  },
  taskDetailValue: {
    fontSize: 16,
    color: '#333',
  },
  completeButton: {
    backgroundColor: '#2E7D32',
    marginRight: 8,
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  endButton: {
    backgroundColor: '#F44336',
    marginRight: 8,
  },
  endButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  nameEditContainer: {
    marginBottom: 8,
  },
  nameInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.7)',
    paddingBottom: 4,
  },
  addTaskButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#C8E6C9',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
    marginBottom: 2,
    fontStyle: 'italic',
  },
}); 