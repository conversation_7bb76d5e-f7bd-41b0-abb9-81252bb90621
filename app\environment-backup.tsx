import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Modal, Alert, TextInput, Image } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import Header from '../components/Header';
import Footer from '../components/Footer';

interface Environment {
  id: string;
  name: string;
  type: 'indoor' | 'outdoor';
  description: string;
  temperature: {
    min: number;
    max: number;
    unit: 'C' | 'F';
  };
  humidity: {
    min: number;
    max: number;
    unit: '%';
  };
  lightIntensity: {
    level: 'low' | 'medium' | 'high' | 'very_high';
    description: string;
  };
  notes: string;
}

const STORAGE_KEY = '@environments';
const SETTINGS_KEY = '@settings_data';

const defaultEnvironments: Environment[] = [
  {
    id: '1',
    name: 'Indoor Tent',
    type: 'indoor',
    description: 'Standard 4x4 grow tent with LED lighting',
    temperature: {
      min: 20,
      max: 28,
      unit: 'C'
    },
    humidity: {
      min: 40,
      max: 60,
      unit: '%'
    },
    lightIntensity: {
      level: 'high',
      description: 'Strong LED lighting system'
    },
    notes: 'Ventilation system with carbon filter'
  },
  {
    id: '2',
    name: 'Outdoor Garden',
    type: 'outdoor',
    description: 'Sunny backyard garden with partial shade',
    temperature: {
      min: 15,
      max: 35,
      unit: 'C'
    },
    humidity: {
      min: 30,
      max: 70,
      unit: '%'
    },
    lightIntensity: {
      level: 'very_high',
      description: 'Full sun with partial shade'
    },
    notes: 'Natural sunlight with wind protection'
  }
];

export default function EnvironmentScreen() {
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [selectedEnvironment, setSelectedEnvironment] = useState<Environment | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [userUnit, setUserUnit] = useState<'C' | 'F'>('F');
  const [newEnvironment, setNewEnvironment] = useState<Partial<Environment>>({
    name: '',
    type: 'indoor',
    description: '',
    temperature: { min: 20, max: 28, unit: 'C' },
    humidity: { min: 40, max: 60, unit: '%' },
    lightIntensity: { level: 'medium', description: 'Standard lighting' },
    notes: ''
  });
  const router = useRouter();

  useEffect(() => {
    loadEnvironments();
    loadUserSettings();
  }, []);

  useEffect(() => {
    saveEnvironments(environments);
  }, [environments]);

  const loadUserSettings = async () => {
    try {
      const storedSettings = await AsyncStorage.getItem(SETTINGS_KEY);
      if (storedSettings) {
        const settings = JSON.parse(storedSettings);
        setUserUnit(settings.measurementUnit);
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }
  };

  const convertTemperature = (temp: number, fromUnit: 'C' | 'F', toUnit: 'C' | 'F'): number => {
    if (fromUnit === toUnit) return temp;
    if (fromUnit === 'C' && toUnit === 'F') return (temp * 9/5) + 32;
    if (fromUnit === 'F' && toUnit === 'C') return (temp - 32) * 5/9;
    return temp;
  };

  const displayTemperature = (temp: number, unit: 'C' | 'F'): number => {
    return convertTemperature(temp, unit, userUnit);
  };

  const handleTemperatureChange = (value: string, field: 'min' | 'max') => {
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      setNewEnvironment(prev => ({
        ...prev,
        temperature: {
          ...prev.temperature!,
          [field]: convertTemperature(numValue, userUnit, 'C')
        }
      }));
    } else {
      // Handle empty or invalid input by setting to 0
      setNewEnvironment(prev => ({
        ...prev,
        temperature: {
          ...prev.temperature!,
          [field]: 0
        }
      }));
    }
  };

  const loadEnvironments = async () => {
    try {
      console.log('Loading environments from AsyncStorage...');
      const storedEnvironments = await AsyncStorage.getItem(STORAGE_KEY);
      console.log('Stored environments data:', storedEnvironments ? 'Data exists' : 'No data found');
      
      if (storedEnvironments) {
        const parsedEnvironments = JSON.parse(storedEnvironments);
        console.log('Parsed environments:', parsedEnvironments);
        setEnvironments(parsedEnvironments);
      } else {
        console.log('No stored environments found, setting default environments');
        setEnvironments(defaultEnvironments);
      }
    } catch (error) {
      console.error('Error loading environments:', error);
      setEnvironments(defaultEnvironments);
    }
  };

  const saveEnvironments = async (environmentsToSave: Environment[]) => {
    try {
      console.log('Saving environments to AsyncStorage:', environmentsToSave);
      const jsonValue = JSON.stringify(environmentsToSave);
      console.log('JSON string length:', jsonValue.length);
      
      await AsyncStorage.setItem(STORAGE_KEY, jsonValue);
      console.log('Successfully saved environments to AsyncStorage');
      
      // Verify the save
      const savedData = await AsyncStorage.getItem(STORAGE_KEY);
      console.log('Verification - Read back from AsyncStorage:', savedData ? 'Data exists' : 'No data found');
      if (savedData) {
        console.log('Verification - Parsed data:', JSON.parse(savedData));
      }
    } catch (error) {
      console.error('Error saving environments:', error);
    }
  };

  const handleEnvironmentPress = (environment: Environment) => {
    setSelectedEnvironment(environment);
    setIsEditing(false);
    setIsModalVisible(true);
  };

  const handleAddEnvironment = () => {
    setSelectedEnvironment(null);
    setIsEditing(true);
    setNewEnvironment({
      name: '',
      type: 'indoor',
      description: '',
      temperature: { min: 20, max: 28, unit: 'C' },
      humidity: { min: 40, max: 60, unit: '%' },
      lightIntensity: { level: 'medium', description: 'Standard lighting' },
      notes: ''
    });
    setIsModalVisible(true);
  };

  const handleEditEnvironment = (environment: Environment) => {
    setIsEditing(true);
    setSelectedEnvironment(environment);
    setNewEnvironment({
      ...environment,
      temperature: {
        ...environment.temperature,
        unit: 'C'
      }
    });
  };

  const handleSaveEnvironment = async () => {
    if (!newEnvironment.name || !newEnvironment.description) {
      Alert.alert('Error', 'Please enter both name and description');
      return;
    }

    try {
      let updatedEnvironments: Environment[];
      
      if (selectedEnvironment) {
        // Update existing environment
        updatedEnvironments = environments.map(env => 
          env.id === selectedEnvironment.id 
            ? {
                ...env,
                name: newEnvironment.name || '',
                type: newEnvironment.type || 'indoor',
                description: newEnvironment.description || '',
                temperature: newEnvironment.temperature || { min: 20, max: 28, unit: 'C' },
                humidity: newEnvironment.humidity || { min: 40, max: 60, unit: '%' },
                lightIntensity: newEnvironment.lightIntensity || { level: 'medium', description: 'Standard lighting' },
                notes: newEnvironment.notes || ''
              }
            : env
        );
        console.log('Updating environment:', selectedEnvironment.id);
      } else {
        // Create new environment
        const newEnv: Environment = {
          id: Date.now().toString(),
          name: newEnvironment.name || '',
          type: newEnvironment.type || 'indoor',
          description: newEnvironment.description || '',
          temperature: newEnvironment.temperature || { min: 20, max: 28, unit: 'C' },
          humidity: newEnvironment.humidity || { min: 40, max: 60, unit: '%' },
          lightIntensity: newEnvironment.lightIntensity || { level: 'medium', description: 'Standard lighting' },
          notes: newEnvironment.notes || ''
        };
        console.log('Creating new environment:', newEnv);
        updatedEnvironments = [...environments, newEnv];
      }
      
      console.log('Updated environments:', updatedEnvironments);
      
      // Save to AsyncStorage first
      await saveEnvironments(updatedEnvironments);
      
      // Then update state
      setEnvironments(updatedEnvironments);
      console.log('Updated state with environments');
      
      // Reset form and close modal
      setIsEditing(false);
      setIsModalVisible(false);
      setSelectedEnvironment(null);
      setNewEnvironment({
        name: '',
        type: 'indoor',
        description: '',
        temperature: { min: 20, max: 28, unit: 'C' },
        humidity: { min: 40, max: 60, unit: '%' },
        lightIntensity: { level: 'medium', description: 'Standard lighting' },
        notes: ''
      });
      
      Alert.alert('Success', 'Environment saved successfully');
    } catch (error) {
      console.error('Error saving environment:', error);
      Alert.alert('Error', 'Failed to save environment');
    }
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSelectedEnvironment(null);
    setIsEditing(false);
    setNewEnvironment({
      name: '',
      type: 'indoor',
      description: '',
      temperature: { min: 20, max: 28, unit: 'C' },
      humidity: { min: 40, max: 60, unit: '%' },
      lightIntensity: { level: 'medium', description: 'Standard lighting' },
      notes: ''
    });
  };

  const clearEnvironments = async () => {
    try {
      console.log('Clearing environments from AsyncStorage...');
      await AsyncStorage.removeItem(STORAGE_KEY);
      console.log('Successfully cleared environments from AsyncStorage');
      setEnvironments(defaultEnvironments);
      await saveEnvironments(defaultEnvironments);
    } catch (error) {
      console.error('Error clearing environments:', error);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Growing Environments</Text>
            <Text style={styles.heroDescription}>Manage your plant growing spaces</Text>
          </View>
        </View>
        <View style={styles.addButtonContainer}>
          <TouchableOpacity style={styles.addButton} onPress={handleAddEnvironment}>
            <Ionicons name="add-circle" size={24} color="#2E7D32" />
            <Text style={styles.addButtonText}>Add New Environment</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.addButton, { marginLeft: 10, backgroundColor: '#FFEBEE' }]} 
            onPress={clearEnvironments}
          >
            <Ionicons name="trash-outline" size={24} color="#D32F2F" />
            <Text style={[styles.addButtonText, { color: '#D32F2F' }]}>Reset</Text>
          </TouchableOpacity>
        </View>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            <View style={styles.settingsCard}>
              {environments.length === 0 ? (
                <Text style={styles.emptyText}>No environments set up</Text>
              ) : (
                environments.map((environment) => (
                  <TouchableOpacity 
                    key={environment.id} 
                    style={styles.settingButton}
                    onPress={() => handleEnvironmentPress(environment)}
                  >
                    <View style={styles.settingItem}>
                      <View style={styles.settingInfo}>
                        <View style={styles.iconContainer}>
                          <Ionicons 
                            name={environment.type === 'indoor' ? 'home' : 'sunny'} 
                            size={24} 
                            color="#4CAF50" 
                          />
                        </View>
                        <View>
                          <Text style={styles.settingText}>{environment.name}</Text>
                          <Text style={styles.settingDescription}>
                            {environment.description}
                          </Text>
                          <Text style={styles.scheduleDays}>
                            {environment.type === 'indoor' ? 'Indoor' : 'Outdoor'} • {displayTemperature(environment.temperature.min, 'C').toFixed(1)}°{userUnit} - {displayTemperature(environment.temperature.max, 'C').toFixed(1)}°{userUnit}
                          </Text>
                        </View>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </View>
                  </TouchableOpacity>
                ))
              )}
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="settings" />

        <Modal
          animationType="slide"
          transparent={true}
          visible={isModalVisible}
          onRequestClose={closeModal}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {isEditing ? (selectedEnvironment ? 'Edit Environment' : 'New Environment') : selectedEnvironment?.name}
                </Text>
                <TouchableOpacity onPress={closeModal}>
                  <Ionicons name="close" size={24} color="#4CAF50" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.modalMainContent}>
                <ScrollView style={styles.modalScrollView}>
              <View style={styles.modalBody}>
                {isEditing ? (
                  <>
                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Name</Text>
                      <TextInput
                        style={styles.input}
                        value={newEnvironment.name}
                        onChangeText={(text) => setNewEnvironment(prev => ({ ...prev, name: text }))}
                        placeholder="Enter environment name"
                      />
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Type</Text>
                      <View style={styles.typeButtons}>
                        <TouchableOpacity
                          style={[
                            styles.typeButton,
                            newEnvironment.type === 'indoor' && styles.typeButtonActive
                          ]}
                          onPress={() => setNewEnvironment(prev => ({ ...prev, type: 'indoor' }))}
                        >
                          <Text style={[
                            styles.typeButtonText,
                            newEnvironment.type === 'indoor' && styles.typeButtonTextActive
                          ]}>Indoor</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[
                            styles.typeButton,
                            newEnvironment.type === 'outdoor' && styles.typeButtonActive
                          ]}
                          onPress={() => setNewEnvironment(prev => ({ ...prev, type: 'outdoor' }))}
                        >
                          <Text style={[
                            styles.typeButtonText,
                            newEnvironment.type === 'outdoor' && styles.typeButtonTextActive
                          ]}>Outdoor</Text>
                        </TouchableOpacity>
                      </View>
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Description</Text>
                      <TextInput
                            style={[styles.input, styles.textArea]}
                        value={newEnvironment.description}
                        onChangeText={(text) => setNewEnvironment(prev => ({ ...prev, description: text }))}
                            placeholder="Enter environment description"
                            multiline
                      />
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Temperature Range</Text>
                      <View style={styles.rangeInputs}>
                            <View style={[styles.inputContainer, styles.rangeInput]}>
                        <TextInput
                                style={styles.input}
                          value={displayTemperature(newEnvironment.temperature?.min || 0, 'C').toFixed(1)}
                          onChangeText={(text) => handleTemperatureChange(text, 'min')}
                          keyboardType="numeric"
                        />
                            </View>
                            <Text style={styles.rangeSeparator}>-</Text>
                            <View style={[styles.inputContainer, styles.rangeInput]}>
                        <TextInput
                                style={styles.input}
                          value={displayTemperature(newEnvironment.temperature?.max || 0, 'C').toFixed(1)}
                          onChangeText={(text) => handleTemperatureChange(text, 'max')}
                          keyboardType="numeric"
                        />
                            </View>
                        <Text style={styles.unitText}>°{userUnit}</Text>
                      </View>
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Humidity Range</Text>
                      <View style={styles.rangeInputs}>
                            <View style={[styles.inputContainer, styles.rangeInput]}>
                        <TextInput
                                style={styles.input}
                          value={newEnvironment.humidity?.min?.toString() || '0'}
                                onChangeText={(text) => {
                                  const num = Number(text);
                                  if (!isNaN(num)) {
                                    setNewEnvironment(prev => ({
                            ...prev,
                                      humidity: {
                                        ...prev.humidity!,
                                        min: num
                                      }
                                    }));
                                  } else {
                                    setNewEnvironment(prev => ({
                                      ...prev,
                                      humidity: {
                                        ...prev.humidity!,
                                        min: 0
                                      }
                                    }));
                                  }
                                }}
                          keyboardType="numeric"
                        />
                            </View>
                            <Text style={styles.rangeSeparator}>-</Text>
                            <View style={[styles.inputContainer, styles.rangeInput]}>
                        <TextInput
                                style={styles.input}
                          value={newEnvironment.humidity?.max?.toString() || '0'}
                                onChangeText={(text) => {
                                  const num = Number(text);
                                  if (!isNaN(num)) {
                                    setNewEnvironment(prev => ({
                            ...prev,
                                      humidity: {
                                        ...prev.humidity!,
                                        max: num
                                      }
                                    }));
                                  } else {
                                    setNewEnvironment(prev => ({
                                      ...prev,
                                      humidity: {
                                        ...prev.humidity!,
                                        max: 0
                                      }
                                    }));
                                  }
                                }}
                          keyboardType="numeric"
                        />
                            </View>
                        <Text style={styles.unitText}>%</Text>
                      </View>
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Light Intensity</Text>
                      <View style={styles.lightLevelButtons}>
                            {['low', 'medium', 'high', 'very_high'].map((level) => (
                        <TouchableOpacity
                                key={level}
                          style={[
                            styles.lightLevelButton,
                                  newEnvironment.lightIntensity?.level === level && styles.lightLevelButtonActive
                          ]}
                          onPress={() => setNewEnvironment(prev => ({
                            ...prev,
                                  lightIntensity: {
                                    level: level as 'low' | 'medium' | 'high' | 'very_high',
                                    description: level === 'low' ? 'Low light conditions, suitable for shade-tolerant plants' :
                                               level === 'medium' ? 'Moderate light conditions, good for most houseplants' :
                                               level === 'high' ? 'Bright light conditions, ideal for sun-loving plants' :
                                               'Very bright light conditions, perfect for full-sun plants'
                                  }
                          }))}
                        >
                          <Text style={[
                            styles.lightLevelButtonText,
                                  newEnvironment.lightIntensity?.level === level && styles.lightLevelButtonTextActive
                                ]}>
                                  {level.split('_').map(word => 
                                    word.charAt(0).toUpperCase() + word.slice(1)
                                  ).join(' ')}
                                </Text>
                        </TouchableOpacity>
                            ))}
                      </View>
                          <View style={styles.lightDescriptionInput}>
                      <TextInput
                              style={[styles.input, styles.textArea]}
                        value={newEnvironment.lightIntensity?.description || ''}
                        onChangeText={(text) => setNewEnvironment(prev => ({
                          ...prev,
                                lightIntensity: {
                                  ...prev.lightIntensity!,
                                  description: text
                                }
                        }))}
                              placeholder="Enter light intensity description"
                              multiline
                      />
                          </View>
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Notes</Text>
                      <TextInput
                        style={[styles.input, styles.textArea]}
                        value={newEnvironment.notes || ''}
                        onChangeText={(text) => setNewEnvironment(prev => ({ ...prev, notes: text }))}
                            placeholder="Enter additional notes"
                        multiline
                      />
                    </View>

                    <TouchableOpacity 
                      style={styles.saveButton}
                      onPress={handleSaveEnvironment}
                    >
                      <Text style={styles.saveButtonText}>Save Environment</Text>
                    </TouchableOpacity>
                  </>
                ) : (
                  <>
                    <View style={styles.detailRow}>
                      <Ionicons name="home-outline" size={20} color="#4CAF50" />
                      <View style={styles.detailText}>
                        <Text style={styles.detailLabel}>Type</Text>
                        <Text style={styles.detailValue}>
                          {selectedEnvironment?.type === 'indoor' ? 'Indoor' : 'Outdoor'}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.detailRow}>
                      <Ionicons name="information-circle-outline" size={20} color="#4CAF50" />
                      <View style={styles.detailText}>
                        <Text style={styles.detailLabel}>Description</Text>
                        <Text style={styles.detailValue}>{selectedEnvironment?.description}</Text>
                      </View>
                    </View>

                    <View style={styles.detailRow}>
                      <Ionicons name="thermometer-outline" size={20} color="#4CAF50" />
                      <View style={styles.detailText}>
                        <Text style={styles.detailLabel}>Temperature Range</Text>
                        <Text style={styles.detailValue}>
                          {displayTemperature(selectedEnvironment?.temperature.min || 0, 'C').toFixed(1)}°{userUnit} - {displayTemperature(selectedEnvironment?.temperature.max || 0, 'C').toFixed(1)}°{userUnit}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.detailRow}>
                      <Ionicons name="water-outline" size={20} color="#4CAF50" />
                      <View style={styles.detailText}>
                        <Text style={styles.detailLabel}>Humidity Range</Text>
                        <Text style={styles.detailValue}>
                          {selectedEnvironment?.humidity.min}{selectedEnvironment?.humidity.unit} - {selectedEnvironment?.humidity.max}{selectedEnvironment?.humidity.unit}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.detailRow}>
                      <Ionicons name="sunny-outline" size={20} color="#4CAF50" />
                      <View style={styles.detailText}>
                        <Text style={styles.detailLabel}>Light Intensity</Text>
                        <Text style={styles.detailValue}>
                          {selectedEnvironment?.lightIntensity.level.split('_').map(word => 
                            word.charAt(0).toUpperCase() + word.slice(1)
                          ).join(' ')}
                        </Text>
                        <Text style={styles.detailDescription}>
                          {selectedEnvironment?.lightIntensity.description}
                        </Text>
                      </View>
                    </View>

                    {selectedEnvironment?.notes && (
                      <View style={styles.detailRow}>
                        <Ionicons name="document-text-outline" size={20} color="#4CAF50" />
                        <View style={styles.detailText}>
                          <Text style={styles.detailLabel}>Notes</Text>
                          <Text style={styles.detailValue}>{selectedEnvironment.notes}</Text>
                        </View>
                      </View>
                    )}
                  </>
                )}
          </View>
        </ScrollView>
                {!isEditing && (
                  <View style={styles.modalFooter}>
                    <TouchableOpacity 
                      style={styles.editButton}
                      onPress={() => handleEditEnvironment(selectedEnvironment!)}
                    >
                      <Text style={styles.editButtonText}>Edit Environment</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  cardContainer: {
    padding: 20,
    gap: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  cardDescription: {
    fontSize: 14,
    color: '#4CAF50',
    marginTop: 4,
  },
  addButtonContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  addButtonText: {
    marginLeft: 8,
    color: '#2E7D32',
    fontSize: 14,
    fontWeight: '500',
  },
  settingsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  settingButton: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E8F5E9',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingText: {
    fontSize: 16,
    color: '#2E7D32',
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 14,
    color: '#4CAF50',
    marginTop: 2,
  },
  scheduleDays: {
    fontSize: 12,
    color: '#9E9E9E',
    marginTop: 2,
  },
  emptyText: {
    fontSize: 16,
    color: '#81C784',
    textAlign: 'center',
    padding: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    width: '100%',
    maxHeight: '98%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E8F5E9',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalMainContent: {
    padding: 12,
  },
  modalScrollView: {
    maxHeight: '90%',
  },
  modalBody: {
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailText: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: '#9E9E9E',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#2E7D32',
    fontWeight: '500',
  },
  inputContainer: {
    gap: 2,
  },
  inputLabel: {
    fontSize: 13,
    color: '#2E7D32',
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E8F5E9',
    borderRadius: 6,
    padding: 6,
    fontSize: 14,
    color: '#2E7D32',
  },
  textArea: {
    height: 60,
    textAlignVertical: 'top',
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 6,
  },
  typeButton: {
    flex: 1,
    padding: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E8F5E9',
    alignItems: 'center',
  },
  typeButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  typeButtonText: {
    fontSize: 14,
    color: '#2E7D32',
  },
  typeButtonTextActive: {
    color: '#fff',
  },
  rangeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rangeInput: {
    flex: 1,
  },
  rangeSeparator: {
    fontSize: 14,
    color: '#2E7D32',
  },
  unitText: {
    fontSize: 14,
    color: '#2E7D32',
    width: 32,
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  editButton: {
    backgroundColor: '#E8F5E9',
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  editButtonText: {
    color: '#2E7D32',
    fontSize: 14,
    fontWeight: '500',
  },
  lightLevelButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 2,
  },
  lightLevelButton: {
    flex: 1,
    minWidth: '48%',
    padding: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E8F5E9',
    alignItems: 'center',
  },
  lightLevelButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  lightLevelButtonText: {
    fontSize: 14,
    color: '#2E7D32',
  },
  lightLevelButtonTextActive: {
    color: '#fff',
  },
  lightDescriptionInput: {
    marginTop: 2,
  },
  detailDescription: {
    fontSize: 14,
    color: '#4CAF50',
    marginTop: 2,
  },
  modalFooter: {
    padding: 10,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E8F5E9',
  },
  enterButton: {
    backgroundColor: '#2E7D32',
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 8,
    marginTop: 40,
    alignSelf: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)',
    elevation: 5,
  },
  notificationCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E8F5E9',
  },
  actionButtonIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
}); 