import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, ActivityIndicator, Alert, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { db } from '../../firebaseConfig';
import { doc, getDoc, deleteDoc } from 'firebase/firestore';

interface Strain {
  id: string;
  name: string;
  description: string;
  effects: string | string[];
  flavor: string | string[];
  thc: string;
  cbd: string;
  imageUrl?: string;
  Type?: string | string[];
  Rating?: number | string | string[];
}

const STORAGE_KEY = '@strains_data';

export default function StrainDetailScreen() {
  const [strain, setStrain] = useState<Strain | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const params = useLocalSearchParams();
  const router = useRouter();
  const strainId = params.id as string;

  useEffect(() => {
    loadStrain();
  }, [strainId]);

  const loadStrain = async () => {
    try {
      setIsLoading(true);
      
      // Check if we have strain data passed in the route params
      if (params && params.id) {
        const strainData: Strain = {
          id: params.id as string,
          name: params.name as string,
          description: params.description as string,
          effects: (params.effects as string).split(','),
          flavor: (params.flavor as string).split(','),
          thc: params.thc as string,
          cbd: params.cbd as string,
          imageUrl: params.imageUrl as string,
          Type: (params.type as string).split(','),
          Rating: (params.rating as string).split(',')
        };
        
        setStrain(strainData);
        setIsLoading(false);
        return;
      }
      
      // If no params, try to load from Firebase
      const strainDoc = await getDoc(doc(db, 'strains', strainId));
      
      if (strainDoc.exists()) {
        const strainData = strainDoc.data() as Strain;
        setStrain(strainData);
        
        // Save to AsyncStorage for offline access
        try {
          const storedStrains = await AsyncStorage.getItem(STORAGE_KEY);
          const strains = storedStrains ? JSON.parse(storedStrains) : {};
          strains[strainId] = strainData;
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(strains));
        } catch (error) {
          console.error('Error saving strain to AsyncStorage:', error);
        }
      } else {
        // If not in Firebase, try AsyncStorage
        try {
          const storedStrains = await AsyncStorage.getItem(STORAGE_KEY);
          if (storedStrains) {
            const strains = JSON.parse(storedStrains);
            if (strains[strainId]) {
              setStrain(strains[strainId]);
            } else {
              setError('Strain not found');
            }
          } else {
            setError('Strain not found');
          }
        } catch (error) {
          console.error('Error loading strain from AsyncStorage:', error);
          setError('Error loading strain data');
        }
      }
    } catch (error) {
      console.error('Error loading strain:', error);
      setError('Error loading strain data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = () => {
    if (strain) {
      router.push({
        pathname: '/strain/edit/[id]',
        params: { 
          id: strain.id,
          name: strain.name,
          description: strain.description,
          effects: strain.effects,
          flavor: strain.flavor,
          thc: strain.thc,
          cbd: strain.cbd,
          imageUrl: strain.imageUrl || ''
        }
      });
    }
  };

  const handleDelete = async () => {
    Alert.alert(
      'Delete Strain',
      'Are you sure you want to delete this strain?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete from Firebase
              await deleteDoc(doc(db, 'strains', strainId));
              
              // Also update AsyncStorage
              const storedStrains = await AsyncStorage.getItem(STORAGE_KEY);
              
              if (storedStrains) {
                const strains = JSON.parse(storedStrains);
                const updatedStrains = strains.filter((s: Strain) => s.id !== strainId);
                await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedStrains));
              }
              
              // Navigate back to strains page
              router.push('/strains');
            } catch (error) {
              console.error('Error deleting strain:', error);
              Alert.alert('Error', 'Failed to delete strain. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleBack = () => {
    router.back();
  };

  // Format array or string data for display
  const formatData = (data: string | string[] | number | undefined): string => {
    if (!data) return 'N/A';
    if (Array.isArray(data)) {
      return data.join(', ');
    }
    return String(data);
  };

  // Format type data for display
  const formatType = (type: string | string[] | undefined): string => {
    if (!type) return 'N/A';
    if (Array.isArray(type)) {
      return type.map(t => t.charAt(0).toUpperCase() + t.slice(1)).join(', ');
    }
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#66BB6A" />
        <Text style={styles.loadingText}>Loading strain details...</Text>
      </View>
    );
  }

  if (!strain) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={60} color="#ccc" />
        <Text style={styles.errorTitle}>Strain Not Found</Text>
        <Text style={styles.errorText}>The strain you're looking for doesn't exist or has been removed.</Text>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.push('/strains')}
        >
          <Text style={styles.backButtonText}>Back to Strains</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#66BB6A" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.hero}>
            {strain.imageUrl ? (
              <Image 
                source={{ uri: strain.imageUrl }}
                style={styles.heroImage}
                resizeMode="cover"
              />
            ) : (
              <Image 
                source={require('../../assets/images/seedling.jpg')}
                style={styles.heroImage}
                resizeMode="cover"
              />
            )}
            <View style={styles.heroContent}>
              <Text style={styles.heroTitle}>{strain.name}</Text>
              {strain.Type && (
                <View style={styles.typeBadge}>
                  <Text style={styles.typeText}>{formatType(strain.Type)}</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.detailsContainer}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text style={styles.sectionText}>{strain.description || 'No description available.'}</Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Effects</Text>
              <Text style={styles.sectionText}>{formatData(strain.effects)}</Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Flavor</Text>
              <Text style={styles.sectionText}>{formatData(strain.flavor)}</Text>
            </View>

            {strain.Rating && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Rating</Text>
                <View style={styles.ratingContainer}>
                  <Ionicons name="star" size={20} color="#FFC107" />
                  <Text style={styles.ratingText}>{formatData(strain.Rating)}</Text>
                </View>
              </View>
            )}
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={handleBack}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
              <Text style={styles.backButtonText}>Back to Strains</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
        
        <Footer currentScreen="home" />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#66BB6A', // Lighter grass green
  },
  safeArea: {
    backgroundColor: '#66BB6A', // Lighter grass green
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  backButtonContainer: {
    padding: 16,
    backgroundColor: '#fff',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: '#66BB6A',
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    fontSize: 16,
    color: '#66BB6A',
    marginTop: 10,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  hero: {
    height: 250,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  typeBadge: {
    backgroundColor: '#66BB6A',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  typeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  detailsContainer: {
    padding: 16,
    paddingTop: 24,
  },
  section: {
    marginBottom: 20,
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#A5D6A7',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#43A047',
    marginBottom: 8,
  },
  sectionText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
    marginTop: 0,
  },
  editButton: {
    backgroundColor: '#66BB6A',
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
}); 