import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Image, TextInput, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';

interface Plant {
  id: number;
  name: string;
  dateAdded: string;
  lastWatered: string;
  light: string;
  environment: string;
  stage: string;
  lastFeeding: string;
  history?: { 
    id: string;
    type: 'watering' | 'feeding' | 'photo' | 'stage' | 'log';
    date: string;
    description: string;
    photoUrl?: string;
    stage?: string;
  }[];
}

const STORAGE_KEY = '@plants_data';

export default function PhotoAssignScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [selectedPlant, setSelectedPlant] = useState<number | null>(null);
  const [note, setNote] = useState('');
  const [photoUri, setPhotoUri] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const params = useLocalSearchParams();
  
  // Check if we have a photo URI from params
  useEffect(() => {
    if (params.photoUri) {
      setPhotoUri(params.photoUri as string);
    } else {
      // If no photo URI provided, take a new photo
      takePhoto();
    }
  }, [params.photoUri]);
  
  // Load plants from storage
  useEffect(() => {
    loadPlants();
  }, []);
  
  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };
  
  const takePhoto = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant camera permissions to take photos.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });
      
      if (!result.canceled && result.assets[0].uri) {
        setPhotoUri(result.assets[0].uri);
      } else {
        // If user canceled, go back
        router.back();
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };
  
  const handleSave = async () => {
    if (!photoUri) {
      Alert.alert('Error', 'No photo selected');
      return;
    }
    
    if (!selectedPlant) {
      Alert.alert('Error', 'Please select a plant');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get the selected plant
      const plant = plants.find(p => p.id === selectedPlant);
      if (!plant) {
        throw new Error('Plant not found');
      }
      
      // Create a new history item
      const now = new Date();
      const newHistoryItem = {
        id: Date.now().toString(),
        type: 'photo' as const,
        date: now.toISOString(),
        description: note || 'Photo added',
        photoUrl: photoUri
      };
      
      // Update the plant's history
      const updatedPlant = {
        ...plant,
        history: [newHistoryItem, ...(plant.history || [])]
      };
      
      // Update plants in storage
      const updatedPlants = plants.map(p => 
        p.id === selectedPlant ? updatedPlant : p
      );
      
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPlants));
      
      // Save to media library
      await MediaLibrary.saveToLibraryAsync(photoUri);
      
      Alert.alert(
        'Success',
        'Photo added to plant history',
        [{ text: 'OK', onPress: () => router.push('/home') }]
      );
    } catch (error) {
      console.error('Error saving photo:', error);
      Alert.alert('Error', 'Failed to save photo. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleRetake = () => {
    takePhoto();
  };
  
  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.pageHeader}>
            <Text style={styles.pageTitle}>Assign Photo</Text>
            <Text style={styles.pageDescription}>Select a plant and add notes</Text>
          </View>
          
          {photoUri ? (
            <View style={styles.photoContainer}>
              <Image 
                source={{ uri: photoUri }}
                style={styles.photo}
                resizeMode="cover"
              />
              <TouchableOpacity 
                style={styles.retakeButton}
                onPress={handleRetake}
              >
                <Ionicons name="camera" size={20} color="#fff" />
                <Text style={styles.retakeButtonText}>Retake</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#4CAF50" />
              <Text style={styles.loadingText}>Loading camera...</Text>
            </View>
          )}
          
          <View style={styles.formContainer}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Select Plant</Text>
              <View style={styles.plantSelector}>
                {plants.length > 0 ? (
                  <ScrollView 
                    horizontal 
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.plantOptions}
                  >
                    {plants.map(plant => (
                      <TouchableOpacity
                        key={plant.id}
                        style={[
                          styles.plantOption,
                          selectedPlant === plant.id && styles.plantOptionSelected
                        ]}
                        onPress={() => setSelectedPlant(plant.id)}
                      >
                        <Text 
                          style={[
                            styles.plantOptionText,
                            selectedPlant === plant.id && styles.plantOptionTextSelected
                          ]}
                        >
                          {plant.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                ) : (
                  <Text style={styles.noPlantsText}>No plants available. Add a plant first.</Text>
                )}
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Notes (Optional)</Text>
              <TextInput
                style={styles.noteInput}
                placeholder="Add notes about this photo..."
                value={note}
                onChangeText={setNote}
                multiline
                numberOfLines={2}
              />
            </View>
            
            <TouchableOpacity
              style={[
                styles.saveButton,
                (!selectedPlant || !photoUri || isLoading) && styles.saveButtonDisabled
              ]}
              onPress={handleSave}
              disabled={!selectedPlant || !photoUri || isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="save" size={20} color="#fff" />
                  <Text style={styles.saveButtonText}>Save Photo</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
        <Footer currentScreen="home" />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  pageHeader: {
    padding: 20,
    backgroundColor: '#fff',
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  pageDescription: {
    fontSize: 16,
    color: '#666',
  },
  photoContainer: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    height: 300,
  },
  photo: {
    width: '100%',
    height: '100%',
  },
  retakeButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  retakeButtonText: {
    color: '#fff',
    marginLeft: 8,
    fontWeight: '500',
  },
  loadingContainer: {
    margin: 20,
    marginTop: 10,
    height: 300,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
  },
  formContainer: {
    padding: 20,
    paddingBottom: 10,
  },
  formGroup: {
    marginBottom: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  plantSelector: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 8,
    minHeight: 50,
  },
  plantOptions: {
    paddingVertical: 8,
  },
  plantOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  plantOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  plantOptionText: {
    color: '#333',
  },
  plantOptionTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  noPlantsText: {
    color: '#666',
    textAlign: 'center',
    padding: 16,
  },
  noteInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    marginTop: 10,
  },
  saveButtonDisabled: {
    backgroundColor: '#9E9E9E',
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
}); 