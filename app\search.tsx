import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, TextInput, KeyboardAvoidingView, Platform, ActivityIndicator, Keyboard, Dimensions } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter } from 'expo-router';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { GEMINI_API_KEY } from '@env';

interface Plant {
  id: string;
  name: string;
  lastWatered: string | null;
  lastFeeding: string | null;
  environment: string;
}

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

const STORAGE_KEY = '@plants_data';

export default function SearchScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [userInput, setUserInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const chatScrollViewRef = useRef<ScrollView>(null);
  const inputRef = useRef<TextInput>(null);
  const router = useRouter();

  // Load plants from storage when component mounts
  useEffect(() => {
    loadPlants();
    // Initialize chat with a welcome message
    setChatMessages([
      {
        role: 'assistant',
        content: 'Hello! I\'m Seymore, your plant care assistant. How can I help you today?'
      }
    ]);
  }, []);

  // Scroll to bottom of chat when new messages arrive
  useEffect(() => {
    if (chatScrollViewRef.current) {
      setTimeout(() => {
        chatScrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [chatMessages]);

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const sendMessage = async () => {
    if (!userInput.trim() || isSending) return;
    
    // Dismiss keyboard on iOS
    if (Platform.OS === 'ios') {
      Keyboard.dismiss();
      inputRef.current?.blur();
    }
    
    // Add user message to chat
    const userMessage = { role: 'user' as const, content: userInput };
    setChatMessages(prev => [...prev, userMessage]);
    setUserInput('');
    setIsSending(true);
    
    try {
      // Initialize the Gemini API
      const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      
      // Create the prompt for the chat
      const prompt = `You are Seymore, a helpful plant care assistant. The user has asked: "${userInput}". 
      Please provide a helpful response about plant care, diseases, pests, or general plant maintenance.
      Keep your response concise, friendly, and focused on practical advice.`;
      
      // Generate content with Gemini
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Add assistant response to chat
      setChatMessages(prev => [...prev, { role: 'assistant', content: text }]);
      setIsSending(false);
    } catch (error) {
      console.error('Error sending message:', error);
      setChatMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'Sorry, there was an error processing your message. Please try again.' 
      }]);
      setIsSending(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <View style={styles.mainContent}>
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity 
            style={styles.actionButton}
            activeOpacity={0.7}
            onPress={() => router.push('/diagnose')}
          >
            <View style={styles.actionButtonIcon}>
              <Ionicons name="book-outline" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.actionButtonText}>Guide</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionButton}
            activeOpacity={0.7}
            onPress={() => router.push('/diagnose')}
          >
            <View style={styles.actionButtonIcon}>
              <Ionicons name="camera" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.actionButtonText}>Scan Plant</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionButton}
            activeOpacity={0.7}
          >
            <View style={styles.actionButtonIcon}>
              <Ionicons name="search" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.actionButtonText}>Ask Seymore</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionButton}
            activeOpacity={0.7}
            onPress={() => router.push('/strains')}
          >
            <View style={styles.actionButtonIcon}>
              <Ionicons name="leaf" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.actionButtonText}>Strains</Text>
          </TouchableOpacity>
        </View>

        <KeyboardAvoidingView 
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
          <View style={styles.chatContainer}>
            <ScrollView 
              ref={chatScrollViewRef}
              style={styles.chatMessagesContainer}
              contentContainerStyle={styles.chatMessagesContent}
            >
              {chatMessages.map((message, index) => (
                <View 
                  key={index} 
                  style={[
                    styles.messageContainer,
                    message.role === 'user' ? styles.userMessage : styles.assistantMessage
                  ]}
                >
                  <View style={styles.messageIcon}>
                    <Ionicons 
                      name={message.role === 'user' ? 'person' : 'leaf'} 
                      size={20} 
                      color="#4CAF50" 
                    />
                  </View>
                  <View style={styles.messageContent}>
                    <Text style={styles.messageText}>{message.content}</Text>
                  </View>
                </View>
              ))}
              {isSending && (
                <View style={styles.loadingMessage}>
                  <ActivityIndicator size="small" color="#4CAF50" />
                  <Text style={styles.loadingMessageText}>Thinking...</Text>
                </View>
              )}
            </ScrollView>

            <View style={styles.inputContainer}>
              <TextInput
                ref={inputRef}
                style={styles.input}
                placeholder="Ask about plant care..."
                value={userInput}
                onChangeText={setUserInput}
                multiline
                maxLength={500}
                onSubmitEditing={() => {
                  if (Platform.OS === 'ios') {
                    Keyboard.dismiss();
                    inputRef.current?.blur();
                  }
                  sendMessage();
                }}
                returnKeyType="send"
                blurOnSubmit={true}
                onKeyPress={(e: any) => {
                  if (Platform.OS === 'web' && e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                  }
                }}
              />
              <TouchableOpacity 
                style={styles.sendButton}
                onPress={() => {
                  if (Platform.OS === 'ios') {
                    Keyboard.dismiss();
                    inputRef.current?.blur();
                  }
                  sendMessage();
                }}
                disabled={!userInput.trim() || isSending}
              >
                <Ionicons 
                  name="send" 
                  size={20} 
                  color={!userInput.trim() || isSending ? '#ccc' : '#fff'} 
                />
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </View>
      <Footer currentScreen="home" plantCount={plants.length} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    marginHorizontal: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
    gap: 4,
  },
  actionButtonIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  actionButtonText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  chatContainer: {
    flex: 1,
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    margin: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#C8E6C9',
    marginBottom: 100,
  },
  chatMessagesContainer: {
    flex: 1,
  },
  chatMessagesContent: {
    padding: 16,
    paddingBottom: 8,
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
    marginBottom: 12,
    flexDirection: 'row' as const,
    alignItems: 'flex-start',
    gap: 8,
  },
  messageIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E8F5E9',
  },
  messageContent: {
    flex: 1,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E8F5E9',
    borderBottomRightRadius: 4,
  },
  assistantMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E8F5E9',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  loadingMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    marginBottom: 12,
  },
  loadingMessageText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E8F5E9',
    paddingTop: 8,
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: '#fff',
  },
  input: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: '#E8F5E9',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
}); 