import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Platform, Alert, Image, Modal, TextInput, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import React from 'react';
import { format, isToday, isTomorrow } from 'date-fns';
import DateTimePicker from '@react-native-community/datetimepicker';

interface Task {
  id: number;
  title: string;
  description: string;
  time: string;
  type: 'watering' | 'feeding' | 'schedule' | 'pruning' | 'custom';
  plantName: string;
  frequency?: string;
  amount?: string;
  isRecurring: boolean;
}

const NOTIFICATIONS_STORAGE_KEY = '@notifications_data';

export default function TasksScreen() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showTaskDetailsModal, setShowTaskDetailsModal] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState<{
    title: string;
    message: string;
    emoji: string;
  }>({
    title: '',
    message: '',
    emoji: ''
  });
  const [plants, setPlants] = useState([]);
  const router = useRouter();
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);
  const [selectedPlant, setSelectedPlant] = useState('');
  const [selectedTaskType, setSelectedTaskType] = useState<{
    type: string;
    title: string;
    description: string;
    emoji: string;
  } | null>(null);
  const [selectedTime, setSelectedTime] = useState(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(7, 0, 0, 0);
    return tomorrow;
  });
  const [frequency, setFrequency] = useState('daily');
  const [isRecurring, setIsRecurring] = useState(true);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showFrequencyDropdown, setShowFrequencyDropdown] = useState(false);
  const [showWhenDropdown, setShowWhenDropdown] = useState(false);
  const [whenOption, setWhenOption] = useState('tomorrow');
  const [customTaskDescription, setCustomTaskDescription] = useState('');

  // Load tasks when component mounts
  useEffect(() => {
    loadTasks();
    loadPlants();
  }, []);

  // Load tasks when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadTasks();
      loadPlants();
    }, [])
  );

  const handleTaskPress = async (taskId: number) => {
    try {
      const task = tasks.find(t => t.id === taskId);
      if (!task) return;

      setSelectedTask(task);
      setShowTaskDetailsModal(true);
    } catch (error) {
      console.error('Error handling task press:', error);
    }
  };

  const handleCompleteTask = async () => {
    if (!selectedTask) return;

    try {
      // Get current tasks
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      let tasks: Task[] = [];
      if (storedData) {
        tasks = JSON.parse(storedData);
      }

      // Remove the completed task
      const updatedTasks = tasks.filter(t => t.id !== selectedTask.id);

      // If the task is recurring, create the next occurrence
      if (selectedTask.isRecurring) {
        const currentDate = new Date(selectedTask.time);
        const nextDate = new Date(currentDate);
        
        // Calculate next date based on frequency
        if (selectedTask.frequency) {
          const [value, unit] = selectedTask.frequency.split(' ');
          if (unit === 'day' || unit === 'days') {
            nextDate.setDate(nextDate.getDate() + parseInt(value));
          } else if (unit === 'week' || unit === 'weeks') {
            nextDate.setDate(nextDate.getDate() + (parseInt(value) * 7));
          } else if (unit === 'month' || unit === 'months') {
            nextDate.setMonth(nextDate.getMonth() + parseInt(value));
          }
        } else {
          // Default to daily if no frequency specified
          nextDate.setDate(nextDate.getDate() + 1);
        }

        // Check for duplicate tasks
        const isDuplicate = updatedTasks.some(task => 
          task.plantName === selectedTask.plantName &&
          task.type === selectedTask.type &&
          task.title === selectedTask.title &&
          task.frequency === selectedTask.frequency &&
          task.isRecurring === selectedTask.isRecurring &&
          new Date(task.time).toDateString() === nextDate.toDateString()
        );

        // Only create the next task if no duplicate exists
        if (!isDuplicate) {
          const nextTask: Task = {
            id: Date.now(),
            title: selectedTask.title,
            description: selectedTask.description,
            time: nextDate.toISOString(),
            type: selectedTask.type,
            plantName: selectedTask.plantName,
            frequency: selectedTask.frequency,
            isRecurring: selectedTask.isRecurring
          };
          updatedTasks.push(nextTask);
        }
      }

      // Save the updated tasks
      await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(updatedTasks));
      setTasks(updatedTasks);
      setShowTaskDetailsModal(false);
      
      // Show success message
      setSuccessMessage({
        title: 'Success!',
        message: 'Task completed successfully',
        emoji: '✅'
      });
      setShowSuccessAlert(true);
      setTimeout(() => {
        setShowSuccessAlert(false);
      }, 3000);
    } catch (error) {
      console.error('Error completing task:', error);
      Alert.alert('Error', 'Failed to complete task. Please try again.');
    }
  };

  const handleEndTask = async () => {
    if (!selectedTask) return;

    try {
      // Get current tasks
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      let tasks: Task[] = [];
      if (storedData) {
        tasks = JSON.parse(storedData);
      }

      // Remove the task
      const updatedTasks = tasks.filter(t => t.id !== selectedTask.id);
      await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(updatedTasks));
      setTasks(updatedTasks);
      setShowTaskDetailsModal(false);
      
      // Show success message
      setSuccessMessage({
        title: 'Success!',
        message: 'Task ended successfully',
        emoji: '✅'
      });
      setShowSuccessAlert(true);
      setTimeout(() => {
        setShowSuccessAlert(false);
      }, 3000);
    } catch (error) {
      console.error('Error ending task:', error);
      Alert.alert('Error', 'Failed to end task. Please try again.');
    }
  };

  const loadTasks = async () => {
    try {
      console.log('Loading tasks from AsyncStorage...');
      const storedData = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      console.log('Stored tasks data:', storedData);
      if (storedData) {
        const data = JSON.parse(storedData);
        if (Array.isArray(data)) {
          console.log('Setting tasks:', data);
          setTasks(data);
        } else {
          console.log('Stored data is not an array:', data);
          setTasks([]);
        }
      } else {
        console.log('No tasks found in storage');
        setTasks([]);
      }
    } catch (error) {
      console.error('Error loading tasks:', error);
      Alert.alert('Error', 'Failed to load tasks. Please try again.');
      setTasks([]);
    }
  };

  const handleRemoveTask = async (id: number) => {
    try {
      const updatedTasks = tasks.filter(t => t.id !== id);
      setTasks(updatedTasks);
      await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(updatedTasks));
    } catch (error) {
      console.error('Error removing task:', error);
    }
  };

  const clearAllTasks = async () => {
    try {
      await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify([]));
      setTasks([]);
      setSuccessMessage({
        title: 'Success!',
        message: 'All tasks have been cleared',
        emoji: '✅'
      });
      setShowSuccessAlert(true);
      setTimeout(() => {
        setShowSuccessAlert(false);
      }, 3000);
    } catch (error) {
      console.error('Error clearing tasks:', error);
      Alert.alert('Error', 'Failed to clear tasks. Please try again.');
    }
  };

  const formatTime = (time: string, frequency?: string) => {
    const date = new Date(time);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return format(date, 'EEEE, MMMM d, yyyy');
    }
  };

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem('@plants_data');
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const resetTaskForm = () => {
    setSelectedTaskType(null);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(7, 0, 0, 0);
    setSelectedTime(tomorrow);
    setFrequency('daily');
    setIsRecurring(true);
    setSelectedPlant('');
    setWhenOption('tomorrow');
    setCustomTaskDescription('');
  };

  const isPredefinedTaskType = (type: string) => {
    return ['watering', 'feeding', 'pruning', 'check'].includes(type);
  };

  const getPredefinedTaskInfo = (type: string) => {
    switch (type) {
      case 'watering':
        return {
          title: 'Water Plant',
          description: 'Time to water your plant'
        };
      case 'feeding':
        return {
          title: 'Feed Plant',
          description: 'Time to feed your plant'
        };
      case 'pruning':
        return {
          title: 'Prune Plant',
          description: 'Time to prune your plant'
        };
      case 'check':
        return {
          title: 'Check Plant',
          description: 'Time to check on your plant'
        };
      default:
        return {
          title: '',
          description: ''
        };
    }
  };

  const handleCreateTask = () => {
    if (!selectedTaskType || !selectedPlant) return;

    // For predefined tasks, use the predefined info
    const taskInfo = isPredefinedTaskType(selectedTaskType.type) 
      ? getPredefinedTaskInfo(selectedTaskType.type)
      : {
          title: selectedTaskType.title,
          description: customTaskDescription || selectedTaskType.description
        };

    // Create a new task with the selected options
    const newTask: Task = {
      id: Date.now(),
      title: taskInfo.title,
      description: taskInfo.description,
      time: selectedTime.toISOString(),
      type: selectedTaskType.type as 'watering' | 'feeding' | 'schedule' | 'pruning' | 'custom',
      plantName: selectedPlant,
      frequency: frequency === 'never' ? undefined : frequency,
      isRecurring: frequency !== 'never'
    };
    
    // Save the new task
    AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY)
      .then(storedData => {
        let currentTasks: Task[] = [];
        if (storedData) {
          currentTasks = JSON.parse(storedData);
        }
        currentTasks.push(newTask);
        return AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(currentTasks));
      })
      .then(() => {
        setTasks(prevTasks => [...prevTasks, newTask]);
        setShowAddTaskModal(false);
        resetTaskForm();
        setSuccessMessage({
          title: 'Task Created',
          message: `New ${selectedTaskType.type} task created for ${selectedPlant}`,
          emoji: '✅'
        });
        setShowSuccessAlert(true);
        setTimeout(() => setShowSuccessAlert(false), 3000);
      })
      .catch(error => {
        console.error('Error saving task:', error);
        Alert.alert('Error', 'Failed to create task. Please try again.');
      });
  };

  // Function to dismiss keyboard
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#fff',
    },
    safeArea: {
      backgroundColor: '#2E7D32',
    },
    mainContent: {
      flex: 1,
      backgroundColor: '#fff',
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 24,
    },
    cardContainer: {
      padding: 16,
      gap: 16,
    },
    card: {
      backgroundColor: '#F1F8E9',
      borderRadius: 12,
      padding: 16,
      boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
      elevation: 3,
      borderWidth: 0,
    },
    cardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    cardTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#2E7D32',
      marginBottom: 4,
    },
    cardDescription: {
      fontSize: 14,
      color: '#4CAF50',
    },
    settingsCard: {
      backgroundColor: '#F1F8E9',
      borderRadius: 12,
      padding: 8,
      boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
      elevation: 3,
      borderWidth: 0,
      gap: 8,
      marginBottom: 16,
    },
    settingButton: {
      backgroundColor: '#fff',
      borderRadius: 8,
      padding: 12,
      boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
      elevation: 2,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    settingInfo: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#E8F5E9',
      alignItems: 'center',
      justifyContent: 'center',
    },
    settingText: {
      fontSize: 16,
      fontWeight: '500',
      color: '#2E7D32',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 4,
    },
    settingValueContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    settingValue: {
      fontSize: 16,
      color: '#666',
    },
    notificationTime: {
      fontSize: 14,
      color: '#4CAF50',
      marginTop: 2,
    },
    emptyStateContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: 24,
    },
    emptyStateIcon: {
      width: 64,
      height: 64,
      borderRadius: 32,
      backgroundColor: '#E8F5E9',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
    },
    emptyStateTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#2E7D32',
      marginBottom: 8,
    },
    emptyStateText: {
      fontSize: 14,
      color: '#666',
      textAlign: 'center',
      lineHeight: 20,
    },
    hero: {
      height: 200,
      width: '100%',
      overflow: 'hidden',
      backgroundColor: '#fff',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
    },
    heroImage: {
      width: '100%',
      height: '100%',
    },
    heroContent: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      padding: 20,
    },
    heroTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#fff',
      marginBottom: 8,
    },
    heroDescription: {
      fontSize: 14,
      color: '#fff',
      fontWeight: '500',
    },
    plantName: {
      fontSize: 14,
      color: '#2E7D32',
      fontWeight: '500',
      marginTop: 2,
    },
    settingEmoji: {
      fontSize: 24,
      textAlign: 'center',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    taskDetailsModalContent: {
      backgroundColor: '#fff',
      borderRadius: 16,
      padding: 24,
      width: '90%',
      maxWidth: 400,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#2E7D32',
    },
    modalCloseButton: {
      padding: 4,
    },
    taskDetailsContent: {
      gap: 16,
    },
    taskDetailRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: 12,
    },
    taskDetailIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#E8F5E9',
      alignItems: 'center',
      justifyContent: 'center',
    },
    taskDetailEmoji: {
      fontSize: 24,
    },
    taskDetailInfo: {
      flex: 1,
    },
    taskDetailTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: '#2E7D32',
      marginBottom: 4,
    },
    taskDetailDescription: {
      fontSize: 14,
      color: '#666',
      lineHeight: 20,
    },
    modalButtons: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      gap: 12,
      marginTop: 24,
    },
    modalButton: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
      minWidth: 100,
      alignItems: 'center',
    },
    completeButton: {
      backgroundColor: '#4CAF50',
    },
    completeButtonText: {
      color: '#fff',
      fontWeight: '600',
    },
    endButton: {
      backgroundColor: '#FF9800',
    },
    endButtonText: {
      color: '#fff',
      fontWeight: '600',
    },
    cancelButton: {
      backgroundColor: '#E0E0E0',
    },
    cancelButtonText: {
      color: '#666',
      fontWeight: '600',
    },
    alertOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    alertContent: {
      backgroundColor: '#fff',
      borderRadius: 16,
      padding: 24,
      width: '90%',
      maxWidth: 400,
      alignItems: 'center',
    },
    alertEmoji: {
      fontSize: 48,
      marginBottom: 16,
    },
    alertTitle: {
      fontSize: 24,
      fontWeight: '600',
      color: '#2E7D32',
      marginBottom: 8,
      textAlign: 'center',
    },
    alertMessage: {
      fontSize: 16,
      color: '#2E7D32',
      opacity: 0.9,
      textAlign: 'center',
    },
    clearButton: {
      backgroundColor: '#FF5252',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 8,
    },
    clearButtonText: {
      color: '#fff',
      fontWeight: '600',
      fontSize: 14,
    },
    taskDetailSection: {
      marginTop: 16,
    },
    taskDetailLabel: {
      fontSize: 14,
      color: '#666',
      marginBottom: 4,
    },
    taskDetailValue: {
      fontSize: 16,
      color: '#2E7D32',
      fontWeight: '500',
    },
    detailRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    detailLabel: {
      fontSize: 14,
      color: '#666',
    },
    detailValue: {
      fontSize: 16,
      color: '#2E7D32',
      fontWeight: '500',
    },
    addTaskButton: {
      backgroundColor: '#4CAF50',
      borderRadius: 8,
      padding: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addTaskButtonText: {
      color: '#fff',
      fontWeight: '600',
      fontSize: 14,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: '#fff',
      borderRadius: 16,
      padding: 24,
      width: '90%',
      maxWidth: 400,
    },
    modalScroll: {
      gap: 16,
      paddingRight: 10,
    },
    formGroup: {
      flexDirection: 'column',
      alignItems: 'flex-start',
      gap: 8,
      width: '100%',
    },
    label: {
      fontSize: 14,
      fontWeight: 'bold',
      color: '#2E7D32',
      marginBottom: 4,
    },
    plantSelector: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      width: '100%',
    },
    plantOption: {
      padding: 8,
      borderWidth: 1,
      borderColor: '#4CAF50',
      borderRadius: 8,
    },
    selectedPlantOption: {
      backgroundColor: '#4CAF50',
    },
    plantOptionText: {
      fontSize: 14,
      fontWeight: '500',
      color: '#2E7D32',
    },
    selectedPlantOptionText: {
      color: '#fff',
    },
    taskTypeGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      width: '100%',
    },
    taskTypeOption: {
      padding: 8,
      borderWidth: 1,
      borderColor: '#4CAF50',
      borderRadius: 8,
    },
    selectedTaskType: {
      backgroundColor: '#A5D6A7',
    },
    taskTypeEmoji: {
      fontSize: 24,
      marginBottom: 4,
    },
    taskTypeText: {
      fontSize: 14,
      fontWeight: '500',
      color: '#2E7D32',
    },
    dateSelector: {
      padding: 12,
      borderWidth: 1,
      borderColor: '#4CAF50',
      borderRadius: 8,
      width: '100%',
    },
    dateSelectorContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    dateText: {
      fontSize: 16,
      fontWeight: '500',
      color: '#2E7D32',
    },
    frequencySelector: {
      flexDirection: 'column',
      gap: 8,
      width: '100%',
    },
    frequencyOption: {
      borderWidth: 1,
      borderColor: '#4CAF50',
      borderRadius: 8,
      width: '100%',
    },
    frequencyOptionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
    },
    selectedFrequency: {
      backgroundColor: '#F1F8E9',
    },
    frequencyText: {
      fontSize: 16,
      fontWeight: '500',
      color: '#2E7D32',
    },
    selectedFrequencyText: {
      color: '#2E7D32',
    },
    modalFooter: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      gap: 12,
      marginTop: 24,
    },
    createButton: {
      backgroundColor: '#4CAF50',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
    },
    createButtonText: {
      color: '#fff',
      fontWeight: '600',
    },
    disabledButton: {
      backgroundColor: '#E0E0E0',
    },
    dropdownSelector: {
      borderWidth: 1,
      borderColor: '#4CAF50',
      borderRadius: 8,
      width: '100%',
      marginBottom: 4,
    },
    dropdownContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
    },
    dropdownText: {
      fontSize: 16,
      fontWeight: '500',
      color: '#2E7D32',
    },
    dropdownMenu: {
      borderWidth: 1,
      borderColor: '#4CAF50',
      borderRadius: 8,
      width: '100%',
      backgroundColor: '#fff',
      maxHeight: 200,
    },
    dropdownScrollView: {
      maxHeight: 200,
    },
    dropdownItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
      borderBottomWidth: 1,
      borderBottomColor: '#E8F5E9',
    },
    selectedDropdownItem: {
      backgroundColor: '#F1F8E9',
    },
    dropdownItemText: {
      fontSize: 16,
      color: '#2E7D32',
    },
    selectedDropdownItemText: {
      fontWeight: '500',
    },
    recurringText: {
      fontSize: 14,
      color: '#4CAF50',
      fontStyle: 'italic',
    },
    textInput: {
      borderWidth: 1,
      borderColor: '#4CAF50',
      borderRadius: 8,
      padding: 12,
      width: '100%',
      fontSize: 16,
      color: '#2E7D32',
      backgroundColor: '#fff',
      minHeight: 80,
      textAlignVertical: 'top',
    },
    taskDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 2,
      marginBottom: 2,
      fontStyle: 'italic',
    },
  });

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Tasks</Text>
            <Text style={styles.heroDescription}>Your plant care tasks</Text>
          </View>
        </View>
        <ScrollView 
          contentContainerStyle={[styles.scrollContainer, { paddingBottom: 80 }]}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            <TouchableOpacity 
              style={[styles.addTaskButton, { 
                alignSelf: 'flex-end', 
                flexDirection: 'row', 
                alignItems: 'center', 
                paddingHorizontal: 16, 
                paddingVertical: 8,
                marginBottom: 12
              }]}
              onPress={() => setShowAddTaskModal(true)}
            >
              <Ionicons name="add-circle-outline" size={20} color="#fff" style={{ marginRight: 8 }} />
              <Text style={[styles.addTaskButtonText, { fontSize: 15 }]}>Add Task</Text>
            </TouchableOpacity>

            <View style={styles.card}>
              <View style={styles.cardHeader}>
                <View>
                  <Text style={styles.cardTitle}>Current Tasks</Text>
                  <Text style={styles.cardDescription}>Your upcoming plant care tasks</Text>
                </View>
              </View>
            </View>

            <View style={[styles.settingsCard, { marginBottom: 24 }]}>
              {tasks.length === 0 ? (
                <View style={styles.emptyStateContainer}>
                  <View style={styles.emptyStateIcon}>
                    <Ionicons name="checkmark-circle-outline" size={48} color="#4CAF50" />
                  </View>
                  <Text style={styles.emptyStateTitle}>All Caught Up!</Text>
                  <Text style={styles.emptyStateText}>You have no upcoming plant care tasks</Text>
                </View>
              ) : (
                <View style={{ paddingBottom: 20 }}>
                  {tasks.map((task) => (
                    <TouchableOpacity 
                      key={task.id} 
                      style={styles.settingButton}
                      onPress={() => handleTaskPress(task.id)}
                    >
                      <View style={styles.settingItem}>
                        <View style={styles.settingInfo}>
                          <View style={styles.iconContainer}>
                            <Text style={styles.settingEmoji}>
                              {task.type === 'watering' ? '💧' :
                               task.type === 'feeding' ? '🌿' :
                               task.type === 'schedule' ? '📅' :
                               task.type === 'pruning' ? '✂️' : '📋'}
                            </Text>
                          </View>
                          <View>
                            <Text style={styles.settingText}>{task.title}</Text>
                            <Text style={styles.plantName}>Plant: {task.plantName}</Text>
                            {task.type === 'custom' && task.description && (
                              <Text style={styles.taskDescription}>{task.description}</Text>
                            )}
                            <Text style={styles.notificationTime}>
                              {new Date(task.time) > new Date() 
                                ? `Due ${formatTime(task.time, task.frequency)}`
                                : `Overdue (${formatTime(task.time, task.frequency)})`}
                            </Text>
                            {task.isRecurring && task.frequency && (
                              <Text style={styles.recurringText}>• Repeats {task.frequency}</Text>
                            )}
                          </View>
                        </View>
                        <View style={styles.settingValueContainer}>
                          <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="settings" plantCount={plants.length} />
      </SafeAreaView>
      
      {showTaskDetailsModal && selectedTask && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowTaskDetailsModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.taskDetailsModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Task Details</Text>
                <TouchableOpacity 
                  onPress={() => setShowTaskDetailsModal(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.taskDetailsContent}>
                <View style={styles.taskDetailRow}>
                  <View style={styles.taskDetailIcon}>
                    <Text style={styles.taskDetailEmoji}>
                      {selectedTask.type === 'watering' ? '💧' :
                       selectedTask.type === 'feeding' ? '🌿' :
                       selectedTask.type === 'schedule' ? '📅' :
                       selectedTask.type === 'pruning' ? '✂️' : '📋'}
                    </Text>
                  </View>
                  <View style={styles.taskDetailInfo}>
                    <Text style={styles.taskDetailTitle}>{selectedTask.title}</Text>
                    <Text style={styles.taskDetailDescription}>
                      {selectedTask.description || 'No description provided'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.taskDetailSection}>
                  <Text style={styles.taskDetailLabel}>Due Date</Text>
                  <Text style={styles.taskDetailValue}>
                    {(() => {
                      const dueDate = new Date(selectedTask.time);
                      if (isToday(dueDate)) {
                        return 'Today';
                      } else if (isTomorrow(dueDate)) {
                        return 'Tomorrow';
                      } else {
                        return format(dueDate, 'EEEE, MMMM d, yyyy');
                      }
                    })()}
                  </Text>
                </View>
                
                {selectedTask.isRecurring && (
                  <View style={styles.taskDetailSection}>
                    <Text style={styles.taskDetailLabel}>Recurring</Text>
                    <Text style={[styles.taskDetailValue, { color: '#2E7D32' }]}>
                      {selectedTask.frequency 
                        ? `Repeats every ${selectedTask.frequency.toLowerCase()}`
                        : 'Repeats daily'}
                    </Text>
                  </View>
                )}
                
                <View style={styles.taskDetailSection}>
                  <Text style={styles.taskDetailLabel}>Plant</Text>
                  <Text style={styles.taskDetailValue}>{selectedTask.plantName}</Text>
                </View>
                
                <View style={styles.taskDetailSection}>
                  <Text style={styles.taskDetailLabel}>Task Type</Text>
                  <Text style={styles.taskDetailValue}>
                    {selectedTask.type === 'watering' ? 'Watering' :
                     selectedTask.type === 'feeding' ? 'Feeding' :
                     selectedTask.type === 'schedule' ? 'Scheduled Task' :
                     selectedTask.type === 'pruning' ? 'Pruning' : 'Custom Task'}
                  </Text>
                </View>
              </View>
              
              <View style={styles.modalButtons}>
                {new Date(selectedTask.time).toDateString() === new Date().toDateString() ? (
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.completeButton]}
                    onPress={handleCompleteTask}
                  >
                    <Text style={styles.completeButtonText}>Complete Task</Text>
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.endButton]}
                    onPress={handleEndTask}
                  >
                    <Text style={styles.endButtonText}>End Task</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setShowTaskDetailsModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Close</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showSuccessAlert && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowSuccessAlert(false)}
        >
          <View style={styles.alertOverlay}>
            <View style={styles.alertContent}>
              <Text style={styles.alertEmoji}>{successMessage.emoji}</Text>
              <Text style={styles.alertTitle}>{successMessage.title}</Text>
              <Text style={styles.alertMessage}>{successMessage.message}</Text>
            </View>
          </View>
        </Modal>
      )}

      <Modal
        visible={showAddTaskModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddTaskModal(false)}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Create New Task</Text>
                <TouchableOpacity 
                  onPress={() => {
                    setShowAddTaskModal(false);
                    resetTaskForm();
                  }}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.modalScroll}>
                <View style={styles.formGroup}>
                  <Text style={styles.label}>Select Plant</Text>
                  <View style={styles.plantSelector}>
                    {plants.map((plant: any) => (
                      <TouchableOpacity
                        key={plant.id}
                        style={[
                          styles.plantOption,
                          selectedPlant === plant.name && styles.selectedPlantOption
                        ]}
                        onPress={() => setSelectedPlant(plant.name)}
                      >
                        <Text style={[
                          styles.plantOptionText,
                          selectedPlant === plant.name && styles.selectedPlantOptionText
                        ]}>
                          {plant.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={[styles.formGroup, { marginTop: 16 }]}>
                  <Text style={styles.label}>Task Type</Text>
                  <View style={styles.taskTypeGrid}>
                    {[
                      { type: 'watering', title: 'Water Plant', emoji: '💧', description: 'Time to water your plant' },
                      { type: 'feeding', title: 'Feed Plant', emoji: '🌿', description: 'Time to feed your plant' },
                      { type: 'pruning', title: 'Prune Plant', emoji: '✂️', description: 'Time to prune your plant' },
                      { type: 'check', title: 'Check Plant', emoji: '🔍', description: 'Time to check on your plant' },
                      { type: 'custom', title: 'Custom Task', emoji: '📋', description: 'Custom plant care task' }
                    ].map((task) => (
                      <TouchableOpacity
                        key={task.type}
                        style={[
                          styles.taskTypeOption,
                          selectedTaskType?.type === task.type && styles.selectedTaskType
                        ]}
                        onPress={() => setSelectedTaskType(task)}
                      >
                        <Text style={styles.taskTypeEmoji}>{task.emoji}</Text>
                        <Text style={styles.taskTypeText}>{task.title}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {selectedTaskType?.type === 'custom' && (
                  <View style={[styles.formGroup, { marginTop: 16 }]}>
                    <Text style={styles.label}>Task Description</Text>
                    <TextInput
                      style={styles.textInput}
                      placeholder="Enter task description"
                      value={customTaskDescription}
                      onChangeText={setCustomTaskDescription}
                      multiline={true}
                      numberOfLines={3}
                      returnKeyType="done"
                      onSubmitEditing={dismissKeyboard}
                      blurOnSubmit={true}
                    />
                  </View>
                )}

                <View style={[styles.formGroup, { marginTop: 16 }]}>
                  <Text style={styles.label}>When</Text>
                  <TouchableOpacity
                    style={styles.dropdownSelector}
                    onPress={() => setShowWhenDropdown(!showWhenDropdown)}
                  >
                    <View style={styles.dropdownContent}>
                      <Text style={styles.dropdownText}>
                        {whenOption === 'tomorrow' ? 'Tomorrow' :
                         whenOption === 'in 2 days' ? 'In 2 days' :
                         whenOption === 'in 3 days' ? 'In 3 days' :
                         whenOption === 'in 4 days' ? 'In 4 days' :
                         whenOption === 'in 5 days' ? 'In 5 days' :
                         whenOption === 'in 6 days' ? 'In 6 days' :
                         whenOption === 'in 1 week' ? 'In 1 week' :
                         whenOption === 'in 2 weeks' ? 'In 2 weeks' :
                         whenOption === 'in 3 weeks' ? 'In 3 weeks' :
                         whenOption === 'in 1 month' ? 'In 1 month' : 'Tomorrow'}
                      </Text>
                      <Ionicons 
                        name={showWhenDropdown ? "chevron-up" : "chevron-down"} 
                        size={24} 
                        color="#2E7D32" 
                      />
                    </View>
                  </TouchableOpacity>
                  
                  {showWhenDropdown && (
                    <View style={styles.dropdownMenu}>
                      <ScrollView style={styles.dropdownScrollView}>
                        {[
                          'tomorrow',
                          'in 2 days',
                          'in 3 days',
                          'in 4 days',
                          'in 5 days',
                          'in 6 days',
                          'in 1 week',
                          'in 2 weeks',
                          'in 3 weeks',
                          'in 1 month'
                        ].map((option) => (
                          <TouchableOpacity
                            key={option}
                            style={[
                              styles.dropdownItem,
                              whenOption === option && styles.selectedDropdownItem
                            ]}
                            onPress={() => {
                              setWhenOption(option);
                              setShowWhenDropdown(false);
                              
                              // Update the selectedTime based on the option
                              const newDate = new Date();
                              if (option === 'tomorrow') {
                                newDate.setDate(newDate.getDate() + 1);
                              } else if (option === 'in 2 days') {
                                newDate.setDate(newDate.getDate() + 2);
                              } else if (option === 'in 3 days') {
                                newDate.setDate(newDate.getDate() + 3);
                              } else if (option === 'in 4 days') {
                                newDate.setDate(newDate.getDate() + 4);
                              } else if (option === 'in 5 days') {
                                newDate.setDate(newDate.getDate() + 5);
                              } else if (option === 'in 6 days') {
                                newDate.setDate(newDate.getDate() + 6);
                              } else if (option === 'in 1 week') {
                                newDate.setDate(newDate.getDate() + 7);
                              } else if (option === 'in 2 weeks') {
                                newDate.setDate(newDate.getDate() + 14);
                              } else if (option === 'in 3 weeks') {
                                newDate.setDate(newDate.getDate() + 21);
                              } else if (option === 'in 1 month') {
                                newDate.setMonth(newDate.getMonth() + 1);
                              }
                              
                              // Set time to 7:00 AM
                              newDate.setHours(7, 0, 0, 0);
                              setSelectedTime(newDate);
                            }}
                          >
                            <Text style={[
                              styles.dropdownItemText,
                              whenOption === option && styles.selectedDropdownItemText
                            ]}>
                              {option.charAt(0).toUpperCase() + option.slice(1)}
                            </Text>
                            {whenOption === option && (
                              <Ionicons name="checkmark" size={20} color="#2E7D32" />
                            )}
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    </View>
                  )}
                </View>

                {showDatePicker && (
                  <DateTimePicker
                    value={selectedTime}
                    mode="datetime"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={(event, selectedDate) => {
                      setShowDatePicker(false);
                      if (selectedDate) {
                        setSelectedTime(selectedDate);
                      }
                    }}
                  />
                )}

                <View style={[styles.formGroup, { marginTop: 16 }]}>
                  <Text style={styles.label}>Repeat</Text>
                  <TouchableOpacity
                    style={styles.dropdownSelector}
                    onPress={() => setShowFrequencyDropdown(!showFrequencyDropdown)}
                  >
                    <View style={styles.dropdownContent}>
                      <Text style={styles.dropdownText}>
                        {frequency === 'never' ? 'Never' :
                         frequency === 'daily' ? 'Daily' :
                         frequency === 'every 2 days' ? 'Every 2 days' :
                         frequency === 'every 3 days' ? 'Every 3 days' :
                         frequency === 'every 4 days' ? 'Every 4 days' :
                         frequency === 'every 5 days' ? 'Every 5 days' :
                         frequency === 'every 6 days' ? 'Every 6 days' :
                         frequency === 'every week' ? 'Every week' :
                         frequency === 'every 2 weeks' ? 'Every 2 weeks' :
                         frequency === 'every 3 weeks' ? 'Every 3 weeks' :
                         frequency === 'every month' ? 'Every month' : 'Daily'}
                      </Text>
                      <Ionicons 
                        name={showFrequencyDropdown ? "chevron-up" : "chevron-down"} 
                        size={24} 
                        color="#2E7D32" 
                      />
                    </View>
                  </TouchableOpacity>
                  
                  {showFrequencyDropdown && (
                    <View style={styles.dropdownMenu}>
                      <ScrollView style={styles.dropdownScrollView}>
                        {[
                          'never',
                          'daily', 
                          'every 2 days', 
                          'every 3 days', 
                          'every 4 days', 
                          'every 5 days', 
                          'every 6 days', 
                          'every week', 
                          'every 2 weeks', 
                          'every 3 weeks', 
                          'every month'
                        ].map((freq) => (
                          <TouchableOpacity
                            key={freq}
                            style={[
                              styles.dropdownItem,
                              frequency === freq && styles.selectedDropdownItem
                            ]}
                            onPress={() => {
                              setFrequency(freq);
                              setShowFrequencyDropdown(false);
                            }}
                          >
                            <Text style={[
                              styles.dropdownItemText,
                              frequency === freq && styles.selectedDropdownItemText
                            ]}>
                              {freq.charAt(0).toUpperCase() + freq.slice(1)}
                            </Text>
                            {frequency === freq && (
                              <Ionicons name="checkmark" size={20} color="#2E7D32" />
                            )}
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    </View>
                  )}
                </View>
              </ScrollView>

              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={[styles.createButton, (!selectedTaskType || !selectedPlant) && styles.disabledButton]}
                  onPress={handleCreateTask}
                  disabled={!selectedTaskType || !selectedPlant}
                >
                  <Text style={styles.createButtonText}>Create Task</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
} 