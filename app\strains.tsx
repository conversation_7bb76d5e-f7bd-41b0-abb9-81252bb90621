import React, { useState, useEffect, useRef, useCallback } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, TextInput, ActivityIndicator, Platform, Linking, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter } from 'expo-router';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { GEMINI_API_KEY } from '@env';
import { db } from '../firebaseConfig';
import { collection, getDocs, query, where, orderBy, limit, startAfter } from 'firebase/firestore';

interface Strain {
  id: string;
  name: string;
  description: string;
  effects: string | string[];
  flavor: string | string[];
  thc: string;
  cbd: string;
  imageUrl?: string;
  Strain?: string;
  Description?: string;
  Effects?: string | string[];
  Flavor?: string | string[];
  THC?: string;
  CBD?: string;
  ImageUrl?: string;
  Rating?: number | string | string[];
  isActive?: boolean;
  createdAt?: number;
  updatedAt?: number;
  Type?: string | string[];
}

const STORAGE_KEY = '@strains_data';
const STRAINS_PER_PAGE = 20;

export default function StrainsScreen() {
  const [strains, setStrains] = useState<Strain[]>([]);
  const [filteredStrains, setFilteredStrains] = useState<Strain[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastVisible, setLastVisible] = useState<any>(null);
  const [hasMore, setHasMore] = useState(true);
  const [randomStrain, setRandomStrain] = useState<Strain | null>(null);
  const [showAllStrains, setShowAllStrains] = useState(false);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [shownStrainIds, setShownStrainIds] = useState<Set<string>>(new Set());
  const [allStrainIds, setAllStrainIds] = useState<string[]>([]);
  const [lastStrainStartsWithNumber, setLastStrainStartsWithNumber] = useState<boolean | null>(null);
  const [numberStrainIds, setNumberStrainIds] = useState<string[]>([]);
  const [letterStrainIds, setLetterStrainIds] = useState<string[]>([]);
  const [useTopOfArray, setUseTopOfArray] = useState<boolean | null>(null);
  const [randomStrainInitialized, setRandomStrainInitialized] = useState(false);
  const [typeLoadingComplete, setTypeLoadingComplete] = useState(false);
  const loadedTypesRef = useRef<Set<string>>(new Set());
  const isLoadingTypeRef = useRef<boolean>(false);
  const router = useRouter();

  // Create a ref for the ScrollView
  const scrollViewRef = useRef<ScrollView>(null);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Function to handle scroll events
  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const paddingToBottom = 20;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom;
    
    // Show back to top button when scrolled down
    setShowBackToTop(contentOffset.y > 300);
    
    if (isCloseToBottom && !isLoadingMore && hasMore) {
      handleLoadMore();
    }
  };

  // Function to scroll to top
  const scrollToTop = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ y: 0, animated: true });
    }
  };

  // Function to clear the cache and reload all strains
  const clearCacheAndReload = async () => {
    try {
      console.log("Clearing strain cache...");
      await AsyncStorage.removeItem(STORAGE_KEY);
      console.log("Cache cleared, reloading all strains from Firebase...");
      
      // Set loading state
      setIsLoading(true);
      
      // Get strains from Firebase
      const strainsCollection = collection(db, 'strains');
      console.log("Collection reference created:", strainsCollection ? "Yes" : "No");
      
      // Fetch ALL strains at once
      console.log("Fetching ALL strains from Firebase...");
      const allDocs = await getDocs(query(strainsCollection));
      console.log(`Fetched ${allDocs.size} strains from Firebase`);
      
      if (allDocs.size > 0) {
        const strainsData = allDocs.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            name: data.Strain || '',
            description: data.Description || '',
            effects: data.Effects || '',
            flavor: data.Flavor || '',
            thc: data.THC || '',
            cbd: data.CBD || '',
            imageUrl: data.ImageUrl || '',
            // Include original fields
            Strain: data.Strain,
            Description: data.Description,
            Effects: data.Effects,
            Flavor: data.Flavor,
            THC: data.THC,
            CBD: data.CBD,
            ImageUrl: data.ImageUrl,
            Rating: data.Rating,
            isActive: data.isActive,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
            Type: data.Type,
          };
        }) as Strain[];
        
        console.log(`Processed ${strainsData.length} strains from Firebase`);
        setStrains(strainsData);
        setFilteredStrains(strainsData);
        setHasMore(false);
        
        // Cache the data in AsyncStorage for offline access
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(strainsData));
        console.log(`Cached ${strainsData.length} strains in AsyncStorage`);
      } else {
        console.log("No strains found in Firebase");
        setStrains([]);
        setFilteredStrains([]);
      }
    } catch (error) {
      console.error("Error clearing cache and reloading strains:", error);
      // Fall back to regular loading process
      await loadStrains(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Load strains from Firebase when component mounts
  useEffect(() => {
    console.log("Component mounted, loading strains...");
    
    // Check if we have cached data
    const checkCacheAndLoad = async () => {
      try {
        const cachedData = await AsyncStorage.getItem(STORAGE_KEY);
        
        if (cachedData) {
          console.log("Using cached strains from AsyncStorage");
          const strainsData = JSON.parse(cachedData) as Strain[];
          console.log(`Loaded ${strainsData.length} strains from cache`);
          
          // Set all strains at once without pagination
          setStrains(strainsData);
          setFilteredStrains(strainsData);
          setHasMore(false);
          setIsLoading(false);
          
          // Start background refresh of strains from Firebase
          refreshStrainsInBackground();
        } else {
          console.log("No cached strains found, loading from Firebase...");
          // Clear cache and reload all strains from Firebase
          await clearCacheAndReload();
        }
      } catch (error) {
        console.error("Error checking cache:", error);
        // Fall back to regular loading process
        await loadStrains(true);
      }
    };
    
    checkCacheAndLoad();
  }, []);
  
  // Function to refresh strains in the background
  const refreshStrainsInBackground = async () => {
    try {
      console.log("Starting background refresh of strains...");
      
      // Get strains from Firebase
      const strainsCollection = collection(db, 'strains');
      
      // Fetch ALL strains at once
      const allDocs = await getDocs(query(strainsCollection));
      console.log(`Background refresh: Fetched ${allDocs.size} strains from Firebase`);
      
      if (allDocs.size > 0) {
        const strainsData = allDocs.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            name: data.Strain || '',
            description: data.Description || '',
            effects: data.Effects || '',
            flavor: data.Flavor || '',
            thc: data.THC || '',
            cbd: data.CBD || '',
            imageUrl: data.ImageUrl || '',
            // Include original fields
            Strain: data.Strain,
            Description: data.Description,
            Effects: data.Effects,
            Flavor: data.Flavor,
            THC: data.THC,
            CBD: data.CBD,
            ImageUrl: data.ImageUrl,
            Rating: data.Rating,
            isActive: data.isActive,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
            Type: data.Type,
          };
        }) as Strain[];
        
        console.log(`Background refresh: Processed ${strainsData.length} strains from Firebase`);
        
        // Update the strains state
        setStrains(strainsData);
        setFilteredStrains(strainsData);
        
        // Cache the data in AsyncStorage for offline access
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(strainsData));
        console.log(`Background refresh: Cached ${strainsData.length} strains in AsyncStorage`);
      }
    } catch (error) {
      console.error("Error in background refresh:", error);
    }
  };

  // Function to shuffle an array using a more robust algorithm
  const shuffleArray = <T,>(array: T[]): T[] => {
    // Create a copy of the array to avoid modifying the original
    const shuffled = [...array];
    
    // Use a more robust shuffling algorithm
    for (let i = shuffled.length - 1; i > 0; i--) {
      // Use a cryptographically secure random number if available
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    
    return shuffled;
  };

  // Function to check if a string starts with a number
  const startsWithNumber = (str: string): boolean => {
    return /^\d/.test(str);
  };

  // Function to categorize strains by whether they start with a number or letter
  const categorizeStrains = () => {
    const numberIds: string[] = [];
    const letterIds: string[] = [];
    
    strains.forEach(strain => {
      if (startsWithNumber(strain.name)) {
        numberIds.push(strain.id);
      } else {
        letterIds.push(strain.id);
      }
    });
    
    setNumberStrainIds(numberIds);
    setLetterStrainIds(letterIds);
  };

  // Function to get a truly random strain
  const getRandomStrain = useCallback(() => {
    if (strains.length === 0) return null;
    
    // If we don't have categorized strains yet, do that first
    if (numberStrainIds.length === 0 && letterStrainIds.length === 0) {
      categorizeStrains();
    }
    
    // Determine which category we want based on the last strain
    let targetIds: string[] = [];
    
    if (lastStrainStartsWithNumber === null) {
      // First time, randomly choose between number and letter
      const useNumber = Math.random() < 0.5;
      targetIds = useNumber ? numberStrainIds : letterStrainIds;
    } else if (lastStrainStartsWithNumber) {
      // Last strain started with a number, so we want a letter next
      targetIds = letterStrainIds;
    } else {
      // Last strain started with a letter, so we want a number next
      targetIds = numberStrainIds;
    }
    
    console.log(`Total strains: ${strains.length}`);
    console.log(`Number strains: ${numberStrainIds.length}`);
    console.log(`Letter strains: ${letterStrainIds.length}`);
    console.log(`Target category strains: ${targetIds.length}`);
    console.log(`Shown strains: ${shownStrainIds.size}`);
    
    // Filter out all previously shown strains
    let availableIds = targetIds.filter(id => !shownStrainIds.has(id));
    console.log(`Available strains in target category: ${availableIds.length}`);
    
    // If we don't have any available strains in our target category, try the other category
    if (availableIds.length === 0) {
      const otherCategoryIds = lastStrainStartsWithNumber ? letterStrainIds : numberStrainIds;
      availableIds = otherCategoryIds.filter(id => !shownStrainIds.has(id));
      console.log(`Available strains in other category: ${availableIds.length}`);
      
      // If we still don't have any available strains, reset the shown strains list
      if (availableIds.length === 0) {
        // Check if we've shown all strains
        if (shownStrainIds.size >= strains.length) {
          // Reset the shown strains list
          setShownStrainIds(new Set());
          availableIds = targetIds;
          console.log(`Reset shown strains. Available strains: ${availableIds.length}`);
        } else {
          // Use all strains that haven't been shown yet
          availableIds = allStrainIds.filter(id => !shownStrainIds.has(id));
          console.log(`Available strains from all strains: ${availableIds.length}`);
          
          // If we still don't have any strains, use all strains
          if (availableIds.length === 0) {
            availableIds = allStrainIds;
            console.log(`Using all strains: ${availableIds.length}`);
          }
        }
      }
    }
    
    // Determine whether to use the top or bottom of the array
    let useTop: boolean;
    
    if (useTopOfArray === null) {
      // First time, randomly choose between top and bottom
      useTop = Math.random() < 0.5;
      setUseTopOfArray(useTop);
    } else {
      // Alternate between top and bottom
      useTop = !useTopOfArray;
      setUseTopOfArray(useTop);
    }
    
    // Sort the available IDs to ensure consistent ordering
    const sortedIds = [...availableIds].sort();
    console.log(`Sorted available strains: ${sortedIds.length}`);
    
    // Select from the top or bottom of the array
    let selectedId: string;
    
    if (useTop) {
      // Select from the top (first 20%)
      const topIndex = Math.floor(Math.random() * Math.min(20, Math.ceil(sortedIds.length * 0.2)));
      selectedId = sortedIds[topIndex];
      console.log(`Selected from top at index ${topIndex} of ${sortedIds.length}`);
    } else {
      // Select from the bottom (last 20%)
      const bottomIndex = sortedIds.length - 1 - Math.floor(Math.random() * Math.min(20, Math.ceil(sortedIds.length * 0.2)));
      selectedId = sortedIds[bottomIndex];
      console.log(`Selected from bottom at index ${bottomIndex} of ${sortedIds.length}`);
    }
    
    // Log the selected strain name
    const selectedStrain = strains.find(strain => strain.id === selectedId);
    console.log(`Selected strain: ${selectedStrain?.name || 'Unknown'}`);
    
    return selectedId;
  }, [strains, numberStrainIds, letterStrainIds, categorizeStrains]);

  // Function to reload a random strain
  const reloadRandomStrain = useCallback(() => {
    if (strains.length > 0) {
      // Get a random strain ID
      const randomStrainId = getRandomStrain();
      
      if (randomStrainId) {
        // Find the strain with the selected ID
        const newRandomStrain = strains.find(strain => strain.id === randomStrainId);
        
        if (newRandomStrain) {
          // Update the random strain
          setRandomStrain(newRandomStrain);
          
          // Update whether the last strain started with a number
          setLastStrainStartsWithNumber(startsWithNumber(newRandomStrain.name));
          
          // Add the new strain ID to the shown strains set
          setShownStrainIds(prevSet => {
            const newSet = new Set(prevSet);
            newSet.add(randomStrainId);
            return newSet;
          });
          
          // Scroll to the top of the page to show the new random strain
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ y: 0, animated: true });
          }
        }
      }
    }
  }, [strains, getRandomStrain]);

  // Set a random strain when strains are loaded
  useEffect(() => {
    // Only set up the initial data structures, but don't automatically select a random strain
    if (strains.length > 0) {
      console.log("Setting up strain data structures...");
      // Create a list of all strain IDs
      const strainIds = strains.map(strain => strain.id);
      setAllStrainIds(strainIds);
      
      // Categorize strains by whether they start with a number or letter
      categorizeStrains();
    }
  }, [strains]); // Only depend on strains, not on randomStrain or randomStrainInitialized

  // Function to manually set a random strain
  const setInitialRandomStrain = () => {
    if (strains.length > 0 && !randomStrain) {
      console.log("Manually setting initial random strain...");
      
      // Get a random strain from all available strains
      const randomIndex = Math.floor(Math.random() * strains.length);
      const newRandomStrain = strains[randomIndex];
      
      console.log(`Selected random strain: ${newRandomStrain.name} at index ${randomIndex} of ${strains.length}`);
      
      // Update the random strain
      setRandomStrain(newRandomStrain);
      
      // Update whether the last strain started with a number
      setLastStrainStartsWithNumber(startsWithNumber(newRandomStrain.name));
      
      // Add the new strain ID to the shown strains set
      setShownStrainIds(new Set([newRandomStrain.id]));
    }
  };

  // Call the manual function once when the component mounts
  useEffect(() => {
    if (strains.length > 0 && !randomStrain) {
      setInitialRandomStrain();
    }
  }, [strains, randomStrain]); // Add dependencies to avoid linter errors

  // Memoize the loadAllStrainsForType function to prevent recreation on each render
  const loadAllStrainsForType = useCallback(async (type: string) => {
    try {
      // Set loading flag to prevent multiple loads
      if (isLoadingTypeRef.current) {
        console.log("Already loading strains, skipping...");
        return;
      }
      
      isLoadingTypeRef.current = true;
      setIsLoading(true);
      console.log(`Loading ${type} strains from current strains array`);
      
      // Use the current strains array instead of loading from storage again
      const typeFiltered = strains.filter(strain => {
        // Handle different possible formats of the Type field
        let strainTypes: string[] = [];
        
        if (Array.isArray(strain.Type)) {
          strainTypes = strain.Type.map(t => String(t).toLowerCase().trim());
        } else if (typeof strain.Type === 'string') {
          if (strain.Type.includes(',')) {
            strainTypes = strain.Type.split(',').map(t => t.toLowerCase().trim());
          } else {
            strainTypes = [strain.Type.toLowerCase().trim()];
          }
        }
        
        return strainTypes.some(t => t.includes(type.toLowerCase()));
      });
      
      console.log(`Found ${typeFiltered.length} strains of type ${type}`);
      
      // Randomize the filtered strains
      const randomizedFiltered = shuffleArray(typeFiltered);
      console.log(`Randomized ${typeFiltered.length} ${type} strains`);
      
      // Set filtered strains immediately
      setFilteredStrains(randomizedFiltered);
      setShowAllStrains(true);
      setHasMore(false);
      
    } catch (error) {
      console.error('Error filtering strains by type:', error);
    } finally {
      setIsLoading(false);
      isLoadingTypeRef.current = false;
    }
  }, [strains]);

  // Filter strains by type when selectedType changes
  useEffect(() => {
    if (selectedType) {
      // Always reload strains when a type is selected
      loadAllStrainsForType(selectedType);
    } else {
      setFilteredStrains(strains);
      
      // Reset the loaded types when no type is selected
      if (!selectedType) {
        loadedTypesRef.current.clear();
      }
    }
  }, [selectedType, strains, loadAllStrainsForType]);

  const loadStrains = async (isInitialLoad = true) => {
    // Skip loading more strains if a type is selected
    if (!isInitialLoad && selectedType) {
      console.log("Skipping pagination load because a type is selected");
      return;
    }
    
    try {
      if (isInitialLoad) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      
      // Check if we already have cached data
      const cachedData = await AsyncStorage.getItem(STORAGE_KEY);
      
      if (cachedData && isInitialLoad) {
        console.log("Using cached strains from AsyncStorage");
        const strainsData = JSON.parse(cachedData) as Strain[];
        console.log(`Loaded ${strainsData.length} strains from cache`);
        
        // Set all strains at once without pagination
        setStrains(strainsData);
        setFilteredStrains(strainsData);
        setHasMore(false);
        setIsLoading(false);
        return;
      }
      
      console.log("Loading strains from Firebase...");
      console.log("Firebase instance:", db ? "Available" : "Not available");
      
      // Get strains from Firebase
      const strainsCollection = collection(db, 'strains');
      console.log("Collection reference created:", strainsCollection ? "Yes" : "No");
      
      // For initial load, we want to get ALL strains at once
      // This is a special case to ensure we get all strains
      try {
        console.log("Fetching ALL strains from Firebase...");
        // Use a query without any limits to get all strains
        const allDocs = await getDocs(query(strainsCollection));
        console.log(`Fetched ${allDocs.size} strains from Firebase`);
        
        if (allDocs.size > 0) {
          const strainsData = allDocs.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              name: data.Strain || '',
              description: data.Description || '',
              effects: data.Effects || '',
              flavor: data.Flavor || '',
              thc: data.THC || '',
              cbd: data.CBD || '',
              imageUrl: data.ImageUrl || '',
              // Include original fields
              Strain: data.Strain,
              Description: data.Description,
              Effects: data.Effects,
              Flavor: data.Flavor,
              THC: data.THC,
              CBD: data.CBD,
              ImageUrl: data.ImageUrl,
              Rating: data.Rating,
              isActive: data.isActive,
              createdAt: data.createdAt,
              updatedAt: data.updatedAt,
              Type: data.Type,
            };
          }) as Strain[];
          
          console.log(`Processed ${strainsData.length} strains from Firebase`);
          setStrains(strainsData);
          setFilteredStrains(strainsData);
          setHasMore(false);
          
          // Cache the data in AsyncStorage for offline access
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(strainsData));
          console.log(`Cached ${strainsData.length} strains in AsyncStorage`);
          
          setIsLoading(false);
          return;
        }
      } catch (error) {
        console.error("Error fetching all strains:", error);
        // Continue with the regular loading process if this fails
      }
      
      // If we couldn't load all strains at once, try the regular loading process
      let strainsQuery;
      
      if (isInitialLoad) {
        // Initial load - get ALL strains at once without pagination
        console.log("Creating query to load ALL strains");
        // Use a query without any limits to get all strains
        strainsQuery = query(strainsCollection);
      } else {
        // Load more - start after the last visible document
        if (!lastVisible) {
          console.log("No more strains to load");
          setHasMore(false);
          return;
        }
        console.log("Creating pagination query with startAfter and limit:", STRAINS_PER_PAGE);
        // Use 'Strain' field instead of 'name' for ordering
        strainsQuery = query(strainsCollection, orderBy('Strain'), startAfter(lastVisible), limit(STRAINS_PER_PAGE));
      }
      
      console.log("Firebase query created, fetching documents...");
      console.log("Query parameters:", strainsQuery ? "Available" : "Not available");
      
      try {
        const querySnapshot = await getDocs(strainsQuery);
        console.log("Firebase query completed. Empty?", querySnapshot.empty);
        console.log("Number of documents:", querySnapshot.size);
        
        if (!querySnapshot.empty) {
          console.log("Processing strain documents...");
          const strainsData = querySnapshot.docs.map(doc => {
            const data = doc.data();
            console.log(`Strain ${doc.id}: ${data.Strain || 'Unnamed'}`);
            
            // Map Firebase fields to our Strain interface
            return {
              id: doc.id,
              name: data.Strain || '',
              description: data.Description || '',
              effects: data.Effects || '',
              flavor: data.Flavor || '',
              thc: data.THC || '',
              cbd: data.CBD || '',
              imageUrl: data.ImageUrl || '',
              // Include original fields
              Strain: data.Strain,
              Description: data.Description,
              Effects: data.Effects,
              Flavor: data.Flavor,
              THC: data.THC,
              CBD: data.CBD,
              ImageUrl: data.ImageUrl,
              Rating: data.Rating,
              isActive: data.isActive,
              createdAt: data.createdAt,
              updatedAt: data.updatedAt,
              Type: data.Type,
            };
          }) as Strain[];
          
          console.log("Strains loaded from Firebase:", strainsData.length);
          console.log("First strain:", strainsData.length > 0 ? strainsData[0].name : "None");
          
          // Update last visible document for pagination
          setLastVisible(querySnapshot.docs[querySnapshot.docs.length - 1]);
          
          // Check if we have more data to load
          setHasMore(querySnapshot.docs.length === STRAINS_PER_PAGE);
          
          if (isInitialLoad) {
            setStrains(strainsData);
            setFilteredStrains(strainsData);
            
            // Cache the data in AsyncStorage for offline access
            await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(strainsData));
            console.log("Strains cached in AsyncStorage");
          } else {
            // Append to existing strains
            setStrains(prevStrains => {
              const updatedStrains = [...prevStrains, ...strainsData];
              // No longer need to apply search filter
              setFilteredStrains(updatedStrains);
              return updatedStrains;
            });
            console.log("Appended strains to existing list");
          }
        } else {
          console.log("No strains found in Firebase");
          console.log("Checking if collection exists...");
          
          // Try to get a count of all documents in the collection
          const countQuery = query(strainsCollection);
          const countSnapshot = await getDocs(countQuery);
          console.log("Total documents in collection:", countSnapshot.size);
          
          if (countSnapshot.size > 0) {
            console.log("Collection has documents but query returned empty. Possible issue with query parameters.");
            console.log("First document in collection:", countSnapshot.docs[0].data());
            
            // Try a different approach - load all documents without ordering
            console.log("Trying to load all documents without ordering...");
            const allDocs = await getDocs(countQuery);
            console.log("Loaded all documents:", allDocs.size);
            
            if (allDocs.size > 0) {
              const strainsData = allDocs.docs.map(doc => {
                const data = doc.data();
                return {
                  id: doc.id,
                  name: data.Strain || '',
                  description: data.Description || '',
                  effects: data.Effects || '',
                  flavor: data.Flavor || '',
                  thc: data.THC || '',
                  cbd: data.CBD || '',
                  imageUrl: data.ImageUrl || '',
                  // Include original fields
                  Strain: data.Strain,
                  Description: data.Description,
                  Effects: data.Effects,
                  Flavor: data.Flavor,
                  THC: data.THC,
                  CBD: data.CBD,
                  ImageUrl: data.ImageUrl,
                  Rating: data.Rating,
                  isActive: data.isActive,
                  createdAt: data.createdAt,
                  updatedAt: data.updatedAt,
                  Type: data.Type,
                };
              }) as Strain[];
              
              console.log("Processed all strains:", strainsData.length);
              setStrains(strainsData);
              setFilteredStrains(strainsData);
              
              // Cache the data in AsyncStorage for offline access
              await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(strainsData));
              console.log("All strains cached in AsyncStorage");
            } else {
              setStrains([]);
              setFilteredStrains([]);
            }
          } else {
            setStrains([]);
            setFilteredStrains([]);
          }
        }
      } catch (queryError) {
        console.error("Error executing Firebase query:", queryError);
        console.error("Error details:", JSON.stringify(queryError));
      }
    } catch (error) {
      console.error('Error loading strains:', error);
      console.error("Error details:", JSON.stringify(error));
      setStrains([]);
      setFilteredStrains([]);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (!isLoadingMore && hasMore) {
      loadStrains(false);
    }
  };

  const handleStrainPress = (strain: Strain) => {
    // Navigate to strain details page with the full strain data
    router.push({
      pathname: '/strain/[id]',
      params: { 
        id: strain.id,
        name: strain.name,
        description: strain.description,
        effects: Array.isArray(strain.effects) ? strain.effects.join(',') : strain.effects,
        flavor: Array.isArray(strain.flavor) ? strain.flavor.join(',') : strain.flavor,
        thc: strain.thc,
        cbd: strain.cbd,
        imageUrl: strain.imageUrl || '',
        type: Array.isArray(strain.Type) ? strain.Type.join(',') : strain.Type || '',
        rating: Array.isArray(strain.Rating) ? strain.Rating.join(',') : strain.Rating || ''
      }
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView 
          ref={scrollViewRef}
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={400}
        >
          <View style={styles.hero}>
            <Image 
              source={require('../assets/images/seedling.jpg')}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <View style={styles.heroContent}>
              <Text style={styles.heroTitle}>Cannabis Strains</Text>
              <Text style={styles.heroDescription}>Explore different cannabis varieties</Text>
            </View>
          </View>

          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={() => router.push('/diagnose')}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="book-outline" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Guide</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={() => router.push('/diagnose')}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="camera" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Scan Plant</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={() => router.push('/search')}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="search" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Ask Seymore</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              activeOpacity={0.7}
              onPress={reloadRandomStrain}
            >
              <View style={styles.actionButtonIcon}>
                <Ionicons name="leaf" size={24} color="#4CAF50" />
              </View>
              <Text style={styles.actionButtonText}>Strains</Text>
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#4CAF50" />
              <Text style={styles.loadingText}>Loading strains...</Text>
            </View>
          ) : (
            <>
              {/* Debug info */}
              {(() => {
                console.log('Debug - randomStrain:', randomStrain, 'selectedType:', selectedType);
                return null;
              })()}
              
              {/* Force display of What's Dank section on web if we have strains but no random strain */}
              {Platform.OS === 'web' && strains.length > 0 && !randomStrain && !selectedType && (
                <View style={styles.whatsDankContainer}>
                  <Text style={styles.sectionTitle}>What's Dank ...</Text>
                  <Text style={styles.whatsDankSubtitle}>Featured Strain of the Day</Text>
                  
                  <TouchableOpacity 
                    style={styles.randomStrainCard}
                    onPress={() => {
                      // Select a random strain when clicked
                      const randomIndex = Math.floor(Math.random() * strains.length);
                      const webRandomStrain = strains[randomIndex];
                      handleStrainPress(webRandomStrain);
                    }}
                  >
                    <View style={styles.randomStrainHeader}>
                      <View style={styles.randomStrainIcon}>
                        <Ionicons name="leaf" size={32} color="#4CAF50" />
                      </View>
                      <View style={styles.randomStrainTitleContainer}>
                        <Text style={styles.randomStrainName}>Click to Discover a Random Strain</Text>
                        <View style={styles.randomStrainBadge}>
                          <Text style={styles.randomStrainBadgeText}>FEATURED</Text>
                        </View>
                      </View>
                    </View>
                    
                    <Text style={styles.randomStrainDescription}>
                      Explore our collection of cannabis strains. Click here to discover a random strain with unique effects and flavors.
                    </Text>
                    
                    <View style={styles.randomStrainFooter}>
                      <Text style={styles.randomStrainFooterText}>Click to discover a random strain</Text>
                      <Ionicons name="chevron-forward" size={20} color="#4CAF50" />
                    </View>
                  </TouchableOpacity>
                </View>
              )}
              
              {randomStrain && !selectedType && (
                <View style={styles.whatsDankContainer}>
                  <Text style={styles.sectionTitle}>What's Dank ...</Text>
                  <Text style={styles.whatsDankSubtitle}>Featured Strain of the Day</Text>
                  
                  <TouchableOpacity 
                    style={styles.randomStrainCard}
                    onPress={() => handleStrainPress(randomStrain)}
                  >
                    <View style={styles.randomStrainHeader}>
                      <View style={styles.randomStrainIcon}>
                        <Ionicons name="leaf" size={32} color="#4CAF50" />
                      </View>
                      <View style={styles.randomStrainTitleContainer}>
                        <Text style={styles.randomStrainName}>{randomStrain.name}</Text>
                        <View style={styles.randomStrainBadge}>
                          <Text style={styles.randomStrainBadgeText}>
                            {(Array.isArray(randomStrain.Type) 
                              ? randomStrain.Type[0] 
                              : typeof randomStrain.Type === 'string' && randomStrain.Type.includes(',')
                                ? randomStrain.Type.split(',')[0].trim()
                                : randomStrain.Type || 'Unknown').toUpperCase()}
                          </Text>
                        </View>
                      </View>
                    </View>
                    
                    <Text style={styles.randomStrainDescription}>
                      {randomStrain.description}
                    </Text>
                    
                    <View style={styles.randomStrainDetails}>
                      <View style={styles.randomStrainDetailItem}>
                        <Ionicons name="flame" size={16} color="#4CAF50" style={styles.detailIcon} />
                        <View style={styles.randomStrainDetailContent}>
                          <Text style={styles.strainDetailLabel}>Effects:</Text>
                          {Array.isArray(randomStrain.effects) 
                            ? randomStrain.effects.map((effect: string, index: number) => (
                                <Text key={index} style={styles.bulletPoint}>• {effect}</Text>
                              ))
                            : typeof randomStrain.effects === 'string' && randomStrain.effects.includes(',')
                              ? randomStrain.effects.split(',').map((effect: string, index: number) => (
                                  <Text key={index} style={styles.bulletPoint}>• {effect.trim()}</Text>
                                ))
                              : <Text style={styles.bulletPoint}>• {String(randomStrain.effects || 'N/A')}</Text>
                          }
                        </View>
                      </View>
                      <View style={styles.randomStrainDetailItem}>
                        <Ionicons name="nutrition" size={16} color="#4CAF50" style={styles.detailIcon} />
                        <View style={styles.randomStrainDetailContent}>
                          <Text style={styles.strainDetailLabel}>Flavor:</Text>
                          {Array.isArray(randomStrain.flavor)
                            ? randomStrain.flavor.map((flavor: string, index: number) => (
                                <Text key={index} style={styles.bulletPoint}>• {flavor}</Text>
                              ))
                            : typeof randomStrain.flavor === 'string' && randomStrain.flavor.includes(',')
                              ? randomStrain.flavor.split(',').map((flavor: string, index: number) => (
                                  <Text key={index} style={styles.bulletPoint}>• {flavor.trim()}</Text>
                                ))
                              : <Text style={styles.bulletPoint}>• {String(randomStrain.flavor || 'N/A')}</Text>
                          }
                        </View>
                      </View>
                      <View style={styles.randomStrainDetailItem}>
                        <Ionicons name="analytics" size={16} color="#4CAF50" style={styles.detailIcon} />
                        <View style={styles.randomStrainDetailContent}>
                          <Text style={styles.strainDetailLabel}>Type:</Text>
                          {Array.isArray(randomStrain.Type)
                            ? randomStrain.Type.map((type: string, index: number) => (
                                <Text key={index} style={styles.bulletPoint}>• {type}</Text>
                              ))
                            : typeof randomStrain.Type === 'string' && randomStrain.Type.includes(',')
                              ? randomStrain.Type.split(',').map((type: string, index: number) => (
                                  <Text key={index} style={styles.bulletPoint}>• {type.trim()}</Text>
                                ))
                              : <Text style={styles.bulletPoint}>• {String(randomStrain.Type || 'N/A')}</Text>
                          }
                        </View>
                      </View>
                      <View style={styles.randomStrainDetailItem}>
                        <Ionicons name="star" size={16} color="#4CAF50" style={styles.detailIcon} />
                        <View style={styles.randomStrainDetailContent}>
                          <Text style={styles.strainDetailLabel}>Rating:</Text>
                          {Array.isArray(randomStrain.Rating)
                            ? randomStrain.Rating.map((rating: string, index: number) => (
                                <Text key={index} style={styles.bulletPoint}>• {rating}/5</Text>
                              ))
                            : typeof randomStrain.Rating === 'string' && randomStrain.Rating.includes(',')
                              ? randomStrain.Rating.split(',').map((rating: string, index: number) => (
                                  <Text key={index} style={styles.bulletPoint}>• {rating.trim()}/5</Text>
                                ))
                              : <Text style={styles.bulletPoint}>• {randomStrain.Rating ? `${randomStrain.Rating}/5` : 'N/A'}</Text>
                          }
                        </View>
                      </View>
                    </View>
                    
                    <View style={styles.randomStrainFooter}>
                      <Text style={styles.randomStrainFooterText}>Tap to view details</Text>
                      <Ionicons name="chevron-forward" size={20} color="#4CAF50" />
                    </View>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.randomStrainButton}
                    onPress={() => {
                      // Scroll to top
                      if (scrollViewRef.current) {
                        scrollViewRef.current.scrollTo({ y: 0, animated: true });
                      }
                      // Reload random strain
                      reloadRandomStrain();
                    }}
                  >
                    <Text style={styles.randomStrainButtonText}>Show Random Strain</Text>
                    <Ionicons name="refresh" size={20} color="#fff" />
                  </TouchableOpacity>
                </View>
              )}

              {filteredStrains.length > 0 && (showAllStrains || selectedType) ? (
                <View style={styles.strainsContainer}>
                  <Text style={styles.sectionTitle}>
                    {selectedType 
                      ? `${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)} Strains` 
                      : 'All Strains'}
                  </Text>
                  
                  {filteredStrains.map((strain) => (
                    <TouchableOpacity 
                      key={strain.id}
                      style={styles.strainCard}
                      onPress={() => handleStrainPress(strain)}
                    >
                      <View style={styles.strainIcon}>
                        <Ionicons name="leaf" size={24} color="#4CAF50" />
                      </View>
                      <View style={styles.strainContent}>
                        <Text style={styles.strainName}>{strain.name}</Text>
                        <Text style={styles.strainType}>{strain.description}</Text>
                        <View style={styles.strainDetails}>
                          <View style={styles.strainDetailItem}>
                            <Ionicons name="flame" size={16} color="#4CAF50" style={styles.detailIcon} />
                            <View style={styles.strainDetailContent}>
                              <Text style={styles.strainDetailLabel}>Effects:</Text>
                              <Text style={{color: '#666', fontSize: 12}}>
                                {Array.isArray(strain.effects) 
                                  ? strain.effects.join(', ')
                                  : typeof strain.effects === 'string' && strain.effects.includes(',')
                                    ? strain.effects
                                    : String(strain.effects || 'N/A')}
                              </Text>
                            </View>
                          </View>
                          <View style={styles.strainDetailItem}>
                            <Ionicons name="nutrition" size={16} color="#4CAF50" style={styles.detailIcon} />
                            <View style={styles.strainDetailContent}>
                              <Text style={styles.strainDetailLabel}>Flavor:</Text>
                              <Text style={{color: '#666', fontSize: 12}}>
                                {Array.isArray(strain.flavor)
                                  ? strain.flavor.join(', ')
                                  : typeof strain.flavor === 'string' && strain.flavor.includes(',')
                                    ? strain.flavor
                                    : String(strain.flavor || 'N/A')}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#ccc" />
                    </TouchableOpacity>
                  ))}
                  
                  {isLoadingMore && (
                    <View style={styles.loadingMoreContainer}>
                      <ActivityIndicator size="small" color="#4CAF50" />
                      <Text style={styles.loadingMoreText}>Loading more strains...</Text>
                    </View>
                  )}
                  
                  {!isLoadingMore && hasMore && (
                    <TouchableOpacity 
                      style={styles.loadMoreButton}
                      onPress={handleLoadMore}
                    >
                      <Text style={styles.loadMoreButtonText}>Load More</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ) : null}
            </>
          )}
        </ScrollView>
        
        {/* Back to Top Button */}
        {showBackToTop && (
          <TouchableOpacity 
            style={styles.backToTopButton}
            onPress={scrollToTop}
            activeOpacity={0.8}
          >
            <Ionicons name="arrow-up" size={24} color="#fff" />
          </TouchableOpacity>
        )}
        
        <Footer currentScreen="home" />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32', // Grass green
  },
  safeArea: {
    backgroundColor: '#2E7D32', // Grass green
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
    gap: 4,
  },
  actionButtonIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  actionButtonText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#43A047', // Lighter green
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#66BB6A', // Lighter green
    marginTop: 10,
    textAlign: 'center',
  },
  strainsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  strainCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#C8E6C9', // Updated border color
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  strainIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  strainContent: {
    flex: 1,
  },
  strainName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32', // Grass green
    marginBottom: 4,
  },
  strainType: {
    fontSize: 14,
    color: '#2E7D32', // Grass green
    marginBottom: 8,
  },
  strainDetails: {
    marginTop: 8,
  },
  strainDetailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  strainDetailContent: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  strainDetailLabel: {
    fontSize: 14,
    color: '#2E7D32', // Grass green
    marginBottom: 4,
  },
  detailIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  randomStrainCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#C8E6C9', // Updated border color
    boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
    elevation: 5,
  },
  randomStrainHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  randomStrainIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E8F5E9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    borderWidth: 2,
    borderColor: '#66BB6A', // Lighter border
  },
  randomStrainTitleContainer: {
    flex: 1,
  },
  randomStrainName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2E7D32', // Grass green
    marginBottom: 6,
  },
  randomStrainBadge: {
    backgroundColor: '#2E7D32', // Grass green
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  randomStrainBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  randomStrainDescription: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
    marginBottom: 16,
  },
  randomStrainDetails: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  randomStrainDetailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  randomStrainDetailContent: {
    flex: 1,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    marginTop: 4,
    marginBottom: 4,
  },
  randomStrainFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#C8E6C9',
    paddingTop: 12,
  },
  randomStrainFooterText: {
    fontSize: 14,
    color: '#2E7D32', // Grass green
    fontWeight: '500',
  },
  whatsDankContainer: {
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 16,
  },
  whatsDankSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  backToTopButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    backgroundColor: 'rgba(129, 199, 132, 0.7)',
    padding: 12,
    borderRadius: 25,
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    cursor: 'pointer',
  },
  loadingMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingMoreText: {
    fontSize: 14,
    color: '#2E7D32', // Grass green
    marginLeft: 8,
  },
  loadMoreButton: {
    backgroundColor: '#E8F5E9',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 10,
    marginBottom: 20,
    alignSelf: 'center',
  },
  loadMoreButtonText: {
    color: '#2E7D32', // Grass green
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  randomStrainButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 16,
    alignSelf: 'center',
  },
  randomStrainButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
}); 