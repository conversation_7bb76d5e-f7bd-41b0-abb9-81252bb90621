import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Modal, Alert, Image } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { useRouter } from 'expo-router';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Picker } from '@react-native-picker/picker';

interface LightingSchedule {
  id: string;
  plantName: string;
  startTime: string;
  endTime: string;
  days: string[];
  description: string;
}

interface SunriseSunsetData {
  sunrise: string;
  sunset: string;
}

const STORAGE_KEY = '@lighting_schedules';

const getSunriseSunset = async (latitude: number, longitude: number): Promise<SunriseSunsetData> => {
  const today = new Date();
  const date = today.toISOString().split('T')[0];
  
  try {
    const response = await fetch(
      `https://api.sunrise-sunset.org/json?lat=${latitude}&lng=${longitude}&date=${date}&formatted=0`
    );
    const data = await response.json();
    
    if (data.status === 'OK') {
      const sunrise = new Date(data.results.sunrise);
      const sunset = new Date(data.results.sunset);
      
      // Convert to local time and format as HH:mm
      return {
        sunrise: sunrise.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }),
        sunset: sunset.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })
      };
    }
    throw new Error('Failed to fetch sunrise/sunset data');
  } catch (error) {
    console.error('Error fetching sunrise/sunset:', error);
    throw error;
  }
};

const sampleSchedules: LightingSchedule[] = [
  {
    id: '1',
    plantName: 'Natural Sunlight',
    startTime: '06:00',
    endTime: '20:00',
    days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    description: 'Natural outdoor sunlight schedule'
  },
  {
    id: '2',
    plantName: 'Seedling Stage',
    startTime: '06:00',
    endTime: '00:00',
    days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    description: 'Weeks 1-2: 18/6 schedule'
  },
  {
    id: '3',
    plantName: 'Vegetative Stage',
    startTime: '06:00',
    endTime: '00:00',
    days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    description: 'Weeks 2-8: 18/6 schedule'
  },
  {
    id: '4',
    plantName: 'Flowering Stage',
    startTime: '06:00',
    endTime: '18:00',
    days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    description: 'Weeks 8-16: 12/12 schedule'
  },
  {
    id: '5',
    plantName: 'Late Flowering',
    startTime: '06:00',
    endTime: '18:00',
    days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    description: 'Final 1-2 weeks: 12/12 schedule'
  }
];

export default function LightingScreen() {
  const [schedules, setSchedules] = useState<LightingSchedule[]>([]);
  const [selectedSchedule, setSelectedSchedule] = useState<LightingSchedule | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [plants, setPlants] = useState([]);
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView>(null);
  const [isEditTimeModalVisible, setIsEditTimeModalVisible] = useState(false);
  const [newStartTime, setNewStartTime] = useState('06:00');

  useEffect(() => {
    loadSchedules();
    getLocation();
    loadPlants();
  }, []);

  useEffect(() => {
    saveSchedules();
  }, [schedules]);

  const loadSchedules = async () => {
    try {
      // Try to load saved schedules from AsyncStorage
      const savedSchedules = await AsyncStorage.getItem(STORAGE_KEY);
      
      if (savedSchedules) {
        // If saved schedules exist, use them
        console.log('Loading saved schedules');
        setSchedules(JSON.parse(savedSchedules));
      } else {
        // If no saved schedules exist, use sample schedules
        console.log('No saved schedules found, using sample schedules');
        setSchedules(sampleSchedules);
        await saveSchedules();
      }
    } catch (error) {
      console.error('Error loading lighting schedules:', error);
      console.log('Setting sample schedules due to error');
      setSchedules(sampleSchedules);
    }
  };

  const saveSchedules = async () => {
    try {
      console.log('Saving schedules:', schedules);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(schedules));
    } catch (error) {
      console.error('Error saving lighting schedules:', error);
    }
  };

  const getLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Location permission is required to get accurate sunrise and sunset times.'
        );
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      setLocation(location);
      
      // Update natural sunlight schedule with actual times
      if (location) {
        const { sunrise, sunset } = await getSunriseSunset(
          location.coords.latitude,
          location.coords.longitude
        );
        
        setSchedules(prev => prev.map(schedule => 
          schedule.plantName === 'Natural Sunlight'
            ? { ...schedule, startTime: sunrise, endTime: sunset }
            : schedule
        ));
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert(
        'Location Error',
        'Unable to get your location. Using default sunrise/sunset times.'
      );
    }
  };

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem('@plants_data');
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const handleSchedulePress = (schedule: LightingSchedule) => {
    setSelectedSchedule(schedule);
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSelectedSchedule(null);
  };

  const handleAddSchedule = () => {
    setIsAddModalVisible(true);
  };

  const closeAddModal = () => {
    setIsAddModalVisible(false);
  };

  const handleEditStartTime = () => {
    setIsEditTimeModalVisible(true);
  };

  const handleTimeChange = (time: string) => {
    setNewStartTime(time);
  };

  const saveNewStartTime = () => {
    const [hours, minutes] = newStartTime.split(':').map(Number);
    setSchedules(prevSchedules => prevSchedules.map(schedule => {
      if (schedule.plantName === 'Natural Sunlight') {
        return schedule;
      }
      const duration = schedule.plantName.includes('Flowering') ? 12 : 18;
      const newEndTime = `${((hours + duration) % 24).toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      return { ...schedule, startTime: newStartTime, endTime: newEndTime };
    }));
    setIsEditTimeModalVisible(false);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Lighting Schedules</Text>
            <Text style={styles.heroDescription}>Manage your plant lighting schedules</Text>
          </View>
        </View>
        
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.cardContainer}>
            <View style={styles.card}>
              <TouchableOpacity style={styles.editButton} onPress={handleEditStartTime}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Ionicons name="time-outline" size={20} color="#fff" style={{ marginRight: 4 }} />
                  <Text style={styles.editButtonText}>Edit Start Time</Text>
                </View>
              </TouchableOpacity>
              {schedules.length === 0 ? (
                <View style={styles.emptyState}>
                  <Ionicons name="sunny-outline" size={48} color="#4CAF50" />
                  <Text style={styles.emptyStateText}>No lighting schedules yet</Text>
                  <Text style={styles.emptyStateDescription}>
                    Add your first lighting schedule to get started
                  </Text>
                  <TouchableOpacity 
                    style={styles.emptyStateButton}
                    onPress={handleAddSchedule}
                  >
                    <Text style={styles.emptyStateButtonText}>Add Schedule</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.scheduleList}>
                  {schedules.map((schedule) => (
                    <TouchableOpacity 
                      key={schedule.id} 
                      style={styles.scheduleItem}
                      onPress={() => handleSchedulePress(schedule)}
                    >
                      <View style={styles.scheduleIcon}>
                        <Ionicons 
                          name="sunny" 
                          size={24} 
                          color="#4CAF50" 
                        />
                      </View>
                      <View style={styles.scheduleInfo}>
                        <Text style={styles.scheduleName}>{schedule.plantName}</Text>
                        <Text style={styles.scheduleDescription}>{schedule.description}</Text>
                        <View style={styles.scheduleDetails}>
                          <Text style={styles.scheduleDetail}>
                            <Ionicons name="time-outline" size={16} color="#666" /> {schedule.startTime} - {schedule.endTime}
                          </Text>
                          <Text style={styles.scheduleDetail}>
                            <Ionicons name="calendar-outline" size={16} color="#666" /> {schedule.days.length} days
                          </Text>
                        </View>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        </ScrollView>
        <Footer currentScreen="home" plantCount={plants.length} />

        <Modal
          animationType="slide"
          transparent={true}
          visible={isModalVisible}
          onRequestClose={closeModal}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{selectedSchedule?.plantName}</Text>
                <TouchableOpacity onPress={closeModal}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              
              <ScrollView 
                style={styles.modalBody}
                ref={scrollViewRef}
              >
                <View style={styles.detailRow}>
                  <Ionicons name="time-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Schedule</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.description}</Text>
                  </View>
                </View>

                <View style={styles.detailRow}>
                  <Ionicons name="sunny-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Active Hours</Text>
                    <Text style={styles.detailValue}>
                      {selectedSchedule?.startTime} - {selectedSchedule?.endTime}
                    </Text>
                  </View>
                </View>

                <View style={styles.detailRow}>
                  <Ionicons name="calendar-outline" size={20} color="#4CAF50" />
                  <View style={styles.detailText}>
                    <Text style={styles.detailLabel}>Days</Text>
                    <Text style={styles.detailValue}>{selectedSchedule?.days.join(', ')}</Text>
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        </Modal>

        <Modal
          animationType="slide"
          transparent={true}
          visible={isEditTimeModalVisible}
          onRequestClose={() => setIsEditTimeModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Edit Start Time</Text>
                <TouchableOpacity onPress={() => setIsEditTimeModalVisible(false)}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              <Picker
                selectedValue={newStartTime}
                onValueChange={handleTimeChange}
                style={styles.picker}
              >
                {Array.from({ length: 24 }, (_, hour) => (
                  ['00', '15', '30', '45'].map(minute => (
                    <Picker.Item key={`${hour}:${minute}`} label={`${hour.toString().padStart(2, '0')}:${minute}`} value={`${hour.toString().padStart(2, '0')}:${minute}`} />
                  ))
                ))}
              </Picker>
              <TouchableOpacity style={styles.saveButton} onPress={saveNewStartTime}>
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 120,
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  emptyStateButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scheduleList: {
    gap: 12,
  },
  scheduleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  scheduleIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  scheduleInfo: {
    flex: 1,
  },
  scheduleName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 4,
  },
  scheduleDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  scheduleDetails: {
    flexDirection: 'row',
    gap: 12,
  },
  scheduleDetail: {
    fontSize: 12,
    color: '#666',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  modalBody: {
    gap: 20,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailText: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: '#9E9E9E',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#2E7D32',
    fontWeight: '500',
  },
  editButtonContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  editButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    width: 'auto',
    alignSelf: 'flex-end',
    marginBottom: 16,
  },
  editButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  picker: {
    height: 150,
    width: '100%',
  },
}); 