import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Link } from 'expo-router';
import Header from '../components/Header';
import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

export default function WelcomeScreen() {
  const [locationPermission, setLocationPermission] = useState<boolean | null>(null);
  const router = useRouter();

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
      } else {
        Alert.alert(
          'Location Permission Required',
          'This app needs location access to provide accurate sunrise and sunset times for your plants. Please enable location services in your device settings.',
          [
            {
              text: 'Open Settings',
              onPress: () => {
                // This will open the app settings where the user can enable location
                Location.requestForegroundPermissionsAsync();
              },
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
        setLocationPermission(false);
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setLocationPermission(false);
    }
  };

  const handleSettingsPress = () => {
    router.push('/settings');
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" backgroundColor="#ffffff" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header showMenu={false} />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <Image 
            source={require('../assets/images/logo.png')} 
            style={styles.logo}
            resizeMode="contain"
          />
          
          {locationPermission === true ? (
            <Link href="/home" asChild>
              <TouchableOpacity style={styles.enterButton}>
                <Text style={styles.enterButtonText}>Enter App</Text>
              </TouchableOpacity>
            </Link>
          ) : locationPermission === false ? (
            <TouchableOpacity 
              style={[styles.enterButton, styles.disabledButton]}
              onPress={requestLocationPermission}
            >
              <Text style={styles.enterButtonText}>Enable Location to Continue</Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Requesting location permission...</Text>
            </View>
          )}
          <Text style={styles.versionText}>Version 1.0.42</Text>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  safeArea: {
    backgroundColor: '#ffffff',
    paddingTop: 10,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 80,
  },
  logo: {
    width: '100%',
    height: 250,
    marginTop: 100,
  },
  enterButton: {
    backgroundColor: '#2E7D32',
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 8,
    marginTop: 40,
    alignSelf: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)',
    elevation: 5,
  },
  disabledButton: {
    backgroundColor: '#9E9E9E',
  },
  enterButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  versionText: {
    color: '#666',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 20,
  },
  loadingContainer: {
    marginTop: 40,
    alignItems: 'center',
  },
  loadingText: {
    color: '#666',
    fontSize: 16,
  },
}); 