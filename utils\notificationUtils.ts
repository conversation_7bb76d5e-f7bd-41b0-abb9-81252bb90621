import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Check if we're running in Expo Go
export const isExpoGo = Constants.appOwnership === 'expo';

// Check if push notifications are supported
export const isPushNotificationSupported = () => {
  if (Platform.OS === 'web') {
    return 'Notification' in window;
  }
  
  if (Platform.OS === 'android' && isExpoGo) {
    // Android push notifications are not supported in Expo Go SDK 53+
    return false;
  }
  
  // iOS push notifications still work in Expo Go
  // Development builds support push notifications on all platforms
  return true;
};

// Get notification permissions with proper error handling
export const getNotificationPermissions = async () => {
  try {
    const { status } = await Notifications.getPermissionsAsync();
    return status;
  } catch (error) {
    console.error('Error getting notification permissions:', error);
    return 'undetermined';
  }
};

// Request notification permissions with proper error handling
export const requestNotificationPermissions = async () => {
  try {
    const { status } = await Notifications.requestPermissionsAsync();
    return status;
  } catch (error) {
    console.error('Error requesting notification permissions:', error);
    return 'denied';
  }
};

// Schedule a local notification with error handling
export const scheduleLocalNotification = async (
  title: string,
  body: string,
  data?: any,
  trigger?: any
) => {
  try {
    const permissionStatus = await getNotificationPermissions();
    
    if (permissionStatus !== 'granted') {
      const newStatus = await requestNotificationPermissions();
      if (newStatus !== 'granted') {
        console.log('Notification permissions not granted');
        return null;
      }
    }

    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: data || {},
      },
      trigger: trigger || null, // null means send immediately
    });

    return notificationId;
  } catch (error) {
    console.error('Error scheduling notification:', error);
    return null;
  }
};

// Show a web notification (for web platform)
export const showWebNotification = (title: string, body: string, icon?: string) => {
  if (Platform.OS !== 'web' || !('Notification' in window)) {
    return false;
  }

  if (Notification.permission === 'granted') {
    new Notification(title, {
      body,
      icon: icon || '/favicon.ico',
    });
    return true;
  } else if (Notification.permission !== 'denied') {
    Notification.requestPermission().then((permission) => {
      if (permission === 'granted') {
        new Notification(title, {
          body,
          icon: icon || '/favicon.ico',
        });
      }
    });
  }
  
  return false;
};

// Get notification capability info for user display
export const getNotificationCapabilityInfo = () => {
  const pushSupported = isPushNotificationSupported();
  
  if (Platform.OS === 'web') {
    return {
      localNotifications: true,
      pushNotifications: 'Notification' in window,
      backgroundNotifications: false,
      message: 'Web notifications are supported in this browser.'
    };
  }
  
  if (Platform.OS === 'android' && isExpoGo) {
    return {
      localNotifications: true,
      pushNotifications: false,
      backgroundNotifications: false,
      message: 'Local notifications work in Expo Go. For push notifications, use a development build.'
    };
  }
  
  if (Platform.OS === 'ios' && isExpoGo) {
    return {
      localNotifications: true,
      pushNotifications: true,
      backgroundNotifications: true,
      message: 'All notification features are supported on iOS.'
    };
  }
  
  // Development build
  return {
    localNotifications: true,
    pushNotifications: true,
    backgroundNotifications: true,
    message: 'All notification features are supported in development builds.'
  };
};
