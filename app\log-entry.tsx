import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, TextInput, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter } from 'expo-router';

interface Plant {
  id: number;
  name: string;
  dateAdded: string;
  lastWatered: string;
  light: string;
  environment: string;
  stage: string;
  lastFeeding: string;
  history?: { 
    id: string;
    type: 'watering' | 'feeding' | 'photo' | 'stage' | 'log';
    date: string;
    description: string;
    photoUrl?: string;
    stage?: string;
  }[];
}

const STORAGE_KEY = '@plants_data';

export default function LogEntryScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [selectedPlant, setSelectedPlant] = useState<number | null>(null);
  const [logEntry, setLogEntry] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  
  // Load plants from storage
  useEffect(() => {
    loadPlants();
  }, []);
  
  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };
  
  const handleSave = async () => {
    if (!logEntry.trim()) {
      Alert.alert('Error', 'Please enter a log entry');
      return;
    }
    
    if (!selectedPlant) {
      Alert.alert('Error', 'Please select a plant');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get the selected plant
      const plant = plants.find(p => p.id === selectedPlant);
      if (!plant) {
        throw new Error('Plant not found');
      }
      
      // Create a new history item
      const now = new Date();
      const newHistoryItem = {
        id: Date.now().toString(),
        type: 'log' as const,
        date: now.toISOString(),
        description: logEntry
      };
      
      // Update the plant's history
      const updatedPlant = {
        ...plant,
        history: [newHistoryItem, ...(plant.history || [])]
      };
      
      // Update plants in storage
      const updatedPlants = plants.map(p => 
        p.id === selectedPlant ? updatedPlant : p
      );
      
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPlants));
      
      Alert.alert(
        'Success',
        'Log entry added to plant history',
        [{ text: 'OK', onPress: () => router.push('/home') }]
      );
    } catch (error) {
      console.error('Error saving log entry:', error);
      Alert.alert('Error', 'Failed to save log entry. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.pageHeader}>
            <Text style={styles.pageTitle}>Add Log Entry</Text>
            <Text style={styles.pageDescription}>Select a plant and add your log entry</Text>
          </View>
          
          <View style={styles.formContainer}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Select Plant</Text>
              <View style={styles.plantSelector}>
                {plants.length > 0 ? (
                  <ScrollView 
                    horizontal 
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.plantOptions}
                  >
                    {plants.map(plant => (
                      <TouchableOpacity
                        key={plant.id}
                        style={[
                          styles.plantOption,
                          selectedPlant === plant.id && styles.plantOptionSelected
                        ]}
                        onPress={() => setSelectedPlant(plant.id)}
                      >
                        <Text 
                          style={[
                            styles.plantOptionText,
                            selectedPlant === plant.id && styles.plantOptionTextSelected
                          ]}
                        >
                          {plant.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                ) : (
                  <Text style={styles.noPlantsText}>No plants available. Add a plant first.</Text>
                )}
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Log Entry</Text>
              <TextInput
                style={styles.logInput}
                placeholder="Enter your log entry here..."
                value={logEntry}
                onChangeText={setLogEntry}
                multiline
                numberOfLines={6}
                textAlignVertical="top"
              />
            </View>
            
            <TouchableOpacity
              style={[
                styles.saveButton,
                (!selectedPlant || !logEntry.trim() || isLoading) && styles.saveButtonDisabled
              ]}
              onPress={handleSave}
              disabled={!selectedPlant || !logEntry.trim() || isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="save" size={20} color="#fff" />
                  <Text style={styles.saveButtonText}>Save Log Entry</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
        <Footer currentScreen="home" />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32',
  },
  safeArea: {
    backgroundColor: '#2E7D32',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  pageHeader: {
    padding: 20,
    backgroundColor: '#fff',
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  pageDescription: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    padding: 20,
    paddingBottom: 10,
  },
  formGroup: {
    marginBottom: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  plantSelector: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 8,
    minHeight: 50,
  },
  plantOptions: {
    paddingVertical: 8,
  },
  plantOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  plantOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  plantOptionText: {
    color: '#333',
  },
  plantOptionTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  noPlantsText: {
    color: '#666',
    textAlign: 'center',
    padding: 16,
  },
  logInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    minHeight: 150,
    textAlignVertical: 'top',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    marginTop: 10,
  },
  saveButtonDisabled: {
    backgroundColor: '#9E9E9E',
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
}); 