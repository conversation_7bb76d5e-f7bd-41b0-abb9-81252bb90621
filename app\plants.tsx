import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, Alert, Pressable, Platform, Modal } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useRouter } from 'expo-router';
import PlantInputModal from '../components/PlantInputModal';

interface Plant {
  id: number;
  name: string;
  dateAdded: string; // ISO date string
  lastWatered: string;
  environment: string;
  lastFeeding?: string;
  stage: string;
  history: string[];
}

const STORAGE_KEY = '@plants_data';
const ENVIRONMENTS_KEY = '@environments';
const NOTIFICATIONS_STORAGE_KEY = '@notifications_data';

const samplePlants = [
  {
    name: 'Blue Dream',
    lastWatered: '2 days ago',
    environment: 'Indoor Tent',
    stage: 'Seedling Stage',
    lastFeeding: 'Not fed yet',
    history: []
  },
  {
    name: 'OG Kush',
    lastWatered: '1 day ago',
    environment: 'Indoor Tent',
    stage: 'Seedling Stage',
    lastFeeding: 'Not fed yet',
    history: []
  },
  {
    name: 'Girl Scout Cookies',
    lastWatered: '3 days ago',
    environment: 'Indoor Tent',
    stage: 'Seedling Stage',
    lastFeeding: 'Not fed yet',
    history: []
  },
  {
    name: 'Northern Lights',
    lastWatered: '4 days ago',
    environment: 'Indoor Tent',
    stage: 'Seedling Stage',
    lastFeeding: 'Not fed yet',
    history: []
  },
  {
    name: 'Sour Diesel',
    lastWatered: '1 day ago',
    environment: 'Indoor Tent',
    stage: 'Seedling Stage',
    lastFeeding: 'Not fed yet',
    history: []
  }
];

const defaultPlants: Plant[] = [
  {
    id: 1,
    name: 'Monstera Deliciosa',
    dateAdded: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(), // 6 months ago
    lastWatered: '2 days ago',
    environment: 'Indoor Tent',
    stage: 'Seedling Stage',
    lastFeeding: 'Not fed yet',
    history: []
  },
  {
    id: 2,
    name: 'Snake Plant',
    dateAdded: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year ago
    lastWatered: '5 days ago',
    environment: 'Outdoor Garden',
    stage: 'Seedling Stage',
    lastFeeding: 'Not fed yet',
    history: []
  }
];

// Helper function to calculate age
const calculateAge = (dateAdded: string): string => {
  const addedDate = new Date(dateAdded);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - addedDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 30) {
    return `${diffDays} days`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months !== 1 ? 's' : ''}`;
  } else {
    const years = Math.floor(diffDays / 365);
    return `${years} year${years !== 1 ? 's' : ''}`;
  }
};

// Helper function to calculate stage duration
const calculateStageDuration = (dateAdded: string): string => {
  const addedDate = new Date(dateAdded);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - addedDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} week${weeks !== 1 ? 's' : ''}`;
  } else {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months !== 1 ? 's' : ''}`;
  }
};

// Helper function to format time for human-readable display
const formatTime = (isoString: string) => {
  // Handle the case when the plant hasn't been watered/fed yet
  if (isoString === 'Not watered yet') {
    return 'I\'m Thirsty!';
  }
  if (isoString === 'Not fed yet') {
    return 'Feed me Seymore!';
  }
  
  const date = new Date(isoString);
  const now = new Date();
  
  // Set both dates to midnight to get accurate day difference
  const dateMidnight = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const nowMidnight = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const diffInDays = Math.floor((nowMidnight.getTime() - dateMidnight.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    return `Today, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric' })}`;
  } else if (diffInDays === 1) {
    return `Yesterday, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric' })}`;
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  }
};

const DeleteButton = ({ onPress }: { onPress: () => void }) => (
  <TouchableOpacity 
    onPress={() => {
      console.log('Delete button pressed');
      onPress();
    }}
    style={styles.deleteButtonContainer}
    activeOpacity={0.7}
    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
  >
    <Ionicons name="close-circle" size={24} color="#FF5252" />
  </TouchableOpacity>
);

export default function PlantsScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [environments, setEnvironments] = useState<string[]>([]);
  const [showRemoveAllModal, setShowRemoveAllModal] = useState(false);
  const [showAddPlantModal, setShowAddPlantModal] = useState(false);
  const router = useRouter();

  // Load plants and environments from storage when component mounts
  useEffect(() => {
    loadPlants();
    loadEnvironments();
  }, []);

  // Save plants to storage whenever they change
  useEffect(() => {
    savePlants();
  }, [plants]);

  const loadPlants = async () => {
    try {
      const storedPlants = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPlants) {
        setPlants(JSON.parse(storedPlants));
      } else {
        // If no stored plants, use default plants
        setPlants(defaultPlants);
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(defaultPlants));
      }
    } catch (error) {
      console.error('Error loading plants:', error);
      setPlants(defaultPlants);
    }
  };

  const savePlants = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(plants));
    } catch (error) {
      console.error('Error saving plants:', error);
    }
  };

  const loadEnvironments = async () => {
    try {
      const storedEnvironments = await AsyncStorage.getItem(ENVIRONMENTS_KEY);
      if (storedEnvironments) {
        const parsedEnvironments = JSON.parse(storedEnvironments);
        setEnvironments(parsedEnvironments.map((env: any) => env.name));
      } else {
        // Default environments if none stored
        const defaultEnvironments = ['Indoor Tent', 'Outdoor Garden'];
        setEnvironments(defaultEnvironments);
        await AsyncStorage.setItem(ENVIRONMENTS_KEY, JSON.stringify(defaultEnvironments.map(name => ({ name }))));
      }
    } catch (error) {
      console.error('Error loading environments:', error);
      setEnvironments(['Indoor Tent', 'Outdoor Garden']);
    }
  };

  const addNewPlant = (plantData: { name: string; environment: string; stage: string }) => {
    const newPlant: Plant = {
      id: Date.now(),
      dateAdded: new Date().toISOString(),
      name: plantData.name,
      lastWatered: 'Not watered yet',
      environment: plantData.environment,
      stage: plantData.stage,
      lastFeeding: 'Not fed yet',
      history: []
    };
    setPlants([newPlant, ...plants]);
    router.push(`/plant/${newPlant.id}`);
  };

  const removePlant = async (plantId: number) => {
    try {
      // Remove the plant
      const updatedPlants = plants.filter(plant => plant.id !== plantId);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPlants));
      setPlants(updatedPlants);

      // Remove notifications associated with this plant
      const storedNotifications = await AsyncStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      if (storedNotifications) {
        const notifications = JSON.parse(storedNotifications);
        const plant = plants.find(p => p.id === plantId);
        if (plant) {
          const updatedNotifications = notifications.filter(
            (notification: any) => notification.plantName !== plant.name
          );
          await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(updatedNotifications));
        }
      }

      console.log('Plant and its notifications removed successfully');
    } catch (error) {
      console.error('Error removing plant and notifications:', error);
      Alert.alert('Error', 'Failed to remove plant and notifications');
    }
  };

  const handleRemoveAllPlants = async () => {
    try {
      // Remove all plants
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify([]));
      setPlants([]);

      // Remove all notifications
      await AsyncStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify([]));
      
      console.log('All plants and their notifications removed successfully');
    } catch (error) {
      console.error('Error removing all plants and notifications:', error);
      Alert.alert('Error', 'Failed to remove all plants and notifications');
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#2E7D32" />
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        <Header />
      </SafeAreaView>
      <SafeAreaView style={styles.mainContent} edges={['left', 'right', 'bottom']}>
        <View style={styles.hero}>
          <Image 
            source={require('../assets/images/seedling.jpg')}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>My Plants</Text>
            <Text style={styles.heroDescription}>View and manage your plant collection</Text>
          </View>
        </View>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.addButtonContainer}>
            <TouchableOpacity style={styles.addButton} onPress={() => setShowAddPlantModal(true)}>
              <Ionicons name="add-circle" size={24} color="#2E7D32" />
              <Text style={styles.addButtonText}>Add New Plant</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.cardContainer}>
            {plants.length === 0 ? (
              <View style={styles.emptyStateContainer}>
                <View style={styles.emptyStateIcon}>
                  <Ionicons name="leaf-outline" size={48} color="#4CAF50" />
                </View>
                <Text style={styles.emptyStateTitle}>No Plants Yet</Text>
                <Text style={styles.emptyStateText}>Add your first plant to get started</Text>
              </View>
            ) : (
              <>
                {plants.map((plant) => (
                  <TouchableOpacity 
                    key={plant.id} 
                    style={styles.plantCard}
                    onPress={() => router.push(`/plant/${plant.id}`)}
                  >
                    <View style={styles.plantCardContent}>
                      <Image 
                        source={require('../assets/images/seedling.jpg')} 
                        style={styles.plantThumbnail}
                      />
                      <View style={styles.plantInfo}>
                        <View style={styles.plantHeader}>
                          <Text style={styles.plantName}>{plant.name}</Text>
                        </View>
                        <View style={styles.plantDetails}>
                          <View style={styles.detailRow}>
                            <Ionicons name="calendar" size={16} color="#4CAF50" />
                            <Text style={styles.detailText}>Age: {calculateAge(plant.dateAdded)}</Text>
                          </View>
                          <View style={styles.detailRow}>
                            <Ionicons name="leaf-outline" size={16} color="#4CAF50" />
                            <Text style={styles.detailText}>Stage: {plant.stage} • {calculateStageDuration(plant.dateAdded)}</Text>
                          </View>
                          <View style={styles.detailRow}>
                            <Ionicons name="home" size={16} color="#4CAF50" />
                            <Text style={styles.detailText}>Environment: {plant.environment}</Text>
                          </View>
                          <View style={styles.detailRow}>
                            <Ionicons name="water" size={16} color="#4CAF50" />
                            <Text style={styles.detailText}>Last watered: {formatTime(plant.lastWatered)}</Text>
                          </View>
                          <View style={styles.detailRow}>
                            <Ionicons name="leaf" size={16} color="#4CAF50" />
                            <Text style={styles.detailText}>Last fed: {plant.lastFeeding ? formatTime(plant.lastFeeding) : 'Not fed yet'}</Text>
                          </View>
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
                <TouchableOpacity 
                  style={styles.removeAllButton}
                  onPress={() => setShowRemoveAllModal(true)}
                >
                  <Ionicons name="trash-outline" size={20} color="#FF5252" />
                  <Text style={styles.removeAllText}>Remove All Plants</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </ScrollView>
        <Footer currentScreen="plants" plantCount={plants.length} />
      </SafeAreaView>

      <PlantInputModal
        visible={showAddPlantModal}
        onClose={() => setShowAddPlantModal(false)}
        onSubmit={addNewPlant}
        environments={environments}
      />

      {showRemoveAllModal && (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowRemoveAllModal(false)}
        >
          <View style={styles.alertOverlay}>
            <View style={[styles.alertContent, { backgroundColor: '#fff' }]}>
              <Text style={[styles.alertEmoji, { color: '#FF5252' }]}>⚠️</Text>
              <Text style={[styles.alertTitle, { color: '#333' }]}>Remove All Plants</Text>
              <Text style={[styles.alertMessage, { color: '#666' }]}>Are you sure you want to remove all plants? This action cannot be undone.</Text>
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setShowRemoveAllModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.clearButton]}
                  onPress={() => {
                    handleRemoveAllPlants();
                    setShowRemoveAllModal(false);
                  }}
                >
                  <Text style={styles.clearButtonText}>OK</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2E7D32', // Grass green
  },
  safeArea: {
    backgroundColor: '#2E7D32', // Grass green
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  addButtonContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  addButtonText: {
    marginLeft: 8,
    color: '#2E7D32',
    fontSize: 14,
    fontWeight: '500',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 80,
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: '#E8F5E9', // Light green background
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9', // Slightly darker light green border
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#2E7D32', // Grass green text
  },
  cardDescription: {
    fontSize: 14,
    color: '#4CAF50', // Medium green text
  },
  plantCard: {
    backgroundColor: '#F1F8E9',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  plantCardContent: {
    flexDirection: 'row',
    gap: 16,
  },
  plantThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  plantInfo: {
    flex: 1,
    gap: 8,
  },
  plantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  plantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  plantDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#4CAF50',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  clearButton: {
    padding: 8,
    backgroundColor: '#FF5252',
    borderWidth: 1,
    borderColor: '#FF5252',
  },
  deleteButtonContainer: {
    padding: 4,
    cursor: 'pointer',
  },
  hero: {
    height: 200,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  removeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#FFCDD2',
  },
  removeAllText: {
    color: '#FF5252',
    fontSize: 16,
    fontWeight: '500',
  },
  alertOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertContent: {
    backgroundColor: '#fff',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    width: '80%',
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  alertEmoji: {
    fontSize: 48,
    marginBottom: 16,
  },
  alertTitle: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  alertMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateIcon: {
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
}); 