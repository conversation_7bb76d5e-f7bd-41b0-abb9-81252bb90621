import { StyleSheet, Text, View, TouchableOpacity, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';

interface FooterProps {
  currentScreen?: 'home' | 'plants' | 'profile' | 'settings' | 'diagnose';
  plantCount?: number;
}

export default function Footer({ currentScreen = 'home', plantCount = 0 }: FooterProps) {
  return (
    <View style={styles.footer}>
      <Link href="/home" asChild>
        <TouchableOpacity style={styles.footerButton}>
          <Ionicons name="home" size={24} color={currentScreen === 'home' ? '#4CAF50' : '#666'} />
          <Text style={[styles.footerButtonText, currentScreen === 'home' && styles.activeText]}>Home</Text>
        </TouchableOpacity>
      </Link>
      
      <Link href="/plants" asChild>
        <TouchableOpacity style={styles.footerButton}>
          <Ionicons name="leaf" size={24} color={currentScreen === 'plants' ? '#4CAF50' : '#666'} />
          <Text style={[styles.footerButtonText, currentScreen === 'plants' && styles.activeText]}>
            {plantCount} {plantCount === 1 ? 'Plant' : 'Plants'}
          </Text>
        </TouchableOpacity>
      </Link>
      
      <Link href="/profile" asChild>
        <TouchableOpacity style={styles.footerButton}>
          <Ionicons name="person" size={24} color={currentScreen === 'profile' ? '#4CAF50' : '#666'} />
          <Text style={[styles.footerButtonText, currentScreen === 'profile' && styles.activeText]}>Profile</Text>
        </TouchableOpacity>
      </Link>
      
      <Link href="/settings" asChild>
        <TouchableOpacity style={styles.footerButton}>
          <Ionicons name="settings" size={24} color={currentScreen === 'settings' ? '#4CAF50' : '#666'} />
          <Text style={[styles.footerButtonText, currentScreen === 'settings' && styles.activeText]}>Settings</Text>
        </TouchableOpacity>
      </Link>
    </View>
  );
}

const styles = StyleSheet.create({
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingBottom: Platform.OS === 'ios' ? 30 : 12, // Add extra padding for iOS to account for home indicator
    borderTopWidth: 1,
    borderTopColor: '#E8F5E9', // Light green border
    position: 'absolute',
    bottom: 10, // Position it 10px from the bottom
    left: 0,
    right: 0,
  },
  footerButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerButtonText: {
    fontSize: 12,
    marginTop: 4,
    color: '#666',
  },
  activeText: {
    color: '#4CAF50', // Medium green for active text
  },
}); 