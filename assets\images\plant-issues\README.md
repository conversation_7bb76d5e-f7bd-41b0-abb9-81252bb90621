# Plant Issue Images

This directory contains images for various plant issues. Each plant issue has its own subdirectory with multiple images.

## Directory Structure

```
plant-issues/
├── overwatering/
│   ├── overwatering-1.jpg
│   ├── overwatering-2.jpg
│   └── overwatering-3.jpg
├── light/
│   ├── light-burn-1.jpg
│   ├── light-burn-2.jpg
│   └── light-burn-3.jpg
├── nutrients/
│   ├── nutrient-deficiency-1.jpg
│   ├── nutrient-deficiency-2.jpg
│   └── nutrient-deficiency-3.jpg
├── pests/
│   ├── pests-1.jpg
│   ├── pests-2.jpg
│   └── pests-3.jpg
└── [other-plant-issues]/
    ├── [issue-name]-1.jpg
    ├── [issue-name]-2.jpg
    └── [issue-name]-3.jpg
```

## Adding Images for New Plant Issues

1. Create a new directory for the plant issue using the issue ID as the directory name
2. Add at least 3 images for the plant issue, named as `[issue-id]-1.jpg`, `[issue-id]-2.jpg`, etc.
3. Update the `PLANT_ISSUES` object in `app/plant-issue/[id].tsx` to include the local image paths:

```typescript
'issue-id': {
  // ... other properties
  images: [
    require('../../assets/images/plant-issues/issue-id/issue-id-1.jpg'),
    require('../../assets/images/plant-issues/issue-id/issue-id-2.jpg'),
    require('../../assets/images/plant-issues/issue-id/issue-id-3.jpg')
  ],
  // ... other properties
}
```

## Image Guidelines

- Use high-quality images that clearly show the plant issue
- Recommended image size: 800x600 pixels or larger
- Use JPG format for photographs
- Keep file sizes reasonable (under 500KB per image)
- Ensure images are properly licensed for use in the app 