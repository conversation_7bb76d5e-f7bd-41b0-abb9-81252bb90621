import React, { useState, useEffect } from 'react';
import { View, Text, Button, TextInput, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Environment {
  id: string;
  name: string;
  description: string;
}

// Using a simpler storage key
const STORAGE_KEY = 'environments_test';

export default function EnvironmentTest() {
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [newName, setNewName] = useState('');
  const [newDescription, setNewDescription] = useState('');
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [buttonPressed, setButtonPressed] = useState(false);

  // Load environments when component mounts
  useEffect(() => {
    loadEnvironments();
  }, []);

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => prev + '\n' + info);
    console.log(info);
  };

  const loadEnvironments = async () => {
    try {
      addDebugInfo('Loading environments...');
      const storedEnvironments = await AsyncStorage.getItem(STORAGE_KEY);
      addDebugInfo(`Stored data: ${storedEnvironments || 'null'}`);
      
      if (storedEnvironments) {
        const parsedEnvironments = JSON.parse(storedEnvironments);
        addDebugInfo(`Parsed environments: ${JSON.stringify(parsedEnvironments)}`);
        setEnvironments(parsedEnvironments);
      } else {
        addDebugInfo('No stored environments found');
        setEnvironments([]);
      }
    } catch (error) {
      addDebugInfo(`Error loading: ${error}`);
      setEnvironments([]);
    }
  };

  const handleSavePress = async () => {
    setButtonPressed(true);
    addDebugInfo('Save button pressed');
    
    if (!newName || !newDescription) {
      Alert.alert('Error', 'Please enter both name and description');
      return;
    }

    try {
      const newEnvironment: Environment = {
        id: Date.now().toString(),
        name: newName,
        description: newDescription
      };

      addDebugInfo(`Creating new environment: ${JSON.stringify(newEnvironment)}`);
      
      const updatedEnvironments = [...environments, newEnvironment];
      addDebugInfo(`Updated environments: ${JSON.stringify(updatedEnvironments)}`);
      
      // Save to AsyncStorage first
      const jsonValue = JSON.stringify(updatedEnvironments);
      addDebugInfo(`Saving to AsyncStorage: ${jsonValue}`);
      
      await AsyncStorage.setItem(STORAGE_KEY, jsonValue);
      addDebugInfo('Saved to AsyncStorage');
      
      // Then update state
      setEnvironments(updatedEnvironments);
      addDebugInfo('Updated state');
      
      // Clear input fields
      setNewName('');
      setNewDescription('');
      
      // Verify the save
      const savedData = await AsyncStorage.getItem(STORAGE_KEY);
      addDebugInfo(`Verification - Read back from AsyncStorage: ${savedData || 'null'}`);
      
      Alert.alert('Success', 'Environment saved successfully');
    } catch (error) {
      addDebugInfo(`Error saving: ${error}`);
      Alert.alert('Error', 'Failed to save environment');
    }
  };

  const handleClearPress = async () => {
    setButtonPressed(true);
    addDebugInfo('Clear button pressed');
    
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
      setEnvironments([]);
      addDebugInfo('Environments cleared');
      Alert.alert('Success', 'Environments cleared successfully');
    } catch (error) {
      addDebugInfo(`Error clearing: ${error}`);
      Alert.alert('Error', 'Failed to clear environments');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Environment Test</Text>
      
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Name:</Text>
        <TextInput
          style={styles.input}
          value={newName}
          onChangeText={setNewName}
          placeholder="Enter environment name"
        />
      </View>
      
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Description:</Text>
        <TextInput
          style={styles.input}
          value={newDescription}
          onChangeText={setNewDescription}
          placeholder="Enter environment description"
        />
      </View>
      
      <TouchableOpacity 
        style={[styles.button, buttonPressed && styles.buttonPressed]} 
        onPress={handleSavePress}
      >
        <Text style={styles.buttonText}>Save Environment</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={[styles.button, styles.clearButton, buttonPressed && styles.buttonPressed]} 
        onPress={handleClearPress}
      >
        <Text style={styles.buttonText}>Clear Environments</Text>
      </TouchableOpacity>
      
      <Text style={styles.subtitle}>Saved Environments:</Text>
      {environments.length === 0 ? (
        <Text style={styles.emptyText}>No environments saved</Text>
      ) : (
        environments.map(env => (
          <View key={env.id} style={styles.environmentItem}>
            <Text style={styles.environmentName}>{env.name}</Text>
            <Text style={styles.environmentDescription}>{env.description}</Text>
          </View>
        ))
      )}

      <Text style={styles.subtitle}>Debug Info:</Text>
      <Text style={styles.debugText}>{debugInfo}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#2E7D32',
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#2E7D32',
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
  },
  button: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: '#f44336',
  },
  buttonPressed: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  environmentItem: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
  },
  environmentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  environmentDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
    marginTop: 10,
  },
}); 